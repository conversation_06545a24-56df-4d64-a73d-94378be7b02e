import 'package:railops/models/index.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/profile_types/profile_response.dart';
import 'package:railops/types/profile_types/request_profile.dart';

class LocationService {
  static Future<String> addCurrentUserLocation(
      final String token, String latitude, String longitude,) async {
    try {
      final responseJson = await ApiService.post(
        '/location/add_current_user_location/',
        {'token': token, 'latitude': latitude, 'longitude': longitude},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> saveTrainLocation(
      final String token, String latitude, String longitude,) async {
    try {
      final responseJson = await ApiService.post(
        '/location/save_train_location/',
        {'token': token, 'latitude': latitude, 'longitude': longitude},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<Map<String, dynamic>> getNearestStation( String latitude, String longitude) async {
    try {
      final responseJson = await ApiService.get(
        '/microservice/station/nearest/?latitude=${latitude}&longitude=${longitude}' , {}
      );
      return responseJson;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }
}