import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';
import 'package:railops/services/notification_services/onboarding_notification_service.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/models/onboarding_notification_model.dart';

/// Phase 3.2: Background notification processing service
/// Integrates with existing WorkManager setup to handle notifications when app is in background
class NotificationBackgroundService {
  // Task identifiers for WorkManager
  static const String notificationProcessingTask = "notificationProcessingTask";
  static const String proximityMonitoringTask = "proximityMonitoringTask";

  // Shared preferences keys for background data storage
  static const String _keyTrainData = 'background_train_data';
  static const String _keyNotificationConfig = 'background_notification_config';
  static const String _keyLastProcessedLocation = 'last_processed_location';
  static const String _keyProximityThresholds = 'proximity_thresholds';

  // Default configuration values
  static const Duration _defaultProcessingInterval = Duration(minutes: 10);
  static const double _defaultProximityThreshold = 2.0; // km
  static const int _maxRetryAttempts = 3;

  static final OnboardingNotificationService _notificationService =
      OnboardingNotificationService();

  /// Initialize background notification processing
  static Future<void> initialize() async {
    try {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Initializing background notification processing');
      }

      // Background processing is initialized through the existing WorkManager setup
      // in lib/utils/fetch_location.dart - we extend the existing callbackDispatcher
    } catch (e) {
      if (kDebugMode) {
        print('NotificationBackgroundService: Error during initialization: $e');
      }
    }
  }

  /// Start background notification processing for a train journey
  static Future<void> startBackgroundProcessing({
    required UpcomingStationResponse trainData,
    required String currentLat,
    required String currentLng,
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableProximityAlerts = true,
    double proximityThresholdKm = 2.0,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store train data for background processing
      final trainDataJson = {
        'train_data': trainData.toJson(),
        'current_lat': currentLat,
        'current_lng': currentLng,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await prefs.setString(_keyTrainData, json.encode(trainDataJson));

      // Store notification configuration
      final configJson = {
        'enable_boarding_notifications': enableBoardingNotifications,
        'enable_offboarding_notifications': enableOffBoardingNotifications,
        'enable_proximity_alerts': enableProximityAlerts,
        'proximity_threshold_km': proximityThresholdKm,
      };

      await prefs.setString(_keyNotificationConfig, json.encode(configJson));

      // Register background tasks with WorkManager
      await _registerBackgroundTasks();

      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Started background processing for train ${trainData.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error starting background processing: $e');
      }
    }
  }

  /// Stop background notification processing
  static Future<void> stopBackgroundProcessing() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear stored data
      await prefs.remove(_keyTrainData);
      await prefs.remove(_keyNotificationConfig);
      await prefs.remove(_keyLastProcessedLocation);
      await prefs.remove(_keyProximityThresholds);

      // Cancel specific notification tasks (location tracking continues for other purposes)
      await Workmanager().cancelByUniqueName(notificationProcessingTask);
      await Workmanager().cancelByUniqueName(proximityMonitoringTask);

      if (kDebugMode) {
        print('NotificationBackgroundService: Stopped background processing');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error stopping background processing: $e');
      }
    }
  }

  /// Register background tasks with WorkManager
  static Future<void> _registerBackgroundTasks() async {
    try {
      // Register periodic notification processing task
      await Workmanager().registerPeriodicTask(
        notificationProcessingTask,
        notificationProcessingTask,
        frequency: _defaultProcessingInterval,
        existingWorkPolicy: ExistingWorkPolicy.replace,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        backoffPolicy: BackoffPolicy.exponential,
        backoffPolicyDelay: const Duration(minutes: 2),
      );

      // Register proximity monitoring task (more frequent)
      await Workmanager().registerPeriodicTask(
        proximityMonitoringTask,
        proximityMonitoringTask,
        frequency: const Duration(minutes: 5),
        existingWorkPolicy: ExistingWorkPolicy.replace,
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        backoffPolicy: BackoffPolicy.exponential,
        backoffPolicyDelay: const Duration(minutes: 1),
      );
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error registering background tasks: $e');
      }
    }
  }

  /// Process background notifications (called by WorkManager)
  static Future<bool> processBackgroundNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if background processing is enabled
      final trainDataString = prefs.getString(_keyTrainData);
      final configString = prefs.getString(_keyNotificationConfig);

      if (trainDataString == null || configString == null) {
        if (kDebugMode) {
          print(
              'NotificationBackgroundService: No background data found, skipping processing');
        }
        return true; // Not an error, just no data to process
      }

      final trainDataJson = json.decode(trainDataString);
      final configJson = json.decode(configString);

      // Check if data is still valid (not too old)
      final timestamp = trainDataJson['timestamp'] as int;
      final dataAge = DateTime.now().millisecondsSinceEpoch - timestamp;
      final maxDataAge = const Duration(hours: 6).inMilliseconds;

      if (dataAge > maxDataAge) {
        if (kDebugMode) {
          print(
              'NotificationBackgroundService: Train data too old, stopping background processing');
        }
        await stopBackgroundProcessing();
        return true;
      }

      // Get current location
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );

      // Process location-based notifications
      await _processLocationBasedNotifications(
        trainDataJson,
        configJson,
        position,
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error in background processing: $e');
      }
      return false;
    }
  }

  /// Process proximity monitoring (called by WorkManager)
  static Future<bool> processProximityMonitoring() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if proximity monitoring is enabled
      final configString = prefs.getString(_keyNotificationConfig);
      if (configString == null) {
        return true; // No configuration, skip
      }

      final configJson = json.decode(configString);
      if (!(configJson['enable_proximity_alerts'] as bool? ?? false)) {
        return true; // Proximity alerts disabled
      }

      // Get current location
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );

      // Check proximity to stations
      await _checkStationProximity(position, configJson);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error in proximity monitoring: $e');
      }
      return false;
    }
  }

  /// Process location-based notifications in background
  static Future<void> _processLocationBasedNotifications(
    Map<String, dynamic> trainDataJson,
    Map<String, dynamic> configJson,
    Position currentPosition,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get last processed location to avoid duplicate processing
      final lastLocationString = prefs.getString(_keyLastProcessedLocation);
      Position? lastPosition;

      if (lastLocationString != null) {
        final lastLocationJson = json.decode(lastLocationString);
        lastPosition = Position(
          latitude: lastLocationJson['latitude'],
          longitude: lastLocationJson['longitude'],
          timestamp: DateTime.fromMillisecondsSinceEpoch(
              lastLocationJson['timestamp']),
          accuracy: lastLocationJson['accuracy'] ?? 0.0,
          altitude: lastLocationJson['altitude'] ?? 0.0,
          heading: lastLocationJson['heading'] ?? 0.0,
          speed: lastLocationJson['speed'] ?? 0.0,
          speedAccuracy: lastLocationJson['speed_accuracy'] ?? 0.0,
          altitudeAccuracy: lastLocationJson['altitude_accuracy'] ?? 0.0,
          headingAccuracy: lastLocationJson['heading_accuracy'] ?? 0.0,
        );
      }

      // Check if location has changed significantly
      if (lastPosition != null) {
        final distance = Geolocator.distanceBetween(
          lastPosition.latitude,
          lastPosition.longitude,
          currentPosition.latitude,
          currentPosition.longitude,
        );

        // Skip processing if moved less than 100 meters
        if (distance < 100) {
          return;
        }
      }

      // Store current location as last processed
      final currentLocationJson = {
        'latitude': currentPosition.latitude,
        'longitude': currentPosition.longitude,
        'timestamp': currentPosition.timestamp.millisecondsSinceEpoch,
        'accuracy': currentPosition.accuracy,
        'altitude': currentPosition.altitude,
        'heading': currentPosition.heading,
        'speed': currentPosition.speed,
        'speed_accuracy': currentPosition.speedAccuracy,
        'altitude_accuracy': currentPosition.altitudeAccuracy,
        'heading_accuracy': currentPosition.headingAccuracy,
      };

      await prefs.setString(
          _keyLastProcessedLocation, json.encode(currentLocationJson));

      // Reconstruct train data
      final trainData =
          UpcomingStationResponse.fromJson(trainDataJson['train_data']);

      // Process notifications based on current location and train data
      await _processTrainLocationNotifications(
        trainData,
        currentPosition,
        configJson,
      );

      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Processed location-based notifications for train ${trainData.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error processing location-based notifications: $e');
      }
    }
  }

  /// Process train location notifications based on current position
  static Future<void> _processTrainLocationNotifications(
    UpcomingStationResponse trainData,
    Position currentPosition,
    Map<String, dynamic> configJson,
  ) async {
    try {
      // Check if we should process boarding/off-boarding notifications
      final enableBoarding =
          configJson['enable_boarding_notifications'] as bool? ?? true;
      final enableOffBoarding =
          configJson['enable_offboarding_notifications'] as bool? ?? true;

      if (enableBoarding || enableOffBoarding) {
        // Process notifications using the existing service
        await _notificationService.processUpcomingStationResponse(
          trainData,
          enableBoardingNotifications: enableBoarding,
          enableOffBoardingNotifications: enableOffBoarding,
          enableStationApproachingNotifications:
              false, // Handled separately by proximity monitoring
        );
      }

      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Processed train location notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error processing train location notifications: $e');
      }
    }
  }

  /// Check proximity to stations and trigger alerts
  static Future<void> _checkStationProximity(
    Position currentPosition,
    Map<String, dynamic> configJson,
  ) async {
    try {
      final proximityThreshold =
          configJson['proximity_threshold_km'] as double? ??
              _defaultProximityThreshold;

      // Get nearest station information
      final nearestStationData = await LocationService.getNearestStation(
        currentPosition.latitude.toString(),
        currentPosition.longitude.toString(),
      );

      if (nearestStationData.isNotEmpty) {
        // Check if we're within the proximity threshold
        final stationName =
            nearestStationData['name'] as String? ?? 'Unknown Station';
        final stationLat = nearestStationData['latitude'] as double?;
        final stationLng = nearestStationData['longitude'] as double?;

        if (stationLat != null && stationLng != null) {
          final distance = Geolocator.distanceBetween(
                currentPosition.latitude,
                currentPosition.longitude,
                stationLat,
                stationLng,
              ) /
              1000; // Convert to kilometers

          if (distance <= proximityThreshold) {
            await _triggerProximityAlert(stationName, distance);
          }
        }
      }

      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Completed station proximity check');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error checking station proximity: $e');
      }
    }
  }

  /// Trigger proximity alert notification
  static Future<void> _triggerProximityAlert(
      String stationName, double distanceKm) async {
    try {
      await _notificationService.triggerImmediateNotification(
        title: 'Approaching $stationName',
        body:
            'You are ${distanceKm.toStringAsFixed(1)}km away from $stationName',
        type: OnboardingNotificationType.proximityAlert,
        additionalData: {
          'notification_category': 'proximity_alert',
          'station_name': stationName,
          'distance_km': distanceKm,
          'triggered_by': 'background_service',
        },
      );

      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Triggered proximity alert for $stationName (${distanceKm.toStringAsFixed(1)}km)');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'NotificationBackgroundService: Error triggering proximity alert: $e');
      }
    }
  }

  /// Get background processing status
  static Future<Map<String, dynamic>> getBackgroundProcessingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final trainDataString = prefs.getString(_keyTrainData);
      final configString = prefs.getString(_keyNotificationConfig);
      final lastLocationString = prefs.getString(_keyLastProcessedLocation);

      return {
        'is_active': trainDataString != null && configString != null,
        'has_train_data': trainDataString != null,
        'has_config': configString != null,
        'has_last_location': lastLocationString != null,
        'last_updated': lastLocationString != null
            ? json.decode(lastLocationString)['timestamp']
            : null,
      };
    } catch (e) {
      if (kDebugMode) {
        print('NotificationBackgroundService: Error getting status: $e');
      }
      return {
        'is_active': false,
        'error': e.toString(),
      };
    }
  }
}
