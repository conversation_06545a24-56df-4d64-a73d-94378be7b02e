import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/user_types/user_info.dart';

class UserService {
  static Future<dynamic> enableDisableUser(String token, String page) async {
    String endpoint = "/api/users/enable_disable_user_info/?page=$page";
    try {
      final response = await ApiService.get(endpoint, {'token': token});
      return response;
    } catch (error) {
      return {'success': false, 'message': error.toString()};
    }
  }

  static Future<dynamic> updateUserStatus(
      String token, String username, String status) async {
    const String endpoint = "/api/users/enable_disable_user/";

    try {
      final response = await ApiService.post(
        endpoint,
        {
          'token': token,
          'username': username,
          'status': status,
        },
      );
      return response;
    } catch (error) {
      print('Error in updateUserStatus: $error');
      return {'success': false, 'message': error.toString()};
    }
  }

  static Future<List<UserInfo>> getUserInfo(String token) async {
    try {
      String endpoint = "/api/users/get_user_data/";
      final response = await ApiService.get(endpoint, {
        'token': token,
      });

      // Convert the JSON response to a List<UserInfo>
      if (response is List) {
        return response.map((json) => UserInfo.fromJson(json)).toList();
      } else if (response is Map<String, dynamic>) {
        // Check if there's a data array in the response
        if (response.containsKey('data') && response['data'] is List) {
          List<dynamic> data = response['data'];
          return data.map((json) => UserInfo.fromJson(json)).toList();
        } else {
          // If response is a single object, return it as a list with one item
          return [UserInfo.fromJson(response)];
        }
      }

      // If we get here, something is wrong with the response format
      print("Unexpected response format: $response");
      return [];
    } catch (e) {
      throw Exception('Error fetching users: $e');
    }
  }

  static Future<dynamic> getUserByPartialUsername(String token,
      {required String username}) async {
    try {
      String endpoint = "/api/users/get-user-by-username/";
      final response = await ApiService.post(
        endpoint,
        {
          'token': token,
          'username': username,
        },
      );
      return response;
    } catch (e) {
      throw Exception('Error searching users: $e');
    }
  }
}
