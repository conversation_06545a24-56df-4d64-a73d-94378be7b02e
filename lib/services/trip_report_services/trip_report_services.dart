import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:railops/constants/index.dart';
import 'package:http/http.dart' as http;
import 'package:railops/services/assign_ehk_ca_services/jobchart_image_upload.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:railops/types/trip_report_type/trip_report_type.dart';

class TripReportServices {
  static Future<Map<String, dynamic>> getTripReport(
      String trainNumber, String date) async {
    try {
      final responseJson = await ApiService.get(
          '/report/journey-reports/$trainNumber/$date', {});
      // return List<String>.from(responseJson['coaches']);
      print("fetching report");
      return responseJson;
    } on ApiException catch (e) {
      print("error fetching report");
      throw e.message;
    } catch (e) {
      return {};
    }
  }

  static Future<List<dynamic>> getAllIssues() async {
    try {
      final responseJson = await ApiService.get('/report/issues/', {});
      // return List<String>.from(responseJson['coaches']);;
      print("fetching issues");
      return responseJson;
    } on ApiException catch (e) {
      print("error fetching issues");
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> addIssues(
    String name,
    String token,
  ) async {
    try {
      final responseJson = await ApiService.post(
          '/report/issues/add/', {"name": name, 'token': token});
      // return List<String>.from(responseJson['coaches']);;
      print("adding issues");
      return responseJson;
    } on ApiException catch (e) {
      print("error adding issues");
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> updateIssue(
    String name,
    int issueId,
    String token,
  ) async {
    try {
      final responseJson = await ApiService.put(
          '/report/issues/update/$issueId/', {'name': name, 'token': token});
      // return List<String>.from(responseJson['coaches']);;
      print("updating issues");
      return responseJson;
    } on ApiException catch (e) {
      print("error updating issues");
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> addSubIssue(
    int issueId,
    String name,
    String token,
  ) async {
    try {
      final responseJson = await ApiService.post('/report/subissues/add/',
          {'name': name, 'issue_id': issueId, 'token': token});
      // return List<String>.from(responseJson['coaches']);;
      print("adding sub issues");
      return responseJson;
    } on ApiException catch (e) {
      print("error adding sub issues");
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> updateSubIssue(
    int subIssueId,
    String name,
    String token,
  ) async {
    try {
      final responseJson = await ApiService.post(
          '/report/subissues/update/$subIssueId/',
          {'name': name, "token": token});
      // return List<String>.from(responseJson['coaches']);;
      print("updating sub issues");
      return responseJson;
    } on ApiException catch (e) {
      print("error updaing sub issues");
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> deleteIssue(
      int issueId, String token) async {
    try {
      final responseJson = await ApiService.delete(
        '/report/issues/delete/$issueId/',
        {'token': token},
      );
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> deleteSubIssue(
      int subIssueId, String token) async {
    try {
      final responseJson = await ApiService.delete(
        '/report/subissues/delete/$subIssueId/',
        {'token': token},
      );
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<Map<String, dynamic>> getIssueStatus(
      String trainNumber, String date, String coach, String token) async {
    try {
      final responseJson = await ApiService.get(
          '/report/journey-status/?train=$trainNumber&date=$date&coach=$coach',
          {});
      print(responseJson);
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Failed to save coach issues: $e");
      throw "Failed to save coach issues: $e";
    }
  }

  static Future<Map<String, dynamic>> saveCoachIssues(
      String trainNumber,
      String date,
      String coach,
      List<int> selectedIssueIds,
      List<int> selectedSubIssueIds,
      String createdBy,
      String token) async {
    try {
      final payload = {
        "train": trainNumber,
        "origin_date": date,
        "coach": coach,
        "selected_issue_ids": selectedIssueIds,
        "selected_subissue_ids": selectedSubIssueIds,
        "created_by": createdBy,
        "token": token
      };
      print("trip report payload: $payload");
      final responseJson = await ApiService.post(
          '/report/journey-reports/create-or-update/', payload);
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Failed to save coach issues: $e");
      throw "Failed to save coach issues: $e";
    }
  }

  static Future<Map<String, dynamic>> updateCoachIssue(
    int issueId,
    String type,
    String typevalue,
    String at,
    String token,
  ) async {
    try {
      final payload = {
        "${type}_by": typevalue,
        "${type}_at": at,
        "token": token
      };
      print("trip report payload: $payload");
      final responseJson = await ApiService.put(
          '/report/coachissue/${issueId}/update/', payload);
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Failed to update coach issues: $e");
      throw "Failed to update coach issues: $e";
    }
  }

  static Future<Map<String, dynamic>> updateCoachStatus(
    int issueId,
    int subIssueId,
    String date,
    String coach,
    String trainNumber,
    int statusId,
    String type,
    String typevalue,
    String at,
    String updated_by,
    String token,
    bool revert,
  ) async {
    try {
      final payload = {
        "${type}_by": typevalue,
        "${type}_at": at,
        "updated_by": updated_by,
        "train": trainNumber,
        "date": date,
        "coach": coach,
        "issue_id": issueId,
        "subissue_id": subIssueId,
        "status": statusId,
        "revert": revert,
        "token": token
      };
      print("trip report payload: $payload");
      final responseJson =
          await ApiService.post('/report/journey-status/', payload);
      return responseJson;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Failed to update coach issues: $e");
      throw "Failed to update coach issues: $e";
    }
  }

  static Future<Map<String, dynamic>?> createTripImageReport(
      {required List<XFile> files,
      required String trainNumber,
      required String date,
      required String token,
      required String latitude,
      required String longitude,
      required String coach,
      String? comment,
      String? issues,
      String? subissues}) async {
    try {
      final endpoint = '/report/upload-journey-report-image/';

      var request = http.MultipartRequest(
          'POST', Uri.parse(ApiConstant.baseUrl + endpoint));
      request.headers.addAll({
        'Authorization': 'Bearer $token',
      });
      print("selected issues: $issues");
      print("selected subissues: $subissues");
      // Uint8List bytes = await imageFiles.readAsBytes();
      // String filename = imageFile.name;

      // var file = http.MultipartFile(
      //   'file',
      //   http.ByteStream.fromBytes(bytes),
      //   bytes.length,
      //   filename: filename,
      // );

      // Add multiple files with 'files' field name
      for (var file in files) {
        final fileBytes = await file.readAsBytes();
        request.files.add(http.MultipartFile.fromBytes(
          'files',
          fileBytes,
          filename: file.name.split('/').last,
        ));
      }

      request.fields['train_number'] = trainNumber;
      request.fields['date'] = date;
      request.fields['latitude'] = latitude;
      request.fields['longitude'] = longitude;
      request.fields['coach'] = coach;
      request.fields['issues'] = issues!;
      request.fields['subissues'] = subissues!;
      request.fields['comment'] = comment ?? "";

      var response = await request.send();

      if (response.statusCode == 200) {
        var responseData = await response.stream.bytesToString();
        return json.decode(responseData);
      }
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      rethrow;
    }
  }

  static Future<List<TripReportType>?> fetchTripReportImages({
    String? date,
    String? trainNumber,
    String? token,
    String? coach,
  }) async {
    try {
      final endpoint =
          "/report/get-journey-report-images/?train_number=$trainNumber&date=$date&coach=$coach";

      final body = <String, String>{};
      body["token"] = token!;

      final responseJson = await ApiService.get(endpoint, body);
      if (responseJson != null && responseJson is List) {
        return responseJson
            .map((json) => TripReportType.fromJson(json))
            .toList();
      }
      return null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  static Future<Map<String, dynamic>?> deleteJourneyReportMedia({
    required int fileId,
    required String mediaUrl,
    required String token,
  }) async {
    try {
      final endpoint = "/report/delete-journey-report-media/";

      final body = {
        'report_id': fileId,
        'media_url': mediaUrl,
        'token': token,
      };

      final response = await ApiService.post(endpoint, body);

      return response;
    } catch (e) {
      throw Exception('Error deleting media: ${e.toString()}');
    }
  }
}
