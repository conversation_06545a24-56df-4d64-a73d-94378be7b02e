import 'dart:convert';
import 'dart:io' as io;
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/rail_sathi_types/complain_type.dart';
import 'package:intl/intl.dart';

class ComplainServices {
  static Future<Map<String, dynamic>?> addComplain({
    required String token,
    required String pnrNumber,
    required String isPnrValidated, // Updated to take the new status format
    required String name,
    required String mobileNumber,
    required String complainType,
    required String complainDescription,
    required String complainStatus,
    int? train, 
    String? trainNumber, 
    String? trainName, 
    required String coach,
    required int berthNo,
    required List<io.File> mediaFiles,
    required List<Uint8List> mediaBytes,
    required String complainDate,
  }) async {
    var uri = Uri.parse('${ApiConstant.baseUrl}rail-sathi/add/');
    
    try {
      var request = http.MultipartRequest('POST', uri);
      request.headers.addAll({
        'Authorization': 'Bearer $token',
      });

      // Add text fields
      request.fields['pnr_number'] = pnrNumber;
      request.fields['is_pnr_validated'] = isPnrValidated;
      request.fields['name'] = name;
      request.fields['mobile_number'] = mobileNumber;
      request.fields['complain_type'] = complainType;
      request.fields['complain_description'] = complainDescription;
      request.fields['complain_status'] = complainStatus;
      request.fields['coach'] = coach;
      request.fields['berth_no'] = berthNo.toString();
      request.fields['complain_date'] = convertToISO(complainDate);
      
      // Add train information based on selection type
      if (train != null && train > 0) {
        request.fields['train'] = train.toString();
      }
      if (trainNumber != null && trainNumber.isNotEmpty) {
        request.fields['train_number'] = trainNumber;
      }
      if (trainName != null && trainName.isNotEmpty) {
        request.fields['train_name'] = trainName;
      }

      // Add media files
      if (kIsWeb) {
        // Web implementation
        for (int i = 0; i < mediaBytes.length; i++) {
          final bytes = mediaBytes[i];
          final fileName = 'media_$i.jpg'; // Default to jpg
          
          request.files.add(
            http.MultipartFile.fromBytes(
              'rail_sathi_complain_media_files',
              bytes,
              filename: fileName,
              contentType: MediaType('image', 'jpeg'),
            ),
          );
        }
      } else {
        // Mobile implementation
        for (int i = 0; i < mediaFiles.length; i++) {
          final file = mediaFiles[i];
          final fileName = file.path.split('/').last;
          final extension = fileName.split('.').last.toLowerCase();
          
          String contentType = 'image/jpeg'; // Default
          if (extension == 'png') {
            contentType = 'image/png';
          } else if (extension == 'mp4') {
            contentType = 'video/mp4';
          }
          
          request.files.add(
            await http.MultipartFile.fromPath(
              'rail_sathi_complain_media_files',
              file.path,
              contentType: MediaType(
                contentType.split('/')[0],
                contentType.split('/')[1],
              ),
            ),
          );
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 201 || response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        print('Failed to submit complaint: ${response.body}');
        return null;
      }
    } catch (e) {
      print('Exception while submitting complaint: $e');
      return null;
    }
  }

  static convertToISO(String inputDate) {
    try {
      final originalFormat = DateFormat('dd-MM-yyyy');
      final targetFormat = DateFormat('yyyy-MM-dd');
      final dateTime = originalFormat.parse(inputDate);
      return targetFormat.format(dateTime);
    } catch (e) {
      return ''; 
    }
  }

  static Future<bool> updateComplaint({
    required String token,
    required int complainId,
    String? pnrNumber,
    bool? isPnrValidated,
    String? name,
    String? mobileNumber,
    String? complainType,
    String? complainDescription,
    String? complainStatus,
    String? trainNumber,
    String? coach,
    int? berthNo,
    List<io.File>? mediaFiles,
    List<Uint8List>? mediaBytes,
    List<String>? mediaNames,
    int? train
  }) async {
    try {
      final uri = Uri.parse('${ApiConstant.baseUrl}/rail-sathi/update/$complainId');
      final request = http.MultipartRequest('PATCH', uri)
        ..headers['Authorization'] = 'Bearer $token';

      // Add non-null fields to the request
      if (pnrNumber != null) request.fields['pnr_number'] = pnrNumber;
      if (isPnrValidated != null) {
        request.fields['is_pnr_validated'] = isPnrValidated.toString();
      }
      if (name != null) request.fields['name'] = name;
      if (mobileNumber != null) request.fields['mobile_number'] = mobileNumber;
      if (complainType != null) request.fields['complain_type'] = complainType;
      if (complainDescription != null) {
        request.fields['complain_description'] = complainDescription;
      }
      if (complainStatus != null) request.fields['complain_status'] = complainStatus;
      if (trainNumber != null) request.fields['train_number_input'] = trainNumber.toString();
      if (coach != null) request.fields['coach'] = coach;
      if (berthNo != null) request.fields['berth_no'] = berthNo.toString();

      // Handle media uploads
      if (mediaFiles != null) {
        for (var file in mediaFiles) {
          final fileName = file.path.split('/').last;
          request.files.add(await http.MultipartFile.fromPath(
            'rail_sathi_complain_media_files',
            file.path,
            filename: fileName,
            contentType: MediaType('image', 'jpeg'),
          ));
        }
      }

      if (mediaBytes != null) {
        for (int i = 0; i < mediaBytes.length; i++) {
          final fileName = mediaNames != null && mediaNames.length > i
              ? mediaNames[i]
              : 'media_$i.jpg';

          request.files.add(http.MultipartFile.fromBytes(
            'rail_sathi_complain_media_files',
            mediaBytes[i],
            filename: fileName,
            contentType: MediaType('image', 'jpeg'),
          ));
        }
      }

      final response = await request.send();
      return response.statusCode == 200;
    } catch (e) {
      print('Error updating complaint: $e');
      return false;
    }
  }

  static Future<List<Complain>> getComplaintsByDate({
    required String token,
    required String date,
  }) async {
    try {
      final data = await ApiService.get('rail-sathi/get/$date', {'token': token});
      return (data as List).map((json) => Complain.fromJson(json)).toList();
    } on ApiException catch (e) {
      throw Exception('Failed to load complaints: ${e.message}');
    }
  }

  static Future<bool> deleteComplaint({
    required String token,
    required int complainId,
  }) async {
    try {
      await ApiService.delete('/rail-sathi/delete/$complainId', {'token': token});
      return true;
    } on ApiException catch (e) {
      print('API Error: ${e.message}');
      return false;
    } catch (e) {
      print('Unexpected error: $e');
      return false;
    }
  }

  static Future<void> deleteComplaintImage({
    required String token,
    required int complainId,
    required List<int> mediaIds,
  }) async {
    try {
      await ApiService.delete('/rail-sathi/delete-image/$complainId', {
        'token': token,
        'deleted_media_ids': mediaIds,
      });
    } on ApiException catch (e) {
      throw Exception('Failed to delete image: ${e.message}');
    }
  }


}
