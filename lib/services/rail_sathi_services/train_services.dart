import 'package:flutter/material.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/rail_sathi_types/train_type.dart';

class RailSathiTrainService {
  static Future<List<Train>> getAllTrainsWithId() async {
    try {
      final responseJson = await ApiService.get('/api/all-trains-with-id/', {});
      List<Train> trains = List<dynamic>.from(responseJson)
          .map((item) => Train.fromJson(item))
          .toList();
      return trains;
    } on ApiException catch (e) {
      debugPrint('Error fetching trains: ${e.message}');
      throw e.message;
    } catch (e) {
      debugPrint('Error fetching trains: $e');
      return [];
    }
  }
}