import 'dart:async';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/index.dart';
import 'package:railops/types/profile_types/pnr_response.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class PnrService {
  static Future<PnrResponse?> checkPNR(
    String pnrNumber,
    
  ) async {
    try {
     
      // final response = await http.get(
      //     Uri.parse('https://railops-uat-api.biputri.com/pnr_microservice/check_pnr_status?pnr=$pnrNumber')
      //  );

      final response = await http.get(
          Uri.parse('${ApiConstant.baseUrl}/pnr_microservice/check_pnr_status?pnr=$pnrNumber'));

      //print(response.body);
      
      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);
        return PnrResponse.fromJson(jsonData);
      }

    } 
    on ApiException catch (e) {
      print('API Exception: ${e.message}');
      throw (e.message);
    } 
    catch (e) {
      return null;
    }
  }
 
}
// in case testing on your local without using pnr_microservice use below endpoint

// import 'package:railops/services/api_services/index.dart';
// import 'package:railops/types/index.dart';
// import 'package:railops/types/profile_types/pnr_response.dart';
// class PnrService {
//   static Future<PnrResponse?> checkPNR(
//     String pnrNumber,
//     String token,
//   ) async {
//     try {
//       final responseJson = 
//       await ApiService.get('/api/users/pnr-status/?pnr=$pnrNumber',{'token':token});
//           return PnrResponse.fromJson(responseJson);
//     } on ApiException catch (e) {
//       throw (e.message);
//     } catch (e) {
//        print(e);
//       return null;
//     }
//   }
// }