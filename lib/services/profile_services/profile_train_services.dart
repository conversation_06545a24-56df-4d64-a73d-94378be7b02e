import 'package:railops/services/api_services/index.dart';

class ProfileTrainServices {

  static Future<String> toggleInsideTrain(
      final String token, final bool insideTrain, final String insideTrainNumber) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/toggle_inside_train/', 
        {
          'token': token,
          'inside_train': insideTrain.toString(),
          'inside_train_number': insideTrainNumber,
        },
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw 'Error occurred while toggling inside train status';
    }
  }

  static Future<Map<String, dynamic>> getInsideTrainStatus(final String token) async {
    try {
      final responseJson = await ApiService.get(
        '/api/users/inside_train_status/', 
        {'token': token},
      );
      return {
        'inside_train': responseJson['inside_train'],
        'inside_train_number': responseJson['inside_train_number'],
        'inside_train_date':responseJson['inside_train_date']
      };
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw 'Error occurred while fetching inside train status';
    }
  }


  static Future<String> toggleNeedAlarm(final String token, final bool needAlarm) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/toggle_need_alarm/', 
        {
          'token': token,
          'need_alarm': needAlarm.toString(),
        },
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw 'Error occurred while toggling need alarm status';
    }
  }

  static Future<bool> getNeedAlarmStatus(final String token) async {
    try {
      final responseJson = await ApiService.get(
        '/api/users/need_alarm_status/', 
        {'token': token},
      );
      return responseJson['need_alarm'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw 'Error occurred while fetching need alarm status';
    }
  }
}
