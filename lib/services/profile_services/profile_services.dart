import 'package:railops/models/index.dart';
import 'package:railops/screens/profile_screen/widgets/change_whatsapp_form.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/profile_types/profile_response.dart';
import 'package:railops/types/profile_types/request_profile.dart';


class ProfileService {
   String _token = '';

  ProfileService() {
    _getToken();
  }

  Future<void> _getToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    _token = userModel.token;
  }
  
  static Future<profileResponse> getProfile(final String token) async {
    try {
      final responseJson = await ApiService.get('/api/users/profile/', {'token': token});
      return profileResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception('Error occurred while fetching profile');
    }
  }

  static Future<String> updateProfile(
    final String token,
    final RequestProfile updatedProfile,
  ) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/',
        {'token': token, ...updatedProfile.toJson()},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      return 'Error occurred while updating profile $e';
    }
  }

  static Future<String> changePassword(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change_password/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {

      throw '$e';
      
    }
  }

  static Future<String> changePasswordOTP(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-password/enter-otp/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> changePhone(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-phone/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> confirmChangePhone(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-phone/conf-otp/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> changeWhatsapp(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-whatsapp/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> confirmChangeWhatsapp(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-whatsapp/conf-otp/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> changeEmail(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-email/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<String> changeEmailOTP(final String token, final Map<String, dynamic> data) async {
    try {
      final responseJson = await ApiService.post(
        '/api/users/profile/edit-profile/change-email/enter-otp/',
        {'token': token, ...data},
      );
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      return 'Error occurred while verifying email OTP';
    }
  }
}