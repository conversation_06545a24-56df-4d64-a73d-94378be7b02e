import 'package:flutter/foundation.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/notification_services/onboarding_notification_service.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

/// Helper class to integrate OnboardingNotificationService with existing services
/// This provides a simple interface to add notification functionality to existing API calls
class NotificationIntegrationHelper {
  static final OnboardingNotificationService _notificationService =
      OnboardingNotificationService();

  /// Enhanced version of UpcomingStationService.fetchUpcomingStationDetails
  /// that automatically processes notifications after successful API response
  static Future<UpcomingStationResponse>
      fetchUpcomingStationDetailsWithNotifications({
    required String lat,
    required String lng,
    required String token,
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications =
        false, // Disabled by default to avoid spam
  }) async {
    try {
      // Call the existing API service
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: lat,
        lng: lng,
        token: token,
      );

      // Process notifications based on the response
      await _notificationService.processUpcomingStationResponse(
        response,
        enableBoardingNotifications: enableBoardingNotifications,
        enableOffBoardingNotifications: enableOffBoardingNotifications,
        enableStationApproachingNotifications:
            enableStationApproachingNotifications,
      );

      if (kDebugMode) {
        print(
            'NotificationIntegrationHelper: Successfully processed notifications for train ${response.trainNumber}');
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('NotificationIntegrationHelper: Error in enhanced fetch: $e');
      }
      rethrow; // Re-throw the original error to maintain existing error handling
    }
  }

  /// Process notifications for an existing UpcomingStationResponse
  /// Useful when you already have the response and want to trigger notifications
  static Future<void> processNotificationsForResponse(
    UpcomingStationResponse response, {
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications = false,
  }) async {
    await _notificationService.processUpcomingStationResponse(
      response,
      enableBoardingNotifications: enableBoardingNotifications,
      enableOffBoardingNotifications: enableOffBoardingNotifications,
      enableStationApproachingNotifications:
          enableStationApproachingNotifications,
    );
  }

  /// Configure notification timing settings
  /// This allows customization of when notifications are sent
  static void configureNotificationTiming({
    Duration? boardingNotificationDelay,
    Duration? offBoardingNotificationDelay,
    int? stationsBeforeAlert,
  }) {
    OnboardingNotificationService.updateNotificationConfig(
      boardingDelay: boardingNotificationDelay,
      offBoardingDelay: offBoardingNotificationDelay,
      stationsBefore: stationsBeforeAlert,
    );
  }

  // Phase 2: Enhanced configuration methods

  /// Configure proximity-based notification settings
  static void configureProximityNotifications({
    double? proximityThresholdKm,
    Duration? locationUpdateInterval,
    bool enableLocationMonitoring = true,
  }) {
    OnboardingNotificationService.updateProximityConfig(
      proximityThreshold: proximityThresholdKm,
      updateInterval: locationUpdateInterval,
      enableMonitoring: enableLocationMonitoring,
    );
  }

  /// Configure passenger count threshold notifications
  static void configurePassengerCountNotifications({
    int? passengerCountThreshold,
    bool enableBoardingCountUpdates = true,
    bool enableOffBoardingPreparation = true,
  }) {
    OnboardingNotificationService.updatePassengerCountConfig(
      threshold: passengerCountThreshold,
      enableBoardingUpdates: enableBoardingCountUpdates,
      enableOffBoardingPrep: enableOffBoardingPreparation,
    );
  }

  /// Configure train status monitoring
  static void configureTrainStatusMonitoring({
    List<String>? monitoredStatusTypes,
    Duration? statusCheckInterval,
    bool enableScheduleChangeAlerts = true,
  }) {
    OnboardingNotificationService.updateTrainStatusConfig(
      statusTypes: monitoredStatusTypes,
      checkInterval: statusCheckInterval,
      enableScheduleAlerts: enableScheduleChangeAlerts,
    );
  }

  /// Send a test notification (useful for development and testing)
  static Future<void> sendTestNotification({
    String title = 'Test Notification',
    String body = 'This is a test notification from the onboarding system',
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: title,
      body: body,
    );
  }

  // Phase 2: Enhanced helper methods

  /// Send a test proximity notification
  static Future<void> sendTestProximityNotification({
    String stationName = 'Test Station',
    double distanceKm = 2.5,
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Approaching $stationName',
      body: 'You are ${distanceKm.toStringAsFixed(1)}km away from $stationName',
      type: OnboardingNotificationType.proximityAlert,
      additionalData: {
        'notification_category': 'proximity_alert',
        'distance_km': distanceKm,
      },
    );
  }

  /// Send a test station approach alert
  static Future<void> sendTestStationApproachAlert({
    String stationName = 'Test Station',
    int minutesBeforeArrival = 5,
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Approaching $stationName',
      body: 'Next stop in $minutesBeforeArrival minutes - $stationName',
      type: OnboardingNotificationType.stationApproachAlert,
      additionalData: {
        'notification_category': 'station_approach_alert',
        'minutes_before_arrival': minutesBeforeArrival,
      },
    );
  }

  /// Send a test train status update notification
  static Future<void> sendTestTrainStatusUpdate({
    String trainNumber = 'TEST123',
    String statusType = 'delay',
    String statusMessage = 'Train delayed by 15 minutes due to signal issues',
  }) async {
    await _notificationService.triggerImmediateNotification(
      title: 'Train $trainNumber - ${statusType.toUpperCase()}',
      body: statusMessage,
      type: OnboardingNotificationType.trainStatusUpdate,
      additionalData: {
        'notification_category': 'train_status_update',
        'status_type': statusType,
        'train_number': trainNumber,
      },
    );
  }

  /// Send a test boarding count update notification
  static Future<void> sendTestBoardingCountUpdate({
    String stationName = 'Test Station',
    int currentCount = 8,
    int previousCount = 5,
    List<String> coaches = const ['A1', 'B2'],
  }) async {
    final difference = currentCount - previousCount;
    await _notificationService.triggerImmediateNotification(
      title: 'Boarding Update - $stationName',
      body:
          '+$difference passengers boarding in coaches: ${coaches.join(', ')} (Total: $currentCount)',
      type: OnboardingNotificationType.boardingCountUpdate,
      additionalData: {
        'notification_category': 'boarding_count_update',
        'current_count': currentCount,
        'previous_count': previousCount,
        'difference': difference,
        'coaches': coaches,
      },
    );
  }

  /// Get notification service statistics
  static Map<String, dynamic> getNotificationStats() {
    return {
      'processed_notifications':
          _notificationService.processedNotificationCount,
      'boarding_delay_minutes':
          OnboardingNotificationService.boardingNotificationDelay.inMinutes,
      'offboarding_delay_minutes':
          OnboardingNotificationService.offBoardingNotificationDelay.inMinutes,
      'stations_before_alert':
          OnboardingNotificationService.stationsBeforeAlert,
    };
  }

  /// Clear notification cache (useful for testing)
  static void clearNotificationCache() {
    _notificationService.clearProcessedNotifications();
  }
}

/// Example usage class showing how to integrate with existing code
class NotificationIntegrationExample {
  /// Example 1: Replace existing UpcomingStationService calls
  static Future<void> exampleReplaceExistingCall() async {
    // Instead of:
    // final response = await UpcomingStationService.fetchUpcomingStationDetails(
    //   lat: '28.6139',
    //   lng: '77.2090',
    //   token: 'user_token',
    // );

    // Use this enhanced version:
    final response = await NotificationIntegrationHelper
        .fetchUpcomingStationDetailsWithNotifications(
      lat: '28.6139',
      lng: '77.2090',
      token: 'user_token',
      enableBoardingNotifications: true,
      enableOffBoardingNotifications: true,
      enableStationApproachingNotifications: false,
    );

    // Continue with existing logic
    if (kDebugMode) {
      print('Received response for train: ${response.trainNumber}');
    }
  }

  /// Example 2: Process notifications for existing response
  static Future<void> exampleProcessExistingResponse(
      UpcomingStationResponse response) async {
    // If you already have a response and want to add notifications:
    await NotificationIntegrationHelper.processNotificationsForResponse(
      response,
      enableBoardingNotifications: true,
      enableOffBoardingNotifications: true,
    );
  }

  /// Example 3: Configure notification timing
  static void exampleConfigureNotifications() {
    // Customize when notifications are sent
    NotificationIntegrationHelper.configureNotificationTiming(
      boardingNotificationDelay:
          const Duration(minutes: 10), // 10 minutes before boarding
      offBoardingNotificationDelay:
          const Duration(minutes: 20), // 20 minutes before off-boarding
      stationsBeforeAlert: 1, // Alert 1 station before
    );
  }

  /// Example 4: Testing notifications
  static Future<void> exampleTestNotifications() async {
    // Send a test notification
    await NotificationIntegrationHelper.sendTestNotification(
      title: 'Test Alert',
      body: 'Testing the notification system',
    );

    // Check statistics
    final stats = NotificationIntegrationHelper.getNotificationStats();
    if (kDebugMode) {
      print('Notification stats: $stats');
    }
  }
}
