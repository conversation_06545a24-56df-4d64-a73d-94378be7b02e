import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/firebase_messaging_service.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

/// Central service to process API responses and trigger onboarding notifications
/// Integrates with existing FirebaseMessagingService to show contextual notifications
/// based on train station data and passenger boarding/off-boarding information
class OnboardingNotificationService {
  static final OnboardingNotificationService _instance =
      OnboardingNotificationService._internal();

  factory OnboardingNotificationService() => _instance;
  OnboardingNotificationService._internal();

  final FirebaseMessagingService _firebaseMessagingService =
      FirebaseMessagingService();

  // Track processed notifications to avoid duplicates
  final Set<String> _processedNotifications = <String>{};

  // Configuration for notification timing and behavior
  static const Duration _defaultBoardingNotificationDelay =
      Duration(minutes: 15);
  static const Duration _defaultOffBoardingNotificationDelay =
      Duration(minutes: 30);
  static const int _defaultStationsBeforeAlert = 2;

  // Phase 2: Enhanced configuration options
  static const double _defaultProximityThresholdKm = 5.0;
  static const int _defaultPassengerCountThreshold = 5;
  static const Duration _defaultStationApproachTime = Duration(minutes: 5);
  static const Duration _defaultLocationUpdateInterval = Duration(minutes: 2);

  // Phase 2: Location monitoring
  Timer? _locationMonitoringTimer;
  Position? _lastKnownPosition;
  final Map<String, double> _stationDistances = {};
  final Set<String> _triggeredProximityNotifications = {};

  // CA/CS/EHK specific: Track stations that have been notified for current journey
  final Set<String> _notifiedStationsForCurrentJourney = {};
  String? _currentJourneyId;

  // Phase 2: Train status monitoring
  String? _currentTrainNumber;
  Map<String, dynamic>? _lastTrainStatus;

  /// Process UpcomingStationResponse and trigger appropriate notifications
  /// This is the main entry point for the notification system
  /// Enhanced for CA/CS/EHK with 50km radius logic and duplicate prevention
  Future<void> processUpcomingStationResponse(
    UpcomingStationResponse response, {
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications = true,
  }) async {
    try {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Processing station response for train ${response.trainNumber}');
        print('Stations: ${response.stations}');
        print('Boarding details: ${response.details.keys}');
        print('Off-boarding details: ${response.detailsOffBoarding.keys}');
      }

      // Initialize journey tracking for CA/CS/EHK
      _initializeJourneyTracking(response);

      // Process boarding notifications
      if (enableBoardingNotifications) {
        await _processBoardingNotifications(response);
      }

      // Process off-boarding notifications
      if (enableOffBoardingNotifications) {
        await _processOffBoardingNotifications(response);
      }

      // Process station approaching notifications
      if (enableStationApproachingNotifications) {
        await _processStationApproachingNotifications(response);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error processing station response: $e');
      }
    }
  }

  /// Initialize journey tracking for CA/CS/EHK notifications
  /// This ensures notifications are sent only once per station per journey
  void _initializeJourneyTracking(UpcomingStationResponse response) {
    final journeyId = '${response.trainNumber}_${response.date}';

    // If this is a new journey, reset the notification tracking
    if (_currentJourneyId != journeyId) {
      _currentJourneyId = journeyId;
      _currentTrainNumber = response.trainNumber;
      _notifiedStationsForCurrentJourney.clear();
      _triggeredProximityNotifications.clear();

      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Started new journey tracking for $journeyId');
      }
    }
  }

  /// Process boarding notifications for stations with passenger boarding data
  /// Enhanced for CA/CS/EHK with 50km radius logic and duplicate prevention
  Future<void> _processBoardingNotifications(
      UpcomingStationResponse response) async {
    for (final stationEntry in response.details.entries) {
      final stationName = stationEntry.key;
      final coachBerthMap = stationEntry.value;

      if (coachBerthMap.isNotEmpty) {
        // Check if we've already notified for this station in current journey
        final stationKey = '${response.trainNumber}_${stationName}_boarding';
        if (_notifiedStationsForCurrentJourney.contains(stationKey)) {
          if (kDebugMode) {
            print(
                'OnboardingNotificationService: Skipping duplicate boarding notification for $stationName (already notified in current journey)');
          }
          continue;
        }

        final context = StationNotificationContext.fromUpcomingStationResponse(
          response,
          stationName,
        );

        final notificationData =
            _generateBoardingNotification(context, coachBerthMap);

        // Mark this station as notified for current journey
        _notifiedStationsForCurrentJourney.add(stationKey);

        await _scheduleNotification(
          notificationData,
          NotificationTrigger.timeDelay(boardingNotificationDelay),
        );

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Scheduled boarding notification for $stationName with ${coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length)} passengers');
        }
      }
    }
  }

  /// Process off-boarding notifications for stations with passenger off-boarding data
  /// Enhanced for CA/CS/EHK with 50km radius logic and duplicate prevention
  Future<void> _processOffBoardingNotifications(
      UpcomingStationResponse response) async {
    for (final stationEntry in response.detailsOffBoarding.entries) {
      final stationName = stationEntry.key;
      final coachBerthMap = stationEntry.value;

      if (coachBerthMap.isNotEmpty) {
        // Check if we've already notified for this station in current journey
        final stationKey = '${response.trainNumber}_${stationName}_offboarding';
        if (_notifiedStationsForCurrentJourney.contains(stationKey)) {
          if (kDebugMode) {
            print(
                'OnboardingNotificationService: Skipping duplicate off-boarding notification for $stationName (already notified in current journey)');
          }
          continue;
        }

        final context = StationNotificationContext.fromUpcomingStationResponse(
          response,
          stationName,
        );

        final notificationData =
            _generateOffBoardingNotification(context, coachBerthMap);

        // Mark this station as notified for current journey
        _notifiedStationsForCurrentJourney.add(stationKey);

        await _scheduleNotification(
          notificationData,
          NotificationTrigger.timeDelay(offBoardingNotificationDelay),
        );

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Scheduled off-boarding notification for $stationName with ${coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length)} passengers');
        }
      }
    }
  }

  /// Process general station approaching notifications
  Future<void> _processStationApproachingNotifications(
      UpcomingStationResponse response) async {
    for (int i = 0; i < response.stations.length; i++) {
      final stationName = response.stations[i];
      final context = StationNotificationContext.fromUpcomingStationResponse(
        response,
        stationName,
      );

      final notificationData = _generateStationApproachingNotification(context);
      await _scheduleNotification(
        notificationData,
        NotificationTrigger.stationsBefore(stationsBeforeAlert),
      );
    }
  }

  /// Generate boarding notification content
  OnboardingNotificationData _generateBoardingNotification(
    StationNotificationContext context,
    Map<String, List<int>> coachBerthMap,
  ) {
    final totalPassengers =
        coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length);

    final coachList = coachBerthMap.keys.join(', ');

    final title = 'Passengers Boarding at ${context.stationName}';
    final body = totalPassengers == 1
        ? 'One passenger boarding in coach $coachList'
        : '$totalPassengers passengers boarding in coaches: $coachList';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'boarding',
      ),
      type: OnboardingNotificationType.boarding,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'coach_berth_map': coachBerthMap,
        'total_passengers': totalPassengers,
        'notification_category': 'boarding_alert',
      },
      isUrgent: totalPassengers > 10, // Mark as urgent for large groups
    );
  }

  /// Generate off-boarding notification content
  OnboardingNotificationData _generateOffBoardingNotification(
    StationNotificationContext context,
    Map<String, List<int>> coachBerthMap,
  ) {
    final totalPassengers =
        coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length);

    final coachList = coachBerthMap.keys.join(', ');

    final title = 'Passengers Off-boarding at ${context.stationName}';
    final body = totalPassengers == 1
        ? 'One passenger off-boarding from coach $coachList'
        : '$totalPassengers passengers off-boarding from coaches: $coachList';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'offboarding',
      ),
      type: OnboardingNotificationType.offBoarding,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'coach_berth_map': coachBerthMap,
        'total_passengers': totalPassengers,
        'notification_category': 'offboarding_reminder',
      },
      isUrgent: context.isDestination, // Mark as urgent if final destination
    );
  }

  /// Generate station approaching notification content
  OnboardingNotificationData _generateStationApproachingNotification(
    StationNotificationContext context,
  ) {
    final title = 'Approaching ${context.stationName}';
    final body = context.isDestination
        ? 'Final destination ${context.stationName} approaching'
        : 'Next station: ${context.stationName}';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'approaching',
      ),
      type: OnboardingNotificationType.stationApproaching,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'station_approaching',
        'station_index': context.stationIndex,
      },
      isUrgent: context.isDestination,
    );
  }

  // Phase 2: New notification generation methods

  /// Generate station approach alert notification (with timing)
  OnboardingNotificationData _generateStationApproachAlert(
    StationNotificationContext context,
    int minutesBeforeArrival,
  ) {
    final title = 'Approaching ${context.stationName}';
    final body =
        'Next stop in $minutesBeforeArrival minutes - ${context.stationName}';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'approach_alert',
      ),
      type: OnboardingNotificationType.stationApproachAlert,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'station_approach_alert',
        'minutes_before_arrival': minutesBeforeArrival,
        'station_index': context.stationIndex,
      },
      isUrgent: minutesBeforeArrival <= 2 || context.isDestination,
    );
  }

  /// Generate boarding count update notification
  OnboardingNotificationData _generateBoardingCountUpdate(
    StationNotificationContext context,
    Map<String, List<int>> coachBerthMap,
    int previousCount,
  ) {
    final currentCount =
        coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length);
    final difference = currentCount - previousCount;
    final coachList = coachBerthMap.keys.join(', ');

    final title = 'Boarding Update - ${context.stationName}';
    final body = difference > 0
        ? '+$difference passengers boarding in coaches: $coachList (Total: $currentCount)'
        : 'Boarding complete at ${context.stationName} - $currentCount passengers';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'boarding_update',
      ),
      type: OnboardingNotificationType.boardingCountUpdate,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'boarding_count_update',
        'current_count': currentCount,
        'previous_count': previousCount,
        'difference': difference,
        'coach_berth_map': coachBerthMap,
      },
      isUrgent: currentCount > 15, // Mark as urgent for large groups
    );
  }

  /// Generate off-boarding preparation notification
  OnboardingNotificationData _generateOffBoardingPreparation(
    StationNotificationContext context,
    Map<String, List<int>> coachBerthMap,
    int minutesBeforeStation,
  ) {
    final totalPassengers =
        coachBerthMap.values.fold<int>(0, (sum, berths) => sum + berths.length);
    final coachDetails = coachBerthMap.entries
        .map((entry) => '${entry.key}: ${entry.value.join(', ')}')
        .join(' | ');

    final title = 'Prepare for Off-boarding - ${context.stationName}';
    final body =
        'Arriving in $minutesBeforeStation minutes. $totalPassengers passengers off-boarding: $coachDetails';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'offboarding_prep',
      ),
      type: OnboardingNotificationType.offBoardingPreparation,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'offboarding_preparation',
        'total_passengers': totalPassengers,
        'minutes_before_station': minutesBeforeStation,
        'coach_berth_details': coachDetails,
        'coach_berth_map': coachBerthMap,
      },
      isUrgent: context.isDestination || minutesBeforeStation <= 3,
    );
  }

  /// Generate train status update notification
  OnboardingNotificationData _generateTrainStatusUpdate(
    StationNotificationContext context,
    String statusType,
    String statusMessage,
    Map<String, dynamic> statusData,
  ) {
    final title = 'Train ${context.trainNumber} - ${statusType.toUpperCase()}';
    final body = statusMessage;

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        'status',
        statusType,
      ),
      type: OnboardingNotificationType.trainStatusUpdate,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'train_status_update',
        'status_type': statusType,
        'status_data': statusData,
      },
      isUrgent: ['delay', 'cancellation', 'emergency']
          .contains(statusType.toLowerCase()),
    );
  }

  /// Generate proximity alert notification
  OnboardingNotificationData _generateProximityAlert(
    StationNotificationContext context,
    double distanceKm,
  ) {
    final title = 'Approaching ${context.stationName}';
    final body =
        'You are ${distanceKm.toStringAsFixed(1)}km away from ${context.stationName}';

    return OnboardingNotificationData(
      notificationId: _generateNotificationId(
        context.trainNumber,
        context.stationName,
        'proximity',
      ),
      type: OnboardingNotificationType.proximityAlert,
      title: title,
      body: body,
      stationContext: context,
      additionalData: {
        'notification_category': 'proximity_alert',
        'distance_km': distanceKm,
      },
      isUrgent: distanceKm <= 1.0,
    );
  }

  /// Schedule notification with the specified trigger
  Future<void> _scheduleNotification(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    // Check for duplicates
    if (_processedNotifications.contains(notificationData.notificationId)) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Skipping duplicate notification ${notificationData.notificationId}');
      }
      return;
    }

    try {
      // Convert to RemoteMessage format for compatibility with existing service
      final remoteMessage = _convertToRemoteMessage(notificationData);

      // For immediate triggers, show notification right away
      if (trigger.type == NotificationTriggerType.immediate) {
        _firebaseMessagingService.showFlutterNotification(remoteMessage);
        _processedNotifications.add(notificationData.notificationId);

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Immediate notification sent: ${notificationData.title}');
        }
      }
      // For time-based delays, schedule with Timer
      else if (trigger.type == NotificationTriggerType.timeBasedDelay &&
          trigger.delay != null) {
        Timer(trigger.delay!, () {
          _firebaseMessagingService.showFlutterNotification(remoteMessage);
          _processedNotifications.add(notificationData.notificationId);

          if (kDebugMode) {
            print(
                'OnboardingNotificationService: Delayed notification sent: ${notificationData.title}');
          }
        });

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Notification scheduled for ${trigger.delay!.inMinutes} minutes: ${notificationData.title}');
        }
      }
      // Phase 2: Enhanced trigger implementations
      else if (trigger.type == NotificationTriggerType.locationBasedProximity) {
        await _handleLocationBasedTrigger(notificationData, trigger);
      } else if (trigger.type == NotificationTriggerType.stationSequence) {
        await _handleStationSequenceTrigger(notificationData, trigger);
      } else if (trigger.type ==
          NotificationTriggerType.passengerCountThreshold) {
        await _handlePassengerCountTrigger(notificationData, trigger);
      } else if (trigger.type == NotificationTriggerType.trainStatusChange) {
        await _handleTrainStatusTrigger(notificationData, trigger);
      } else if (trigger.type == NotificationTriggerType.scheduleChange) {
        await _handleScheduleChangeTrigger(notificationData, trigger);
      } else {
        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Trigger type ${trigger.type} not yet implemented, sending immediately');
        }
        _firebaseMessagingService.showFlutterNotification(remoteMessage);
        _processedNotifications.add(notificationData.notificationId);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error scheduling notification: $e');
      }
    }
  }

  /// Convert OnboardingNotificationData to RemoteMessage for compatibility
  RemoteMessage _convertToRemoteMessage(
      OnboardingNotificationData notificationData) {
    return RemoteMessage(
      messageId: notificationData.notificationId,
      data: notificationData.toNotificationModel().data,
      notification: RemoteNotification(
        title: notificationData.title,
        body: notificationData.body,
        android: const AndroidNotification(
          channelId: 'high_importance_channel',
          smallIcon: 'ic_notification',
        ),
      ),
    );
  }

  /// Generate unique notification ID
  /// For CA/CS/EHK notifications, we need to ensure one notification per station per journey
  /// to prevent duplicates when stations re-enter the 50km zone
  String _generateNotificationId(
      String trainNumber, String stationName, String type) {
    // Use date to ensure notifications reset for new journeys
    final dateKey =
        DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD
    return '${trainNumber}_${stationName}_${type}_$dateKey';
  }

  // Phase 2: Enhanced trigger handler methods

  /// Handle location-based proximity triggers
  Future<void> _handleLocationBasedTrigger(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    try {
      final thresholdKm =
          trigger.proximityThresholdKm ?? _defaultProximityThresholdKm;
      final stationName = notificationData.stationContext.stationName;

      // Start location monitoring for this notification
      await _startLocationMonitoring(notificationData, thresholdKm);

      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Location-based trigger set for $stationName (${thresholdKm}km threshold)');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error setting location trigger: $e');
      }
      // Fallback to immediate notification
      final remoteMessage = _convertToRemoteMessage(notificationData);
      _firebaseMessagingService.showFlutterNotification(remoteMessage);
      _processedNotifications.add(notificationData.notificationId);
    }
  }

  /// Handle station sequence triggers (e.g., 2 stations before)
  Future<void> _handleStationSequenceTrigger(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    try {
      final stationsBefore = trigger.stationsBefore ?? stationsBeforeAlert;
      final context = notificationData.stationContext;
      final targetStationIndex = context.stationIndex;

      if (targetStationIndex != null && targetStationIndex >= stationsBefore) {
        // Calculate trigger station index
        final triggerStationIndex = targetStationIndex - stationsBefore;
        final triggerStationName = context.allStations[triggerStationIndex];

        // For now, use time-based delay as approximation
        // In a real implementation, this would integrate with train tracking
        final estimatedDelay =
            Duration(minutes: stationsBefore * 10); // ~10 min per station

        Timer(estimatedDelay, () {
          final remoteMessage = _convertToRemoteMessage(notificationData);
          _firebaseMessagingService.showFlutterNotification(remoteMessage);
          _processedNotifications.add(notificationData.notificationId);
        });

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Station sequence trigger set for ${context.stationName} ($stationsBefore stations before, trigger at $triggerStationName)');
        }
      } else {
        // Not enough stations before target, send immediately
        final remoteMessage = _convertToRemoteMessage(notificationData);
        _firebaseMessagingService.showFlutterNotification(remoteMessage);
        _processedNotifications.add(notificationData.notificationId);
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error setting station sequence trigger: $e');
      }
    }
  }

  /// Handle passenger count threshold triggers
  Future<void> _handlePassengerCountTrigger(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    try {
      final threshold = trigger.conditions['passenger_threshold'] ??
          _defaultPassengerCountThreshold;
      final currentCount =
          notificationData.additionalData['total_passengers'] ?? 0;

      if (currentCount >= threshold) {
        // Threshold met, send notification immediately
        final remoteMessage = _convertToRemoteMessage(notificationData);
        _firebaseMessagingService.showFlutterNotification(remoteMessage);
        _processedNotifications.add(notificationData.notificationId);

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Passenger count threshold met ($currentCount >= $threshold)');
        }
      } else {
        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Passenger count below threshold ($currentCount < $threshold), notification not sent');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error handling passenger count trigger: $e');
      }
    }
  }

  /// Handle train status change triggers
  Future<void> _handleTrainStatusTrigger(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    try {
      // This would integrate with real-time train status API
      // For now, we'll implement a basic version
      final statusTypes = trigger.conditions['status_types'] as List<String>? ??
          ['delay', 'cancellation', 'platform_change'];

      // In a real implementation, this would monitor train status changes
      // For now, we'll send the notification immediately as a placeholder
      final remoteMessage = _convertToRemoteMessage(notificationData);
      _firebaseMessagingService.showFlutterNotification(remoteMessage);
      _processedNotifications.add(notificationData.notificationId);

      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Train status trigger set for types: $statusTypes');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error handling train status trigger: $e');
      }
    }
  }

  /// Handle schedule change triggers
  Future<void> _handleScheduleChangeTrigger(
    OnboardingNotificationData notificationData,
    NotificationTrigger trigger,
  ) async {
    try {
      final minimumDelayMinutes =
          trigger.conditions['minimum_delay_minutes'] ?? 5;
      final includeAdvance =
          trigger.conditions['include_advance_notifications'] ?? true;

      // This would integrate with schedule monitoring API
      // For now, we'll implement a basic version
      final remoteMessage = _convertToRemoteMessage(notificationData);
      _firebaseMessagingService.showFlutterNotification(remoteMessage);
      _processedNotifications.add(notificationData.notificationId);

      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Schedule change trigger set (min delay: ${minimumDelayMinutes}min, advance: $includeAdvance)');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error handling schedule change trigger: $e');
      }
    }
  }

  // Phase 2: Location monitoring methods

  /// Start location monitoring for proximity-based notifications
  Future<void> _startLocationMonitoring(
    OnboardingNotificationData notificationData,
    double thresholdKm,
  ) async {
    try {
      // Check location permissions
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('OnboardingNotificationService: Location permission denied');
        }
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
      );
      _lastKnownPosition = position;

      // Try to get station coordinates using the nearest station API
      final stationData = await LocationService.getNearestStation(
        position.latitude.toString(),
        position.longitude.toString(),
      );

      // Start periodic location checking
      _locationMonitoringTimer?.cancel();
      _locationMonitoringTimer =
          Timer.periodic(_defaultLocationUpdateInterval, (timer) {
        _checkProximityToStation(notificationData, thresholdKm);
      });

      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Started location monitoring for ${notificationData.stationContext.stationName}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'OnboardingNotificationService: Error starting location monitoring: $e');
      }
    }
  }

  /// Check proximity to target station
  Future<void> _checkProximityToStation(
    OnboardingNotificationData notificationData,
    double thresholdKm,
  ) async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.medium,
      );
      _lastKnownPosition = position;

      final stationName = notificationData.stationContext.stationName;
      final notificationId = notificationData.notificationId;

      // Skip if already triggered
      if (_triggeredProximityNotifications.contains(notificationId)) {
        return;
      }

      // In a real implementation, you would have station coordinates
      // For now, we'll use a simplified approach with the nearest station API
      final nearestStationData = await LocationService.getNearestStation(
        position.latitude.toString(),
        position.longitude.toString(),
      );

      // This is a simplified check - in reality you'd compare with actual station coordinates
      // For now, we'll trigger based on the API response indicating we're near a station
      if (nearestStationData.isNotEmpty) {
        // Mark as triggered and send notification
        _triggeredProximityNotifications.add(notificationId);
        final remoteMessage = _convertToRemoteMessage(notificationData);
        _firebaseMessagingService.showFlutterNotification(remoteMessage);
        _processedNotifications.add(notificationId);

        if (kDebugMode) {
          print(
              'OnboardingNotificationService: Proximity notification triggered for $stationName');
        }

        // Stop monitoring for this notification
        _locationMonitoringTimer?.cancel();
      }
    } catch (e) {
      if (kDebugMode) {
        print('OnboardingNotificationService: Error checking proximity: $e');
      }
    }
  }

  /// Calculate distance between two coordinates using Haversine formula
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  /// Convert degrees to radians
  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Clear processed notifications cache (useful for testing)
  void clearProcessedNotifications() {
    _processedNotifications.clear();
  }

  /// Get count of processed notifications
  int get processedNotificationCount => _processedNotifications.length;

  /// Quick method to trigger immediate notifications for testing
  /// This can be used during development and testing phases
  Future<void> triggerImmediateNotification({
    required String title,
    required String body,
    OnboardingNotificationType type = OnboardingNotificationType.boarding,
    Map<String, dynamic> additionalData = const {},
  }) async {
    final notificationData = OnboardingNotificationData(
      notificationId: _generateNotificationId('TEST', 'STATION', type.name),
      type: type,
      title: title,
      body: body,
      stationContext: StationNotificationContext(
        stationName: 'Test Station',
        trainNumber: 'TEST123',
        date: DateTime.now().toString(),
        coachNumbers: ['A1'],
        allStations: ['Test Station'],
      ),
      additionalData: additionalData,
    );

    await _scheduleNotification(
      notificationData,
      NotificationTrigger.immediate(),
    );
  }

  /// Configure notification timing settings
  static Duration boardingNotificationDelay = _defaultBoardingNotificationDelay;
  static Duration offBoardingNotificationDelay =
      _defaultOffBoardingNotificationDelay;
  static int stationsBeforeAlert = _defaultStationsBeforeAlert;

  // Phase 2: Enhanced configuration properties
  static double proximityThresholdKm = _defaultProximityThresholdKm;
  static int passengerCountThreshold = _defaultPassengerCountThreshold;
  static Duration stationApproachTime = _defaultStationApproachTime;
  static Duration locationUpdateInterval = _defaultLocationUpdateInterval;
  static bool enableLocationMonitoring = true;
  static bool enableBoardingCountUpdates = true;
  static bool enableOffBoardingPreparation = true;
  static bool enableScheduleChangeAlerts = true;
  static List<String> monitoredStatusTypes = [
    'delay',
    'cancellation',
    'platform_change'
  ];
  static Duration statusCheckInterval = const Duration(minutes: 5);

  /// Update notification timing configuration
  static void updateNotificationConfig({
    Duration? boardingDelay,
    Duration? offBoardingDelay,
    int? stationsBefore,
  }) {
    if (boardingDelay != null) {
      boardingNotificationDelay = boardingDelay;
    }
    if (offBoardingDelay != null) {
      offBoardingNotificationDelay = offBoardingDelay;
    }
    if (stationsBefore != null) {
      stationsBeforeAlert = stationsBefore;
    }
  }

  // Phase 2: Enhanced configuration methods

  /// Update proximity-based notification configuration
  static void updateProximityConfig({
    double? proximityThreshold,
    Duration? updateInterval,
    bool? enableMonitoring,
  }) {
    if (proximityThreshold != null) {
      proximityThresholdKm = proximityThreshold;
    }
    if (updateInterval != null) {
      locationUpdateInterval = updateInterval;
    }
    if (enableMonitoring != null) {
      enableLocationMonitoring = enableMonitoring;
    }
  }

  /// Update passenger count notification configuration
  static void updatePassengerCountConfig({
    int? threshold,
    bool? enableBoardingUpdates,
    bool? enableOffBoardingPrep,
  }) {
    if (threshold != null) {
      passengerCountThreshold = threshold;
    }
    if (enableBoardingUpdates != null) {
      enableBoardingCountUpdates = enableBoardingUpdates;
    }
    if (enableOffBoardingPrep != null) {
      enableOffBoardingPreparation = enableOffBoardingPrep;
    }
  }

  /// Update train status monitoring configuration
  static void updateTrainStatusConfig({
    List<String>? statusTypes,
    Duration? checkInterval,
    bool? enableScheduleAlerts,
  }) {
    if (statusTypes != null) {
      monitoredStatusTypes = statusTypes;
    }
    if (checkInterval != null) {
      statusCheckInterval = checkInterval;
    }
    if (enableScheduleAlerts != null) {
      enableScheduleChangeAlerts = enableScheduleAlerts;
    }
  }
}
