import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';

class MapServices {
  static Future<List<dynamic>> fetchRailwayStations({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final responseJson = await ApiService.get(
        '/api/overpass/?lat=$latitude&lng=$longitude',
        {},
      );

      List<dynamic> stationsAll = responseJson['elements'];

      List<dynamic> filteredStations = stationsAll
          .where((station) => station['tags'] != null && station['tags']['ref'] != null)
          .toList();

      return filteredStations;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      print(e);
      return [];
    }
  }
}
