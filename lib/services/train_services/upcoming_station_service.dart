import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/notification_services/onboarding_notification_service.dart';
import 'package:railops/services/background_services/notification_background_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

class UpcomingStationService {
  static final OnboardingNotificationService _notificationService =
      OnboardingNotificationService();

  // Calls the API to fetch upcoming station details
  static Future<UpcomingStationResponse> fetchUpcomingStationDetails({
    required String lat,
    required String lng,
    required String token,
    // Phase 3.1: Enhanced notification integration options
    bool enableNotificationProcessing = false,
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications = false,
    bool enableLocationBasedScheduling = false,
  }) async {
    try {
      // Temporarily comment out FCM and device info to match Postman exactly
      // final fcmToken = await FcmTokenService.getTokenForApiRequest(token);
      // final deviceInfo = await FcmTokenService.getDeviceInfo();

      // Prepare request body - match Postman format exactly for debugging
      final requestBody = {
        'lat': lat,
        'lng': lng,
        'token': token,
        // Temporarily comment out extra fields to match Postman exactly
        // if (fcmToken != null && FcmTokenService.isValidFcmToken(fcmToken))
        //   'fcm_token': fcmToken,
        // 'device_info': deviceInfo,
      };

      debugPrint('🔍 API Request Debug:');
      debugPrint('URL: ${ApiConstant.baseUrl}/api/onboarding_details_popup/');
      debugPrint('Body: ${jsonEncode(requestBody)}');

      final responseJson = await ApiService.post(
        '/api/onboarding_details_popup/',
        requestBody,
      );

      // Log FCM token registration status if present in response
      if (responseJson.containsKey('fcm_registration')) {
        final fcmStatus = responseJson['fcm_registration'];
        debugPrint(
            'FCM Registration Status: ${fcmStatus['status']} - ${fcmStatus['message']}');
      }

      final response = UpcomingStationResponse.fromJson(responseJson);

      // Phase 3.1: Process notifications if enabled
      if (enableNotificationProcessing) {
        try {
          await _processNotificationsForResponse(
            response,
            enableBoardingNotifications: enableBoardingNotifications,
            enableOffBoardingNotifications: enableOffBoardingNotifications,
            enableStationApproachingNotifications:
                enableStationApproachingNotifications,
            enableLocationBasedScheduling: enableLocationBasedScheduling,
            currentLat: lat,
            currentLng: lng,
          );

          if (kDebugMode) {
            print(
                'UpcomingStationService: Successfully processed notifications for train ${response.trainNumber}');
          }
        } catch (notificationError) {
          // Log notification errors but don't fail the main API call
          if (kDebugMode) {
            print(
                'UpcomingStationService: Error processing notifications: $notificationError');
          }
        }
      }

      return response;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception(e);
    }
  }

  /// Phase 3.1: Private helper method to process notifications for API response
  static Future<void> _processNotificationsForResponse(
    UpcomingStationResponse response, {
    required bool enableBoardingNotifications,
    required bool enableOffBoardingNotifications,
    required bool enableStationApproachingNotifications,
    required bool enableLocationBasedScheduling,
    required String currentLat,
    required String currentLng,
  }) async {
    try {
      // Process standard notifications using the existing service
      await _notificationService.processUpcomingStationResponse(
        response,
        enableBoardingNotifications: enableBoardingNotifications,
        enableOffBoardingNotifications: enableOffBoardingNotifications,
        enableStationApproachingNotifications:
            enableStationApproachingNotifications,
      );

      // Phase 3.1: Enhanced location-based scheduling
      if (enableLocationBasedScheduling) {
        await _processLocationBasedScheduling(
          response,
          currentLat: currentLat,
          currentLng: currentLng,
        );
      }

      if (kDebugMode) {
        print(
            'UpcomingStationService: Notification processing completed for train ${response.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UpcomingStationService: Error in notification processing: $e');
      }
      rethrow;
    }
  }

  /// Phase 3.1: Enhanced location-based notification scheduling
  static Future<void> _processLocationBasedScheduling(
    UpcomingStationResponse response, {
    required String currentLat,
    required String currentLng,
  }) async {
    try {
      // Store current location for background processing
      await _storeLocationForBackgroundProcessing(
        response,
        currentLat: currentLat,
        currentLng: currentLng,
      );

      if (kDebugMode) {
        print(
            'UpcomingStationService: Location-based scheduling configured for train ${response.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UpcomingStationService: Error in location-based scheduling: $e');
      }
    }
  }

  /// Store location and train data for background notification processing
  static Future<void> _storeLocationForBackgroundProcessing(
    UpcomingStationResponse response, {
    required String currentLat,
    required String currentLng,
  }) async {
    try {
      // Phase 3.2: Start background notification processing
      await NotificationBackgroundService.startBackgroundProcessing(
        trainData: response,
        currentLat: currentLat,
        currentLng: currentLng,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableProximityAlerts: true,
        proximityThresholdKm: 2.0,
      );

      if (kDebugMode) {
        print(
            'UpcomingStationService: Started background notification processing for train ${response.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error starting background notification processing: $e');
      }
    }
  }

  /// Phase 3.1: Stop background notification processing
  static Future<void> stopBackgroundNotificationProcessing() async {
    try {
      await NotificationBackgroundService.stopBackgroundProcessing();

      if (kDebugMode) {
        print(
            'UpcomingStationService: Stopped background notification processing');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error stopping background notification processing: $e');
      }
    }
  }

  /// Phase 3.1: Get background notification processing status
  static Future<Map<String, dynamic>> getBackgroundNotificationStatus() async {
    try {
      return await NotificationBackgroundService
          .getBackgroundProcessingStatus();
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error getting background notification status: $e');
      }
      return {
        'is_active': false,
        'error': e.toString(),
      };
    }
  }
}
