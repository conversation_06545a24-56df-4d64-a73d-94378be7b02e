import 'dart:typed_data';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_exception.dart';
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;


class TrainPdfService {

  static Future<Uint8List>  downloadPdf(String trainNo, String date, String stationCode) async {
    try {
      final endpoint = '/pdf/train_no=$trainNo/date/$date/station_code=$stationCode/';

      final response = await http.get(
        Uri.parse('${ApiConstant.baseUrl}$endpoint'), 
      );
      
      if (response.statusCode == 200) {
        return response.bodyBytes; 
      } else {
        throw ApiException(response.statusCode, 'Failed to download PDF');
      }
    } catch (e) {
      throw ApiException(500, 'Error occurred while downloading PDF: $e');
    }
  }
}


