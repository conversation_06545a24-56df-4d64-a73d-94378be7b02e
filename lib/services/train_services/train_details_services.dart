import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';

class TrainService {
  // Fetches the train name based on train number
  static Future<String> getTrainName(final String trainNo) async {
    try {
      final responseJson =
          await ApiService.get('/api/get_train_name/?train_no=$trainNo', {});
      return responseJson['train_name'] as String;
    } on ApiException catch (e) {
      if (e.statusCode == 400) {
        throw 'Train details not found';
      }
      throw e.message;
    } catch (e) {
      return 'Unknown Train';
    }
  }

  static Future<Map<String, dynamic>> getTrainStations(
      final String trainNo) async {
    try {
      final responseJson =
          await ApiService.get('/api/train-route/?train_no=$trainNo', {});

      List<String> stationList = (responseJson['train_route'] as List<dynamic>)
          .map((station) => station.toString())
          .toList();

      Map<String, String> stationsDict =
          (responseJson['stations_dict'] as Map<dynamic, dynamic>)
              .map((key, value) => MapEntry(key.toString(), value.toString()));

      return {
        "stationList": stationList,
        "stationsDict": stationsDict,
      };
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return {
        "stationList": [],
        "stationsDict": {},
      };
    }
  }

  // Fetches train data based on train number, date, and station code
  static Future<TrainDataResponse> fetchTrainData({
    required String trainNumber,
    required String date,
    required String stationCode,
  }) async {
    try {
      final response = await ApiService.get(
        '/api/train_number=$trainNumber/date/$date/station_code=$stationCode/',
        {},
      );
      final Map<String, dynamic> data = response;
      return TrainDataResponse.fromJson(data);
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  //Fetch Charting Data
  static Future<TrainChartingResponse?> fetchChartingTime(
      {String? trainNumber, String? date, String? token}) async {
    try {
      const endpoint = "/api/get_charting_times/";

      final body = <String, String>{};
      body["token"] = token!;
      body["train_number"] = trainNumber!;
      body["date"] = date!;

      final responseJson = await ApiService.post(endpoint, body);
      return TrainChartingResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  // Fetches all available train numbers
  static Future<List<String>> getTrainNumbers() async {
    try {
      final responseJson = await ApiService.get('/api/all-trains/', {});
      List<String> trainNumbers = List<String>.from(responseJson);
      return trainNumbers;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return [];
    }
  }

  // Fetch train details for the authenticated user
  static Future<AddTrainsResponse> getTrainDetails(String token) async {
    try {
      final responseJson = await ApiService.get(
          '/api/users/profile/get_trains', {'token': token});
      return AddTrainsResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw (e.message);
    }
  }

  // Adds train details for the authenticated user
  static Future<void> addTrainDetails(
      {required String trainNumber,
      required List<String> coachNumbers,
      required String originDate,
      required String token}) async {
    try {
      final response = await ApiService.post(
        '/api/users/profile/add_trains',
        {
          'train_number': trainNumber,
          'coach_numbers':
              coachNumbers.where((station) => station != 'Select All').toList(),
          'origin_date': originDate,
          'token': token
        },
      );
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'An error occurred while adding train details ${e}';
    }
  }

  // Deletes train details for the authenticated user
  static Future<void> deleteTrainDetails(
      String token, String trainNumber) async {
    try {
      final response = await ApiService.delete(
        '/api/users/profile/delete_trains/$trainNumber/',
        {'token': token},
      );

      if (response['status'] != 204) {
        throw Exception('Failed to delete train details');
      }
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'An error occurred while deleting train details';
    }
  }
}
