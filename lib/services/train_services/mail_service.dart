import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';

class MailService {
  static Future<String> sendMailOnboardingTableBtn({
    required String date,
    required String trainNo,
    required List<String> stations,
    required String? token,
  }) async {
    try {
      final Map<String, dynamic> requestData = {
        'date': date,
        'train_no': trainNo,
        'stations': stations.join(','),
        'token':token
      };

      final responseJson = await ApiService.post(
        '/mail/onboarding_details_btn/',
        requestData,
      );

      return responseJson['message'] as String;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      return 'An error occurred while sending the mail';
    }
  }
}
