import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/upload_types/upload_request.dart';

class UploadService {
  static Future<String> uploadTrainSchedule(UploadRequest uploadRequest) async {
    try {
      final response = await ApiService.post(
        '/api/save_train_schedule/',
        uploadRequest.toJson(),
        
      );

      return response['message'] as String;
      
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'An error occurred while uploading train schedule';
    }
  }
}
