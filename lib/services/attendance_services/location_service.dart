import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';

class OnboardingService {
  OnboardingService();

  static Future<OnboardingResponse> fetchOnboardingDetails(
    final String trainNumber,
    final String date,
  ) async {
    try {
      final responseJson = await ApiService.get(
        // '/api/onboarding_details_by_nearby_stations/',
        "/microservice/train/location/?train_number=$trainNumber&date=$date",
        {
          'token': await _fetchToken(),
          if (trainNumber.isNotEmpty) 'train_number': trainNumber,
          if (date.isNotEmpty) 'date': date,
        },
      );

      return OnboardingResponse.fromJson(responseJson);
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception('Error occurred while fetching onboarding details: $e');
    }
  }

  static Future<String> _fetchToken() async {
    final userModel = UserModel();
    await userModel.loadUserData();
    return userModel.token;
  }
}

/// Enhanced service for train location-based notifications
/// Handles proximity detection and anti-spam logic for station notifications
class TrainLocationNotificationService {
  static final TrainLocationNotificationService _instance =
      TrainLocationNotificationService._internal();

  factory TrainLocationNotificationService() => _instance;
  TrainLocationNotificationService._internal();

  // Anti-spam tracking
  final Set<String> _notifiedStations = <String>{};
  final Map<String, DateTime> _lastNotificationTime = <String, DateTime>{};

  // Configuration
  static const double proximityThresholdKm = 50.0; // 50km radius as specified
  static const Duration minNotificationInterval =
      Duration(hours: 1); // Prevent spam

  /// Fetch train location data and trigger notifications based on proximity
  static Future<OnboardingResponse?> fetchTrainLocationWithNotifications({
    required String trainNumber,
    required String date,
    Position? currentPosition,
  }) async {
    try {
      if (kDebugMode) {
        print('🚂 Fetching train location data for $trainNumber on $date');
      }

      final response = await OnboardingService.fetchOnboardingDetails(
        trainNumber,
        date,
      );

      // If we have current position, check proximity to stations
      if (currentPosition != null && response.stations != null) {
        await _instance._checkProximityAndNotify(response, currentPosition);
      }

      return response;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching train location data: $e');
      }
      return null;
    }
  }

  /// Check proximity to stations and trigger notifications if within threshold
  Future<void> _checkProximityAndNotify(
    OnboardingResponse response,
    Position currentPosition,
  ) async {
    try {
      if (response.stations == null || response.stations!.isEmpty) {
        return;
      }

      for (final stationCode in response.stations!) {
        final notificationKey = '${response.trainNumber}_$stationCode';

        // Check if we've already notified for this station recently
        if (_shouldSkipNotification(notificationKey)) {
          continue;
        }

        // In a real implementation, you would get station coordinates
        // For now, we'll simulate proximity check
        final isWithinProximity = await _checkStationProximity(
          stationCode,
          currentPosition,
        );

        if (isWithinProximity) {
          await _triggerStationNotification(response, stationCode);
          _markStationNotified(notificationKey);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking proximity: $e');
      }
    }
  }

  /// Check if notification should be skipped (anti-spam logic)
  bool _shouldSkipNotification(String notificationKey) {
    if (_notifiedStations.contains(notificationKey)) {
      final lastTime = _lastNotificationTime[notificationKey];
      if (lastTime != null) {
        final timeSinceLastNotification = DateTime.now().difference(lastTime);
        if (timeSinceLastNotification < minNotificationInterval) {
          if (kDebugMode) {
            print('⏭️ Skipping notification for $notificationKey (too recent)');
          }
          return true;
        }
      }
    }
    return false;
  }

  /// Mark station as notified to prevent spam
  void _markStationNotified(String notificationKey) {
    _notifiedStations.add(notificationKey);
    _lastNotificationTime[notificationKey] = DateTime.now();
  }

  /// Check if current position is within proximity threshold of station
  Future<bool> _checkStationProximity(
    String stationCode,
    Position currentPosition,
  ) async {
    try {
      // TODO: Implement actual station coordinate lookup
      // For now, we'll use a simplified approach
      // In production, you would:
      // 1. Get station coordinates from a database/API
      // 2. Calculate actual distance using Geolocator.distanceBetween()

      if (kDebugMode) {
        print('📍 Checking proximity to station $stationCode');
        print(
            'Current position: ${currentPosition.latitude}, ${currentPosition.longitude}');
      }

      // Simulate proximity check - in real implementation, replace with actual coordinates
      return true; // For testing purposes, always return true
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error checking station proximity: $e');
      }
      return false;
    }
  }

  /// Trigger notification for station approach
  Future<void> _triggerStationNotification(
    OnboardingResponse response,
    String stationCode,
  ) async {
    try {
      // Get coach data for this station
      final coachData = _getCoachDataForStation(response, stationCode);

      if (coachData.isEmpty) {
        return;
      }

      // Create notification content
      final notificationTitle = 'Approaching $stationCode';
      final notificationBody = _buildNotificationBody(coachData);

      if (kDebugMode) {
        print('🔔 Triggering notification for station $stationCode');
        print('Title: $notificationTitle');
        print('Body: $notificationBody');
      }

      // TODO: Integrate with existing notification system
      // This would call the existing FirebaseMessagingService or NotificationProvider
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error triggering station notification: $e');
      }
    }
  }

  /// Get coach data for a specific station
  Map<String, Map<String, int>> _getCoachDataForStation(
    OnboardingResponse response,
    String stationCode,
  ) {
    final Map<String, Map<String, int>> coachData = {};

    // Extract onboarding data
    if (response.details != null &&
        response.details!.containsKey(stationCode)) {
      final stationDetails = response.details![stationCode]!;
      for (final coach in stationDetails.keys) {
        coachData[coach] = coachData[coach] ?? {};
        coachData[coach]!['onboarding'] = stationDetails[coach]!.length;
      }
    }

    // Extract off-boarding data
    if (response.detailsOffBoarding != null &&
        response.detailsOffBoarding!.containsKey(stationCode)) {
      final stationDetails = response.detailsOffBoarding![stationCode]!;
      for (final coach in stationDetails.keys) {
        coachData[coach] = coachData[coach] ?? {};
        coachData[coach]!['offboarding'] = stationDetails[coach]!.length;
      }
    }

    // Extract vacant data
    if (response.detailsVacant != null &&
        response.detailsVacant!.containsKey(stationCode)) {
      final stationDetails = response.detailsVacant![stationCode]!;
      for (final coach in stationDetails.keys) {
        coachData[coach] = coachData[coach] ?? {};
        coachData[coach]!['vacant'] = stationDetails[coach]!.length;
      }
    }

    return coachData;
  }

  /// Build notification body text from coach data
  String _buildNotificationBody(Map<String, Map<String, int>> coachData) {
    final List<String> coachSummaries = [];

    for (final coach in coachData.keys) {
      final data = coachData[coach]!;
      final onboarding = data['onboarding'] ?? 0;
      final offboarding = data['offboarding'] ?? 0;
      final vacant = data['vacant'] ?? 0;

      coachSummaries.add('$coach: +$onboarding/-$offboarding (${vacant}v)');
    }

    return coachSummaries.join(', ');
  }

  /// Clear notification history (useful for testing)
  void clearNotificationHistory() {
    _notifiedStations.clear();
    _lastNotificationTime.clear();

    if (kDebugMode) {
      print('🧹 Cleared notification history');
    }
  }

  /// Get notification statistics
  Map<String, dynamic> getNotificationStats() {
    return {
      'notified_stations_count': _notifiedStations.length,
      'notified_stations': _notifiedStations.toList(),
      'proximity_threshold_km': proximityThresholdKm,
      'min_notification_interval_hours': minNotificationInterval.inHours,
    };
  }
}
