import 'package:railops/services/api_services/index.dart';

class ConfirmPassengerEmailService {
  static Future<dynamic> confirmEmail({
    required String token,
    required String email,
    required String otp,
    required String date,
  }) async {
    final body = {
      'date': date,
      'email': email,
      'otp': otp,
      'token': token,
    };

    try {
     
      final response = await ApiService.post(
        '/passenger-feedback/confirm-passenger-email/',
        body,
      );

      return response;
    } catch (e) {
      rethrow; 
    }
  }
}
