import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:railops/constants/api_constant.dart';

class UpdateFeedback {

  static Future<Map<String, dynamic>> updateFeedback({
    required int feedbackId,
    required Map<String, dynamic> data,
    required String token
  }) async {
    if (!data.containsKey('comment') || data['comment'] == null) {
        data['comment'] = ''; // Provide a default empty comment if not present
      }
    final url = Uri.parse('${ApiConstant.baseUrl}/passenger-feedback/update/$feedbackId/');
    try {
      final response = await http.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(data),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'success': true,
          'message': responseData['message'],
          'data': responseData['data'],
        };
      } else if (response.statusCode == 403) {
        return {
          'success': false,
          'message': jsonDecode(response.body)['message'],
        };
      } else if (response.statusCode == 400) {
        return {
          'success': false,
          'message': 'Invalid data',
          'errors': jsonDecode(response.body),
        };
      } else {
        return {
          'success': false,
          'message': 'Unexpected error occurred',
        };
      }
    } catch (e) {
      debugPrint('Error updating feedback: $e');
      return {
        'success': false,
        'message': 'Failed to update feedback. Please try again later.',
      };
    }
  }
}
