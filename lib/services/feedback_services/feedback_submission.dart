import 'dart:convert';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_service.dart';

class SubmitPassengerFeedback {
  static Future<Map<String, dynamic>> submitFeedback({
    required String token,
    required String date,
    required String passengerName,
    String? mobileNo,
    String? pnrNo,
    String? email,
    String? verificationStatus,
    String? feedbackValue1,
    String? feedbackValue2,
    String? feedbackValue3,
    String? feedbackValue4,
    String? feedbackValue5,
    String? feedbackValue6,
    String? feedbackValue7,
    String? feedbackValue8,
    String? coach,
    int? berthNo,
    required String trainNumber,
    required List<Uint8List> pnrImages,
    required List<Uint8List> imageFiles,
    required List<Uint8List> videoFiles,
    String? status,
    String? comment,
    String? category,
  }) async {
    try {
      // Create a multipart request
      var request = http.MultipartRequest(
          'POST', Uri.parse('${ApiConstant.baseUrl}/passenger-feedback/add/'));

      // Add authorization header
      request.headers['Authorization'] = 'Bearer $token';

      // Add form fields
      Map<String, String> fields = {
        'date': date,
        'passenger_name': passengerName,
        'verification_status': verificationStatus!,
        'status': status!,
        'train_number': trainNumber,
      };

      if (mobileNo != null && mobileNo.isNotEmpty) {
        fields['mobile_no'] = mobileNo;
      }
      if (pnrNo != null && pnrNo.isNotEmpty) {
        fields['pnr_no'] = pnrNo;
      }
      if (email != null && email.isNotEmpty) {
        fields['email'] = email;
      }
      if (feedbackValue1 != null) {
        fields['feedback_value_1'] = feedbackValue1;
      }
      if (feedbackValue2 != null) {
        fields['feedback_value_2'] = feedbackValue2;
      }
      if (feedbackValue3 != null) {
        fields['feedback_value_3'] = feedbackValue3;
      }
      if (feedbackValue4 != null) {
        fields['feedback_value_4'] = feedbackValue4;
      }
      if (feedbackValue5 != null) {
        fields['feedback_value_5'] = feedbackValue5;
      }
      if (feedbackValue6 != null) {
        fields['feedback_value_6'] = feedbackValue6;
      }
      if (feedbackValue7 != null) {
        fields['feedback_value_7'] = feedbackValue7;
      }
      if (feedbackValue8 != null) {
        fields['feedback_value_8'] = feedbackValue8;
      }
      if (coach != null && coach.isNotEmpty) {
        fields['coach'] = coach;
      }
      if (berthNo != null) {
        fields['berth_no'] = berthNo.toString();
      }
      if (comment != null && comment.isNotEmpty) {
        fields['comment'] = comment;
      }
      if (category != null && category.isNotEmpty) {
        fields['category'] = category;
      }

      request.fields.addAll(fields);

      // Add PNR images
      for (int i = 0; i < pnrImages.length; i++) {
        final pnrImage = pnrImages[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'pnr_images',
          pnrImage,
          filename: 'pnr_image_$i.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        request.files.add(multipartFile);
      }

      // Add feedback images
      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'image_file',
          imageFile,
          filename: 'feedback_image_$i.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        request.files.add(multipartFile);
      }

      // Add feedback videos
      for (int i = 0; i < videoFiles.length; i++) {
        final videoFile = videoFiles[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'video_file',
          videoFile,
          filename: 'feedback_video_$i.mp4',
          contentType: MediaType('video', 'mp4'),
        );
        request.files.add(multipartFile);
      }

      // Send the request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      // Handle the response
      if (response.statusCode == 200 || response.statusCode == 201) {
        Map<String, dynamic> responseBody = jsonDecode(response.body);
        return {
          'success': true,
          'message':
              responseBody['message'] ?? 'Feedback submitted successfully',
          'data': responseBody['data'] ?? {}
        };
      } else {
        Map<String, dynamic> errorBody = {};
        try {
          errorBody = jsonDecode(response.body);
          return {
            'success': false,
            'message': errorBody['message'] ?? 'Failed to submit feedback'
          };
        } catch (e) {
          return {
            'success': false,
            'message': 'Failed to submit feedback: ${response.statusCode}'
          };
        }
      }
    } catch (e) {
      return {'success': false, 'message': 'Error: ${e.toString()}'};
    }
  }

  static Future<Map<String, dynamic>> rmSubmitFeedback({
      required String token,
      required String date,
      required String passengerName,
      String? mobileNo,
      String? pnrNo,
      String? coach,
      String? email,
      int? berthNo,
      required String trainNumber,
      String? status,
      String? remarks,
      String? category,
      String? crnNo,
      required String issue,
      required String subIssue,
      String? marks,
      required bool resolved,
      String? verificationStatus,
      required List<Uint8List> pnrImages,
      required List<Uint8List> imageFiles,
      required List<Uint8List> videoFiles,
      
   }) async {
    try {
      // Create a multipart request
      var request = http.MultipartRequest(
          'POST', Uri.parse('${ApiConstant.baseUrl}/passenger-feedback/add/'));

      // Add authorization header
      request.headers['Authorization'] = 'Bearer $token';
      String cleanIssue = issue.trim();
      String cleanSubIssue = subIssue.trim();
      Map<String, String> fields = {
        'date': date,
        'passenger_name': passengerName,
        'verification_status': verificationStatus!,
        'resolved': resolved.toString(),
        'status': status ?? '',
        'train_number': trainNumber,
        'issue_type': cleanIssue,
        'sub_issue_type': cleanSubIssue,
        'rating_out_of_10': marks!,
        'crn_no': crnNo!,
        'comment':remarks!,
      };

      if (mobileNo != null && mobileNo.isNotEmpty) {
        fields['mobile_no'] = mobileNo;
      }
      if (pnrNo != null && pnrNo.isNotEmpty) {
        fields['pnr_no'] = pnrNo;
      }
      if (coach != null && coach.isNotEmpty) {
        fields['coach'] = coach;
      }
      if (berthNo != null) {
        fields['berth_no'] = berthNo.toString();
      }
      if (category != null && category.isNotEmpty) {
        fields['category'] = category;
      }
     
      request.fields.addAll(fields);

      // Add PNR images
      for (int i = 0; i < pnrImages.length; i++) {
        final pnrImage = pnrImages[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'pnr_images',
          pnrImage,
          filename: 'pnr_image_$i.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        request.files.add(multipartFile);
      }

       // Add feedback images
      for (int i = 0; i < imageFiles.length; i++) {
        final imageFile = imageFiles[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'image_file',
          imageFile,
          filename: 'feedback_image_$i.jpg',
          contentType: MediaType('image', 'jpeg'),
        );
        request.files.add(multipartFile);
      }

      // Add feedback videos
      for (int i = 0; i < videoFiles.length; i++) {
        final videoFile = videoFiles[i];
        final multipartFile = http.MultipartFile.fromBytes(
          'video_file',
          videoFile,
          filename: 'feedback_video_$i.mp4',
          contentType: MediaType('video', 'mp4'),
        );
        request.files.add(multipartFile);
      }


      // Send the request
      var streamedResponse = await request.send();
      var response = await http.Response.fromStream(streamedResponse);

      // Handle the response
      if (response.statusCode == 200 || response.statusCode == 201) {
        Map<String, dynamic> responseBody = jsonDecode(response.body);
        return {
          'success': true,
          'message':
              responseBody['message'] ?? 'Feedback submitted successfully',
          'data': responseBody['data'] ?? {}
        };
      } else {
        Map<String, dynamic> errorBody = {};
        try {
          errorBody = jsonDecode(response.body);
          return {
            'success': false,
            'message': errorBody['message'] ?? 'Failed to submit feedback'
          };
        } catch (e) {
          return {
            'success': false,
            'message': 'Failed to submit feedback: ${response.statusCode}'
          };
        }
      }
    } catch (e) {
      return {'success': false, 'message': 'Error: ${e.toString()}'};
    }
  }

  static Future<Map<String, dynamic>> fetchIssues() async {
    try {
      // Using the provided ApiService.get method
      final response =
          await ApiService.get('/passenger-feedback/rm-issues/', {});

      if (response is Map<String, dynamic>) {
        return response;
      } else if (response != null && response.statusCode != null) {
        if (response.statusCode == 200) {
          return jsonDecode(response.body);
        } else {
          throw Exception(
              "Failed to load issues: Status code ${response.statusCode}");
        }
      } else {
        throw Exception("Unexpected response format");
      }
    } catch (e) {
      print("Error in fetchIssues: $e");
      throw Exception("Error fetching issues: $e");
    }
  }
}
