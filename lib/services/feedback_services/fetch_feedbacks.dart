import 'package:railops/services/api_services/index.dart';

class FeedbackService {
  static Future<dynamic> fetchFeedback(
      String date, String token, String trainNumber) async {
    final endpoint = '/passenger-feedback/all/$date/$trainNumber/';
    final body = {
      'token': token
    }; // Assuming you have a token for authorization
    return await ApiService.get(endpoint, body);
  }

  static Future<dynamic> fetchRmFeedback(String token) async {
    const endpoint = '/passenger-feedback/api/rm-feedback/';
    final body = {
      'token': token
    }; // Assuming you have a token for authorization
    return await ApiService.get(endpoint, body);
  }

  static Future<dynamic> getAllFeedback(String token) async {
    const endpoint = '/passenger-feedback/all/';
    final body = {'token': token};
    return await ApiService.get(endpoint, body);
  }
}
