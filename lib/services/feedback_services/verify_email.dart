import 'package:railops/services/api_services/index.dart';

class VerifyPassengerEmailService {
  static Future<dynamic> verifyEmail(String email, String token) async {
    final body = {
      'email': email,
      'token': token,
    };
    try {
      final response = await ApiService.post('/passenger-feedback/verify-passenger-email/', body);
      return response;
    } catch (e) {
      rethrow;
    }
  }
}


