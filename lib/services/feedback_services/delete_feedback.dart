import 'package:railops/services/api_services/api_service.dart';

class DeleteFeedback {

  static Future<dynamic> deleteFeedback({
    required int feedbackId,
    required String token,
  }) async {
    final String endpoint = '/passenger-feedback/delete/$feedbackId';
    final Map<String, dynamic> body = {'token': token};

    try {
      return await ApiService.delete(endpoint, body);
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to delete feedback. Please try again later.',
      };
    }
  }
}
