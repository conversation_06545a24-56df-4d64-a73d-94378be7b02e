import 'dart:async';
import 'dart:convert';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/types/edit_train_types/add_train_types.dart';
import 'package:railops/types/edit_train_types/coach_update_type.dart';
import 'package:railops/types/index.dart';
import 'package:railops/types/edit_train_types/stoppages_update_type.dart';

class AddTrainServices {
  static Future<AddTrainResponse?> addTrainDetails({
    required String token,
    int? trainNo,
    String? trainName,
    String? trainType,
    int? relatedTrain,
    String? fromStation,
    String? toStation,
    String? startTime,
    String? endTime,
    String? chartingTime,
    String? chartingDay,
    List<int>? frequency,
    List<String>? stoppagesInSequence,
    String? updown,
    String? zone,
    String? division,
    String? Depot,
    int? return_gap,
    String? in_out,
    List<String>? coaches,
  }) async {
    try {
      // Create the request body with the provided parameters
      final Map<String, dynamic> requestBody = {
        'token': token,
        'train_no': trainNo,
        'train_name': trainName,
        'train_type': trainType,
        'related_train': relatedTrain,
        'from_station': fromStation,
        'to_station': toStation,
        'start_time': startTime,
        'end_time': endTime,
        'charting_time': chartingTime,
        'charting_day': chartingDay,
        'frequency': frequency,
        'stoppages_in_sequence': stoppagesInSequence,
        'updown': updown,
        'zone': zone,
        'division': division,
        'Depot': Depot,
        'return_gap': return_gap,
        'in_out': in_out,
        "coaches": coaches,
      };
      requestBody.removeWhere((key, value) => value == null);

      const endpoint = '/api/create-train-details/';
      final response = await ApiService.post(endpoint, requestBody);
      return AddTrainResponse.fromJson(response);
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<CoachUpdateType?> addCoachByTrainNo(
      String trainNo, String coach, String token) async {
    try {
      final endpoint = '/api/add_coach/$trainNo/';

      final responseJson = await ApiService.post(endpoint, {
        "train_no": trainNo,
        "coach": coach,
        "token": token,
      });
      return responseJson != null
          ? CoachUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Error in Update Coach: $e");
      return null;
    }
  }

  static Future<CoachUpdateType?> removeCoachByTrainNo(
      String trainNo, String coach, String token) async {
    try {
      final endpoint = '/api/delete_coach/$trainNo/';

      final responseJson = await ApiService.post(endpoint, {
        "train_no": trainNo,
        "coach": coach,
        "token": token,
      });
      return responseJson != null
          ? CoachUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Error in Update Coach: $e");
      return null;
    }
  }

  static Future<StoppagesUpdateType?> addStoppagesForAttendanceByTrainNo(
      String trainNo,
      String updateType,
      String stoppageName,
      String token) async {
    try {
      final endpoint = '/api/attendance-station/$updateType/';

      final responseJson = await ApiService.post(endpoint, {
        "train_no": trainNo,
        "station": stoppageName,
        "token": token,
      });
      return responseJson != null
          ? StoppagesUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }
}
