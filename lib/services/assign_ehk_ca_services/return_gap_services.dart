import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';

class ReturnGapService {
  
  // Fetch Return Gap Data by Train ID
  static Future<ReturnGapData?> fetchReturnGapByTrain(String? trainNo) async {
    try {
      final endpoint = '/return-gap/get/$trainNo/';
      final responseJson = await ApiService.get(endpoint, {});
      return responseJson != null ? ReturnGapData.fromJson(responseJson) : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  // Add or Update Return Gap Data
  static Future<String> addOrUpdateReturnGap(ReturnGapRequest request) async {
    try {
      final response = await ApiService.post(
        '/return-gap/create-or-update/',
        request.toJson(),
      );
      print(response);
      return "added successfully";
    } on ApiException catch (e) {
      throw e.message;
    }
  }
}