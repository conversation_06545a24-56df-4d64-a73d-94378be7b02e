import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/assign_ehk_ca_types/job_chart_status_types.dart';

class JobChartStatusService {
  
  static Future<List<JobChartStatus>?> fetchJobChartStatus(
      {required String date, required String trainNumber, required String statusFor}) async {
    try {
      final endpoint = '/jobchart/get-status/?date=$date&train_number=$trainNumber&status_for=$statusFor';
      final responseJson = await ApiService.get(endpoint, {});
      return responseJson != null
          ? (responseJson as List).map((e) => JobChartStatus.fromJson(e)).toList()
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  static Future<String> addJobChartStatus(JobChartStatusRequest request) async {
    try {
      final response = await ApiService.post(
        '/jobchart/add-status/',
        request.toJson(),
      );
      print(response);
      return "Added successfully";
    } on ApiException catch (e) {
      throw e.message;
    }
  }
}