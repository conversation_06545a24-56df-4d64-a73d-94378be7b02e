import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/constants/index.dart';
import 'package:http/http.dart' as http;
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:railops/types/attendance_types/attendance_data.dart';


class JobchartImageUploadService {
  static Future<Map<String, dynamic>?> createJobChart({
    required XFile imageFile,
    required String trainNumber,
    required String date,
    required String token,
    required String latitude,
    required String longitude,
    required String uploadedFor,
  }) async {
    try {
      final endpoint = '/jobchart/create/';
      
      var request = http.MultipartRequest('POST', Uri.parse(ApiConstant.baseUrl + endpoint));
      request.headers.addAll({
        'Authorization': 'Bearer $token',
      });

      Uint8List bytes = await imageFile.readAsBytes();
      String filename = imageFile.name;

      var file = http.MultipartFile(
        'file',
        http.ByteStream.fromBytes(bytes),
        bytes.length,
        filename: filename,
      );


      request.files.add(file);

      request.fields['train_number'] = trainNumber;
      request.fields['date'] = date;
      request.fields['latitude'] = latitude;
      request.fields['longitude'] = longitude;
      request.fields['uploaded_for'] = uploadedFor;



      var response = await request.send();
      
      if (response.statusCode == 200) {
        var responseData = await response.stream.bytesToString();
        return json.decode(responseData);
      } 
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      rethrow;
    }
  }


  static Future<List<JobchartType>?> fetchJobChart({
    String? date,
    String? trainNumber,
    String? token,
    String ? uploadedFor,
  }) async {
    try {
      final endpoint = "/jobchart/get/?train_number=$trainNumber&date=$date&uploaded_for=$uploadedFor";
    
      final body = <String, String>{};
      body["token"] = token!;

      final responseJson = await ApiService.get(endpoint, body);
      if (responseJson != null && responseJson is List) {
        return responseJson
            .map((json) => JobchartType.fromJson(json))
            .toList();
      }
      return null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }


  static Future<bool> deleteJobChart({
    required int id,
    required String token,
  }) async {
    try {
      final endpoint = "/jobchart/delete/$id/";

      final body = {
        'token': token,
      };

      final response = await ApiService.delete(endpoint, body);

      if (response != null && response['message'] == 'JobChart deleted successfully.') {
        return true;
      } else {
        return false;
      }
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return false;
    }
  }


}
