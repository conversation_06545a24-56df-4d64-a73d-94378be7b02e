import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/edit_train_types/coach_update_type.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart';
import 'package:railops/types/edit_train_types/stoppages_update_type.dart';

class EditTrainServices {
  static Future<EditTrainsData?> fetchTrainDetailsByTrainNo(
      String trainNo) async {
    try {
      final endpoint = '/api/get-train-details/$trainNo/';
      final responseJson = await ApiService.get(endpoint, {});
      return responseJson != null
          ? EditTrainsData.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  static Future<StoppagesUpdateType?> fetchStoppagesForAttendanceByTrainNo(
      String trainNo, String token) async {
    try {
      final endpoint = '/api/attendance-stations?train_no=$trainNo';
      final responseJson = await ApiService.get(endpoint, {});
      return responseJson != null
          ? StoppagesUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  static Future<StoppagesUpdateType?> updateStoppagesForAttendanceByTrainNo(
      String trainNo,
      String updateType,
      String stoppageName,
      String token) async {
    try {
      final endpoint = '/api/attendance-station/$updateType/';

      final responseJson = await ApiService.post(endpoint, {
        "train_no": trainNo,
        "station": stoppageName,
        "token": token,
      });
      return responseJson != null
          ? StoppagesUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Error hjdfbcvhjfedvb: $e");
      return null;
    }
  }

  static Future<CoachUpdateType?> fetchCoachesByTrainNo(String trainNo) async {
    try {
      final endpoint = '/api/show_coaches/$trainNo/';
      final responseJson = await ApiService.get(endpoint, {});
      return responseJson != null
          ? CoachUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  static Future<CoachUpdateType?> updateCoachByTrainNo(String trainNo,
      String updateType,String coach, String token) async {
    try {
      final endpoint = '/api/$updateType/$trainNo/';

      final responseJson = await ApiService.post(endpoint, {
        "train_no": trainNo,
        "coach": coach,
        "token": token,
      });
      return responseJson != null
          ? CoachUpdateType.fromJson(responseJson)
          : null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      print("Error in Update Coach: $e");
      return null;
    }
  }

  static Future<String> updateTrainDetails(
      String trainNo, Map<String, dynamic> request) async {
    try {
      final endpoint = '/api/update-train-details/$trainNo/';
      final response = await ApiService.put(endpoint, request);
      return "Updated successfully";
    } on ApiException catch (e) {
      throw e.message;
    }
  }
}
