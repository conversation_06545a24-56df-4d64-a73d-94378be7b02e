import 'package:flutter/material.dart';

void bottomModalWidget(BuildContext context, String message) {
  showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(16.0)),
    ),
    builder: (BuildContext context) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: MediaQuery.of(context).size.width * 0.8,
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    message,
                    style: const TextStyle(fontSize: 12.0, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.start,
                  ),
                  const SizedBox(height: 10),
                  <PERSON><PERSON>(
                    alignment: AlignmentDirectional.bottomEnd,
                    child:ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        shape:const RoundedRectangleBorder(
                          borderRadius: BorderRadius.all(Radius.circular(5)), 
                        ),
                        backgroundColor: Colors.red.shade100,
                        minimumSize: const Size(50, 50), 
                        padding: const EdgeInsets.all(15.0), 
                      ),
                      child: const Icon(Icons.close),
                    )
                  )
                  ,
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}
