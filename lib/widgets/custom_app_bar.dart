import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:railops/screens/attendance/upload_manager.dart';

class UploadStatusIndicator extends StatelessWidget {
  final VoidCallback? onViewDetails;

  const UploadStatusIndicator({
    Key? key,
    this.onViewDetails,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use Consumer to listen to UploadManager changes
    return Consumer<UploadManager>(
      builder: (context, uploadManager, child) {
        final bool isUploading = uploadManager.uploadCount > 0;
        final bool isCompressing = uploadManager.isCompressing;
        final int uploadCount = uploadManager.uploadCount;
        final double progress = uploadManager.averageProgress;

        if (!isUploading && !isCompressing && uploadCount == 0) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: onViewDetails,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width *
                    0.25), // Add max width constraint
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isUploading || isCompressing)
                  SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      value: progress > 0 ? progress : null,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                  ),
                const SizedBox(width: 4),
                Flexible(
                  // Make text flexible
                  child: Text(
                    isCompressing
                        ? 'Compressing...'
                        : uploadCount > 0
                            ? '$uploadCount ${uploadCount == 1 ? 'upload' : 'uploads'}'
                            : 'Preparing...',
                    style: TextStyle(
                      fontSize: 11, // Slightly smaller font
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                    overflow:
                        TextOverflow.ellipsis, // Add ellipsis for overflow
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onViewUploads;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.onViewUploads,
  }) : super(key: key);

  @override
  _CustomAppBarState createState() => _CustomAppBarState();

  @override
  Size get preferredSize {
    double height = kToolbarHeight;
    if (MediaQueryData.fromWindow(WidgetsBinding.instance.window).size.width <
        600) {
      height = kToolbarHeight + 10;
    }
    return Size.fromHeight(height);
  }
}

class _CustomAppBarState extends State<CustomAppBar> {
  String appVersion = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        appVersion = packageInfo.version;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final uploadManager = Provider.of<UploadManager>(context);
    return AppBar(
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1.0),
        child: Container(
          color: Colors.black26,
          height: 1.0,
        ),
      ),
      backgroundColor: Colors.white,
      leading: Builder(
        builder: (BuildContext context) {
          return IconButton(
            icon: const Icon(Icons.menu, color: Colors.black87),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          );
        },
      ),
      title: Consumer<UserModel>(
        builder: (context, userModel, child) {
          final roleLetter = _getRoleLetter(userModel.userType);
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              LayoutBuilder(
                builder: (context, constraints) {
                  return const Text(
                    'RailOps',
                    style: TextStyle(
                      color: Colors.black87,
                      fontSize: 15.0,
                    ),
                  );
                },
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth < 600) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize
                          .min, // Make the row take only needed space
                      children: [
                        Flexible(
                          // Make the username flexible to allow truncation
                          child: Text(
                            userModel.userName.length > 11
                                ? userModel.userName.substring(
                                    0, userModel.userName.length - 11)
                                : userModel.userName,
                            style: const TextStyle(
                              color: Colors.black87,
                              fontSize: 14.0,
                            ),
                            overflow: TextOverflow
                                .ellipsis, // Add ellipsis for overflow
                          ),
                        ),
                        if (roleLetter.isNotEmpty) ...[
                          const SizedBox(width: 5),
                          Text(
                            '[$roleLetter]',
                            style: const TextStyle(
                              color: Colors.black87,
                              fontSize: 14.0,
                            ),
                          ),
                        ],
                        const SizedBox(width: 5),
                        Text(
                          '[${DateFormat('dd MMM yyyy').format(DateTime.now())}]',
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    );
                  } else {
                    return Container();
                  }
                },
              ),
            ],
          );
        },
      ),
      actions: [
        // Wrap in Flexible to prevent overflow
        Flexible(
          child: Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: UploadStatusIndicator(
              onViewDetails: () =>
                  _showUploadStatusDetails(context, uploadManager),
            ),
          ),
        ),

        // Keep version info and menu constant
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
                height: 38,
                child: PopupMenuButton<String>(
                  icon: const Icon(Icons.more_horiz, color: Colors.black87),
                  offset: const Offset(0, kToolbarHeight),
                  onSelected: (String result) async {
                    if (result == 'Profile') {
                      Navigator.of(context).pushNamed('/edit_profile');
                    }
                  },
                  itemBuilder: (BuildContext context) {
                    return {'Profile'}.map((String choice) {
                      return PopupMenuItem<String>(
                        value: choice,
                        child: Row(
                          children: [
                            if (choice == 'Profile') const Icon(Icons.person),
                            const SizedBox(width: 10),
                            Text(choice),
                          ],
                        ),
                      );
                    }).toList();
                  },
                )),
            if (appVersion.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(right: 3.0),
                child: Text(
                  'v$appVersion',
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 11.0,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  String _getRoleLetter(String userType) {
    switch (userType) {
      case 'railway admin':
        return 'A';
      case 'railway manager':
        return 'M';
      case 'chi_sm':
        return 'S';
      case 's2 admin':
        return 's2A';
      case 'railway officer':
        return 'RO';
      default:
        return userType.isNotEmpty ? userType[0].toUpperCase() : '';
    }
  }

  void _showUploadStatusDetails(
      BuildContext context, UploadManager uploadManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upload Status'),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (uploadManager.isCompressing)
                const ListTile(
                  leading: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  title: Text('Compressing image'),
                  dense: true,
                ),
              if (uploadManager.backgroundTasks.isNotEmpty)
                const Padding(
                  padding: EdgeInsets.only(top: 8.0, bottom: 4.0),
                  child: Text(
                    'Active Uploads:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ...uploadManager.backgroundTasks.entries
                  .map((entry) => ListTile(
                        leading: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            value: entry.value.progress,
                          ),
                        ),
                        title: Text('Upload ${entry.key.substring(0, 6)}...'),
                        subtitle: Text(
                            '${(entry.value.progress * 100).toStringAsFixed(0)}% complete'),
                        dense: true,
                      ))
                  .toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
