import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

void _launchPhoneDialer(String phoneNumber) async {
  final Uri uri = Uri(scheme: 'tel', path: phoneNumber);
  if (await canLaunchUrl(uri)) {
    await launchUrl(uri);
  } else {
    throw 'Could not launch dialer for $phoneNumber';
  }
}


void showCallModal(BuildContext context, String userString) {
  final parts = userString.split('_');
  final name = parts.first;
  final numbers = parts.sublist(1);

  showModalBottomSheet(
    context: context,
    builder: (BuildContext context) {
      return Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: numbers.map((number) {
              final label = '$name\_$number';
              return Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.call, color: Colors.green),
                    onPressed: () {
                      Navigator.pop(context);
                      _launchPhoneDialer(number);
                    },
                    tooltip: 'Call $label',
                  ),
                ],
              );
            }).toList(),
          ),
          Column(
            children: numbers.map((number) {
              final label = '$name\_$number';
              return Column(
                children: [
                  Text(
                    'Call $label',
                    style: const TextStyle(
                        fontSize: 16.0, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 10),
                ],
              );
            }).toList(),
          ),
        ]),
      );
    },
  );
}
