import 'package:flutter/material.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/size_config.dart';


class AppTextField extends StatelessWidget {
  AppTextField({
    super.key,
    required this.controller,
    this.label,
    this.suffixIcon,
    this.prefixIcon,
    this.labelStyle,
    this.textInputType,
    this.maxLength,
    this.obscureText,
    this.validators,
    this.errorText,
    this.onChange,
    this.hint,
    this.hintStyle,
    this.fieldRadius,
    this.readOnly,
    this.onTap,
  });

  TextEditingController controller;
  Widget? label;

  String? hint;
  Widget? prefixIcon;
  Widget? suffixIcon;
  TextStyle? labelStyle;
  TextStyle? hintStyle;
  TextInputType? textInputType;
  int? maxLength;
  bool? obscureText;
  String? Function(String?)? validators;
  String? errorText;
  Function(String)? onChange;
  Function()? onTap;
  double? fieldRadius;
  bool? readOnly;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      readOnly: readOnly ?? false,
      controller: controller,
      keyboardType: textInputType,
      style: const TextStyle(color: Colors.black),
      maxLength: maxLength,
      cursorColor: Colors.blue,
      obscureText: obscureText ?? false,
      validator: validators,
      onChanged: onChange,
      onTap: onTap,
      decoration: InputDecoration(
        fillColor:
            readOnly != null && readOnly! ? Colors.grey.shade200 : kWhiteColor,
        filled: true,
        constraints: BoxConstraints(maxWidth: SizeConfig.screenWidth * 1),
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        labelStyle: TextStyle(
          color: Colors.black,
          fontSize: responsiveFont(14),
        ),
        border: OutlineInputBorder(
          borderRadius:
              BorderRadius.circular(fieldRadius ?? 8), // Rounded corners
          borderSide: const BorderSide(
            color: kTextFieldBorder, // Border color
            width: 1.5, // Border width
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius:
              BorderRadius.circular(fieldRadius ?? 8), // Rounded corners
          borderSide: const BorderSide(
            color: kTextFieldBorder, // Border color
            width: 1.5, // Border width
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(fieldRadius ?? 8),
          borderSide: const BorderSide(
            color: Colors.blue,
            width: 1.0,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(fieldRadius ?? 8),
          borderSide: const BorderSide(
            color: kTextFieldBorder,
            width: 1.0,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(fieldRadius ?? 8),
          borderSide: const BorderSide(
            color: kTextFieldBorder,
            width: 1.0,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12.0,
          horizontal: 16.0,
        ),
        label: label,
        hintText: hint,
        hintStyle: hintStyle,
      ),
    );
  }
}
