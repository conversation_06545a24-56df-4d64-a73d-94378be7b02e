import 'package:flutter/material.dart';

import 'package:intl/intl.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/size_config.dart';

class AppDatefield extends StatefulWidget {
  final TextEditingController controller;
  final String? label;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final String? hint;
  final double? fieldRadius;
  final bool? readOnly;
  final Function(DateTime)? onDateSelected;

  AppDatefield({
    super.key,
    required this.controller,
    this.label,
    this.prefixIcon,
    this.suffixIcon,
    this.labelStyle,
    this.hintStyle,
    this.hint,
    this.fieldRadius,
    this.readOnly,
    this.onDateSelected,
  });

  @override
  _DateFieldState createState() => _DateFieldState();
}

class _DateFieldState extends State<AppDatefield> {
  Future<void> _selectDate(BuildContext context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    
    if (pickedDate != null) {
      String formattedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
      widget.controller.text = formattedDate;
      if (widget.onDateSelected != null) {
        widget.onDateSelected!(pickedDate);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      readOnly: true,
      controller: widget.controller,
      style: const TextStyle(color: Colors.black),
      cursorColor: Colors.white,
      onTap: () => _selectDate(context),
      decoration: InputDecoration(
        fillColor: Colors.white,
        filled: true,
        constraints: BoxConstraints(maxWidth: SizeConfig.screenWidth * 0.9),
        prefixIcon: widget.prefixIcon ?? Icon(Icons.calendar_today, color: Colors.black54),
        suffixIcon: widget.suffixIcon,
        labelText: widget.label,
        labelStyle: widget.labelStyle ?? TextStyle(color: Colors.black, fontSize: responsiveFont(14)),
        hintText: widget.hint ?? "Select Date",
        hintStyle: widget.hintStyle,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.fieldRadius ?? 8),
          borderSide: BorderSide(color: kTextFieldBorder, width: 1.5),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.fieldRadius ?? 8),
          borderSide: BorderSide(color: kTextFieldBorder, width: 1.5),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(widget.fieldRadius ?? 8),
          borderSide: BorderSide(color: Colors.blue, width: 1.0),
        ),
        contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
      ),
    );
  }
}
