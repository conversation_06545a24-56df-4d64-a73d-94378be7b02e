import 'package:flutter/material.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/size_config.dart';


class AppDropdownfield extends StatelessWidget {
  final String label;
  final List<String> items;
  final String? selectedItem;
  final Function(String?) onChanged;
  final TextStyle? labelStyle;
  final double? fieldRadius;
  final bool readOnly;
  final IconData? icon;
  final bool isLoading;

  const AppDropdownfield({
    super.key,
    required this.label,
    required this.items,
    this.selectedItem,
    required this.onChanged,
    this.labelStyle,
    this.fieldRadius,
    this.readOnly = false,
    this.icon,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButtonFormField<String>(
        value: selectedItem,
        decoration: InputDecoration(
          fillColor: kWhiteColor,
          filled: true,
          labelText: label,
          labelStyle: labelStyle ?? TextStyle(color: kBlackColor, fontSize: responsiveFont(14)),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(fieldRadius ?? 8),
            borderSide: BorderSide(color: kTextFieldBorder, width: responsiveWidth(1.0)),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(fieldRadius ?? 8),
            borderSide: BorderSide(color: kTextFieldBorder, width: responsiveWidth(1.0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(fieldRadius ?? 8),
            borderSide: BorderSide(color: kBlueColor, width: responsiveWidth(1.0)),
          ),
          contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
          suffixIcon: isLoading
              ? const Padding(
                  padding: EdgeInsets.all(12.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2.0),
                  ),
                )
              : (icon != null ? Icon(icon, color: kBlackColor) : null),
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(item, style: const TextStyle(color: kBlackColor)),
          );
        }).toList(),
        onChanged: readOnly ? null : onChanged,
        menuMaxHeight: 300, 
      ),
    );
  }
}
