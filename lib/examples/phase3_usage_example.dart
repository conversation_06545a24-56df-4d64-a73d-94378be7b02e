import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/services/background_services/notification_background_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

/// Phase 3: Comprehensive usage examples for enhanced notification integration
/// 
/// This file demonstrates how to use the new Phase 3 features:
/// - Enhanced UpcomingStationService with automatic notification processing
/// - Background notification processing service
/// - Seamless integration between foreground and background processing
class Phase3UsageExample {
  
  /// Example 1: Basic usage with automatic notification processing
  /// This replaces the existing UpcomingStationService calls with enhanced functionality
  static Future<void> basicUsageWithNotifications() async {
    try {
      // Get current location
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      // Enhanced API call with automatic notification processing
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        // Phase 3.1: Enable automatic notification processing
        enableNotificationProcessing: true,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableStationApproachingNotifications: false, // Avoid spam
        enableLocationBasedScheduling: true, // Enable background processing
      );
      
      if (kDebugMode) {
        print('Phase3Example: Successfully fetched data for train ${response.trainNumber}');
        print('Phase3Example: Automatic notifications processed');
        print('Phase3Example: Background processing started');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in basic usage: $e');
      }
    }
  }
  
  /// Example 2: Backward compatibility - existing code continues to work
  static Future<void> backwardCompatibilityExample() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      // Existing code works without any changes
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        // No Phase 3 parameters - works exactly as before
      );
      
      if (kDebugMode) {
        print('Phase3Example: Backward compatibility maintained for train ${response.trainNumber}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in backward compatibility example: $e');
      }
    }
  }
  
  /// Example 3: Manual background processing control
  static Future<void> manualBackgroundProcessingControl() async {
    try {
      // First, get train data using the standard API call
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        enableNotificationProcessing: false, // Handle notifications manually
      );
      
      // Manually start background processing with custom configuration
      await NotificationBackgroundService.startBackgroundProcessing(
        trainData: response,
        currentLat: position.latitude.toString(),
        currentLng: position.longitude.toString(),
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableProximityAlerts: true,
        proximityThresholdKm: 1.5, // Custom proximity threshold
      );
      
      if (kDebugMode) {
        print('Phase3Example: Manual background processing started with custom config');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in manual background processing: $e');
      }
    }
  }
  
  /// Example 4: Monitoring and controlling background processing
  static Future<void> backgroundProcessingMonitoring() async {
    try {
      // Check current background processing status
      final status = await UpcomingStationService.getBackgroundNotificationStatus();
      
      if (kDebugMode) {
        print('Phase3Example: Background processing status:');
        print('  - Active: ${status['is_active']}');
        print('  - Has train data: ${status['has_train_data']}');
        print('  - Has config: ${status['has_config']}');
        print('  - Last updated: ${status['last_updated']}');
      }
      
      if (status['is_active'] == true) {
        if (kDebugMode) {
          print('Phase3Example: Background processing is currently active');
        }
        
        // Optionally stop background processing
        // await UpcomingStationService.stopBackgroundNotificationProcessing();
        // print('Phase3Example: Background processing stopped');
      } else {
        if (kDebugMode) {
          print('Phase3Example: No background processing currently active');
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error monitoring background processing: $e');
      }
    }
  }
  
  /// Example 5: Selective notification configuration
  static Future<void> selectiveNotificationConfiguration() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      // Configure notifications selectively based on user preferences
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        enableNotificationProcessing: true,
        // Selective configuration
        enableBoardingNotifications: true,  // Enable boarding alerts
        enableOffBoardingNotifications: false, // Disable off-boarding alerts
        enableStationApproachingNotifications: false, // Disable approach alerts
        enableLocationBasedScheduling: true, // Enable background processing
      );
      
      if (kDebugMode) {
        print('Phase3Example: Selective notification configuration applied');
        print('Phase3Example: Only boarding notifications enabled');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in selective configuration: $e');
      }
    }
  }
  
  /// Example 6: Complete journey workflow
  static Future<void> completeJourneyWorkflow() async {
    try {
      if (kDebugMode) {
        print('Phase3Example: Starting complete journey workflow');
      }
      
      // Step 1: Initialize background notification service
      await NotificationBackgroundService.initialize();
      
      // Step 2: Get current location
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      // Step 3: Fetch train details with full notification processing
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        enableNotificationProcessing: true,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableStationApproachingNotifications: false,
        enableLocationBasedScheduling: true,
      );
      
      if (kDebugMode) {
        print('Phase3Example: Journey started for train ${response.trainNumber}');
        print('Phase3Example: Stations: ${response.stations.join(', ')}');
      }
      
      // Step 4: Monitor background processing during journey
      await Future.delayed(const Duration(seconds: 5)); // Simulate some time
      
      final status = await UpcomingStationService.getBackgroundNotificationStatus();
      if (kDebugMode) {
        print('Phase3Example: Background processing active: ${status['is_active']}');
      }
      
      // Step 5: End journey and cleanup (when user reaches destination)
      // await UpcomingStationService.stopBackgroundNotificationProcessing();
      // print('Phase3Example: Journey ended, background processing stopped');
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in complete journey workflow: $e');
      }
    }
  }
  
  /// Example 7: Error handling and recovery
  static Future<void> errorHandlingExample() async {
    try {
      // Demonstrate robust error handling
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      try {
        final response = await UpcomingStationService.fetchUpcomingStationDetails(
          lat: position.latitude.toString(),
          lng: position.longitude.toString(),
          token: 'invalid_token', // This will cause an API error
          enableNotificationProcessing: true,
        );
        
        if (kDebugMode) {
          print('Phase3Example: Unexpected success with invalid token');
        }
        
      } catch (apiError) {
        if (kDebugMode) {
          print('Phase3Example: API error handled gracefully: $apiError');
        }
        
        // Even if API fails, we can still check background processing status
        final status = await UpcomingStationService.getBackgroundNotificationStatus();
        if (kDebugMode) {
          print('Phase3Example: Background processing status after API error: ${status['is_active']}');
        }
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in error handling example: $e');
      }
    }
  }
  
  /// Example 8: Performance optimization
  static Future<void> performanceOptimizationExample() async {
    try {
      // For performance-critical scenarios, disable certain features
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.medium,
        ),
      );
      
      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        token: 'user_auth_token',
        enableNotificationProcessing: true,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: false, // Reduce processing
        enableStationApproachingNotifications: false, // Reduce processing
        enableLocationBasedScheduling: false, // Disable background processing for performance
      );
      
      if (kDebugMode) {
        print('Phase3Example: Performance-optimized configuration applied');
        print('Phase3Example: Minimal notification processing for train ${response.trainNumber}');
      }
      
    } catch (e) {
      if (kDebugMode) {
        print('Phase3Example: Error in performance optimization example: $e');
      }
    }
  }
}
