import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:railops/models/notification_model.dart';
import 'package:railops/models/notification_preferences_model.dart';
import 'package:railops/services/firebase_messaging_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

class NotificationProvider extends ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  NotificationPreferencesModel _preferences =
      NotificationPreferencesModel.defaultPreferences();

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  int get unreadCount =>
      _notifications.where((notification) => !notification.isRead).length;
  NotificationPreferencesModel get preferences => _preferences;

  // Enhanced getters for categorized notifications
  List<NotificationModel> get onboardingNotifications => _notifications
      .where((notification) =>
          _getNotificationType(notification) != NotificationType.general)
      .toList();

  List<NotificationModel> get generalNotifications => _notifications
      .where((notification) =>
          _getNotificationType(notification) == NotificationType.general)
      .toList();

  int get onboardingUnreadCount => onboardingNotifications
      .where((notification) => !notification.isRead)
      .length;

  int get generalUnreadCount =>
      generalNotifications.where((notification) => !notification.isRead).length;

  NotificationProvider() {
    loadNotifications();
    loadPreferences();
  }

  Future<void> loadNotifications() async {
    _isLoading = true;
    notifyListeners();

    try {
      final notificationHistory =
          await FirebaseMessagingService().getNotificationHistory();

      _notifications = notificationHistory
          .map((notification) => NotificationModel.fromJson(notification))
          .toList();

      // Sort notifications by timestamp (newest first)
      _notifications.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // Load read status
      await _loadReadStatus();
    } catch (e) {
      print('Error loading notifications: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> _loadReadStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readStatusJson = prefs.getString('notifications_read_status');

      if (readStatusJson != null) {
        final readStatus = json.decode(readStatusJson) as Map<String, dynamic>;

        for (int i = 0; i < _notifications.length; i++) {
          final notification = _notifications[i];
          if (notification.id != null &&
              readStatus.containsKey(notification.id)) {
            _notifications[i] =
                notification.copyWith(isRead: readStatus[notification.id]);
          }
        }
      }
    } catch (e) {
      print('Error loading read status: $e');
    }
  }

  Future<void> _saveReadStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readStatus = <String, bool>{};

      for (final notification in _notifications) {
        if (notification.id != null) {
          readStatus[notification.id!] = notification.isRead;
        }
      }

      await prefs.setString(
          'notifications_read_status', json.encode(readStatus));
    } catch (e) {
      print('Error saving read status: $e');
    }
  }

  Future<void> markAsRead(String notificationId) async {
    final index = _notifications
        .indexWhere((notification) => notification.id == notificationId);

    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();

      await _saveReadStatus();
    }
  }

  Future<void> markAllAsRead() async {
    _notifications = _notifications
        .map((notification) => notification.copyWith(isRead: true))
        .toList();
    notifyListeners();

    await _saveReadStatus();
  }

  Future<void> clearNotifications() async {
    _notifications = [];
    notifyListeners();

    await FirebaseMessagingService().clearNotificationHistory();

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('notifications_read_status');
  }

  Future<void> refreshNotifications() async {
    await loadNotifications();
  }

  /// Load notification preferences
  Future<void> loadPreferences() async {
    try {
      final loadedPreferences =
          await NotificationPreferencesService.loadPreferences();
      _preferences = loadedPreferences;
      notifyListeners();
    } catch (e) {
      print('Error loading notification preferences: $e');
    }
  }

  /// Update notification preferences
  Future<void> updatePreferences(
      NotificationPreferencesModel newPreferences) async {
    try {
      final success =
          await NotificationPreferencesService.savePreferences(newPreferences);
      if (success) {
        _preferences = newPreferences;
        notifyListeners();
      }
    } catch (e) {
      print('Error updating notification preferences: $e');
    }
  }

  /// Get notifications filtered by type
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications
        .where((notification) => _getNotificationType(notification) == type)
        .toList();
  }

  /// Get notifications filtered by coach type
  List<NotificationModel> getNotificationsByCoachType(String coachType) {
    return _notifications
        .where((notification) =>
            _isCoachSpecificNotification(notification, coachType))
        .toList();
  }

  /// Determine notification type from notification data
  NotificationType _getNotificationType(NotificationModel notification) {
    final data = notification.data;

    // Check for onboarding-specific data keys
    if (data.containsKey('notification_type')) {
      final typeString = data['notification_type'] as String?;
      if (typeString != null) {
        return NotificationTypeExtension.fromString(typeString);
      }
    }

    // Check for onboarding-related data keys
    if (data.containsKey('train_number') ||
        data.containsKey('station_name') ||
        data.containsKey('coach_number') ||
        data.containsKey('onboarding_data')) {
      // Determine specific onboarding type based on content
      final title = notification.title?.toLowerCase() ?? '';
      final body = notification.body?.toLowerCase() ?? '';

      if (title.contains('boarding') || body.contains('boarding')) {
        return NotificationType.boardingAlert;
      } else if (title.contains('approaching') ||
          body.contains('approaching')) {
        return NotificationType.stationApproach;
      } else if (title.contains('off') || body.contains('alighting')) {
        return NotificationType.offBoardingAlert;
      } else if (title.contains('proximity') || body.contains('near')) {
        return NotificationType.proximityAlert;
      } else if (title.contains('coach') || body.contains('coach')) {
        return NotificationType.coachSpecific;
      } else {
        return NotificationType.onboarding;
      }
    }

    return NotificationType.general;
  }

  /// Check if notification is coach-specific and matches the given coach type
  bool _isCoachSpecificNotification(
      NotificationModel notification, String coachType) {
    final data = notification.data;

    if (data.containsKey('coach_number')) {
      final coachNumber = data['coach_number'] as String?;
      if (coachNumber != null) {
        // Extract coach type from coach number (e.g., "AC1-1" -> "AC1")
        final coachTypeFromNumber = coachNumber.split('-').first.toUpperCase();
        return coachTypeFromNumber == coachType.toUpperCase();
      }
    }

    if (data.containsKey('coach_type')) {
      final notificationCoachType = data['coach_type'] as String?;
      return notificationCoachType?.toUpperCase() == coachType.toUpperCase();
    }

    // Check in notification text as fallback
    final title = notification.title?.toUpperCase() ?? '';
    final body = notification.body?.toUpperCase() ?? '';
    final upperCoachType = coachType.toUpperCase();

    return title.contains(upperCoachType) || body.contains(upperCoachType);
  }

  /// Check if notification should be shown based on preferences
  bool shouldShowNotification(NotificationModel notification) {
    final type = _getNotificationType(notification);

    // Check if this notification type is enabled
    if (!_preferences.shouldShowNotificationType(type)) {
      return false;
    }

    // Check coach-specific filtering
    if (type == NotificationType.coachSpecific &&
        _preferences.enableCoachSpecificFiltering) {
      final data = notification.data;
      if (data.containsKey('coach_type')) {
        final coachType = data['coach_type'] as String?;
        if (coachType != null &&
            !_preferences.shouldShowForCoachType(coachType)) {
          return false;
        }
      }
    }

    return true;
  }

  /// Get filtered notifications based on current preferences
  List<NotificationModel> get filteredNotifications {
    return _notifications
        .where((notification) => shouldShowNotification(notification))
        .toList();
  }

  /// Get filtered onboarding notifications
  List<NotificationModel> get filteredOnboardingNotifications {
    return onboardingNotifications
        .where((notification) => shouldShowNotification(notification))
        .toList();
  }
}
