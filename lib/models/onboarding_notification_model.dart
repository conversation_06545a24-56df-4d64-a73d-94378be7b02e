import 'package:railops/models/notification_model.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

/// Enumeration for different types of onboarding notifications
enum OnboardingNotificationType {
  boarding,
  offBoarding,
  stationApproaching,
  coachReminder,
  berthReminder,
  // Phase 2: New notification categories
  stationApproachAlert, // "Approaching [Station] - Next stop in X minutes"
  boardingCountUpdate, // Real-time passenger count updates
  offBoardingPreparation, // Preparation reminders with coach details
  trainStatusUpdate, // Schedule changes and service announcements
  proximityAlert, // Location-based proximity notifications
}

/// Enumeration for notification trigger conditions
enum NotificationTriggerType {
  immediate,
  timeBasedDelay,
  locationBasedProximity,
  stationSequence,
  // Phase 2: Enhanced trigger types
  passengerCountThreshold, // Trigger based on passenger count
  trainStatusChange, // Trigger on train status updates
  scheduleChange, // Trigger on schedule modifications
}

/// Data model for onboarding-specific notification payload
/// Extends the existing NotificationModel structure with train-specific context
class OnboardingNotificationData {
  final String notificationId;
  final OnboardingNotificationType type;
  final String title;
  final String body;
  final StationNotificationContext stationContext;
  final Map<String, dynamic> additionalData;
  final DateTime createdAt;
  final bool isUrgent;

  OnboardingNotificationData({
    required this.notificationId,
    required this.type,
    required this.title,
    required this.body,
    required this.stationContext,
    this.additionalData = const {},
    DateTime? createdAt,
    this.isUrgent = false,
  }) : createdAt = createdAt ?? DateTime.now();

  /// Convert to NotificationModel for compatibility with existing notification system
  NotificationModel toNotificationModel() {
    return NotificationModel(
      id: notificationId,
      title: title,
      body: body,
      data: {
        'type': type.name,
        'station_context': stationContext.toJson(),
        'is_urgent': isUrgent,
        'created_at': createdAt.toIso8601String(),
        ...additionalData,
      },
      timestamp: createdAt.millisecondsSinceEpoch,
      isRead: false,
    );
  }

  /// Create from existing NotificationModel
  factory OnboardingNotificationData.fromNotificationModel(
      NotificationModel model) {
    final data = model.data;
    return OnboardingNotificationData(
      notificationId: model.id ?? '',
      type: OnboardingNotificationType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => OnboardingNotificationType.boarding,
      ),
      title: model.title ?? '',
      body: model.body ?? '',
      stationContext: StationNotificationContext.fromJson(
        data['station_context'] ?? {},
      ),
      additionalData: Map<String, dynamic>.from(data)
        ..removeWhere((key, value) => [
              'type',
              'station_context',
              'is_urgent',
              'created_at'
            ].contains(key)),
      createdAt: data['created_at'] != null
          ? DateTime.parse(data['created_at'])
          : DateTime.fromMillisecondsSinceEpoch(model.timestamp),
      isUrgent: data['is_urgent'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notification_id': notificationId,
      'type': type.name,
      'title': title,
      'body': body,
      'station_context': stationContext.toJson(),
      'additional_data': additionalData,
      'created_at': createdAt.toIso8601String(),
      'is_urgent': isUrgent,
    };
  }

  factory OnboardingNotificationData.fromJson(Map<String, dynamic> json) {
    return OnboardingNotificationData(
      notificationId: json['notification_id'] ?? '',
      type: OnboardingNotificationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => OnboardingNotificationType.boarding,
      ),
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      stationContext: StationNotificationContext.fromJson(
        json['station_context'] ?? {},
      ),
      additionalData: Map<String, dynamic>.from(json['additional_data'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
      isUrgent: json['is_urgent'] ?? false,
    );
  }
}

/// Context data specific to station notifications
/// Contains all relevant train and station information for notification generation
class StationNotificationContext {
  final String stationName;
  final String trainNumber;
  final String date;
  final List<String> coachNumbers;
  final List<String> allStations;
  final Map<String, List<int>>? berthNumbers; // Coach -> Berth numbers
  final Map<String, List<int>>? offBoardingBerthNumbers;
  final int? stationIndex; // Position in the station sequence
  final bool isDestination;

  StationNotificationContext({
    required this.stationName,
    required this.trainNumber,
    required this.date,
    required this.coachNumbers,
    required this.allStations,
    this.berthNumbers,
    this.offBoardingBerthNumbers,
    this.stationIndex,
    this.isDestination = false,
  });

  /// Create context from UpcomingStationResponse for a specific station
  factory StationNotificationContext.fromUpcomingStationResponse(
    UpcomingStationResponse response,
    String targetStation,
  ) {
    final stationIndex = response.stations.indexOf(targetStation);
    final isDestination = stationIndex == response.stations.length - 1;

    // Extract berth numbers for the target station
    Map<String, List<int>>? berthNumbers;
    Map<String, List<int>>? offBoardingBerthNumbers;

    if (response.details.containsKey(targetStation)) {
      berthNumbers = response.details[targetStation];
    }

    if (response.detailsOffBoarding.containsKey(targetStation)) {
      offBoardingBerthNumbers = response.detailsOffBoarding[targetStation];
    }

    return StationNotificationContext(
      stationName: targetStation,
      trainNumber: response.trainNumber,
      date: response.date,
      coachNumbers: response.coachNumbers,
      allStations: response.stations,
      berthNumbers: berthNumbers,
      offBoardingBerthNumbers: offBoardingBerthNumbers,
      stationIndex: stationIndex >= 0 ? stationIndex : null,
      isDestination: isDestination,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'station_name': stationName,
      'train_number': trainNumber,
      'date': date,
      'coach_numbers': coachNumbers,
      'all_stations': allStations,
      'berth_numbers': berthNumbers,
      'off_boarding_berth_numbers': offBoardingBerthNumbers,
      'station_index': stationIndex,
      'is_destination': isDestination,
    };
  }

  factory StationNotificationContext.fromJson(Map<String, dynamic> json) {
    return StationNotificationContext(
      stationName: json['station_name'] ?? '',
      trainNumber: json['train_number'] ?? '',
      date: json['date'] ?? '',
      coachNumbers: List<String>.from(json['coach_numbers'] ?? []),
      allStations: List<String>.from(json['all_stations'] ?? []),
      berthNumbers: json['berth_numbers'] != null
          ? Map<String, List<int>>.from(
              (json['berth_numbers'] as Map).map(
                (key, value) => MapEntry(key.toString(), List<int>.from(value)),
              ),
            )
          : null,
      offBoardingBerthNumbers: json['off_boarding_berth_numbers'] != null
          ? Map<String, List<int>>.from(
              (json['off_boarding_berth_numbers'] as Map).map(
                (key, value) => MapEntry(key.toString(), List<int>.from(value)),
              ),
            )
          : null,
      stationIndex: json['station_index'],
      isDestination: json['is_destination'] ?? false,
    );
  }
}

/// Trigger conditions and timing for notifications
/// Defines when and how notifications should be fired
class NotificationTrigger {
  final NotificationTriggerType type;
  final Duration? delay; // For time-based triggers
  final double? proximityThresholdKm; // For location-based triggers
  final int? stationsBefore; // For station sequence triggers
  final Map<String, dynamic> conditions; // Additional trigger conditions
  final bool isRepeating;
  final Duration? repeatInterval;

  NotificationTrigger({
    required this.type,
    this.delay,
    this.proximityThresholdKm,
    this.stationsBefore,
    this.conditions = const {},
    this.isRepeating = false,
    this.repeatInterval,
  });

  /// Create immediate trigger
  factory NotificationTrigger.immediate() {
    return NotificationTrigger(type: NotificationTriggerType.immediate);
  }

  /// Create time-based delay trigger
  factory NotificationTrigger.timeDelay(Duration delay) {
    return NotificationTrigger(
      type: NotificationTriggerType.timeBasedDelay,
      delay: delay,
    );
  }

  /// Create location-based proximity trigger
  factory NotificationTrigger.proximity(double thresholdKm) {
    return NotificationTrigger(
      type: NotificationTriggerType.locationBasedProximity,
      proximityThresholdKm: thresholdKm,
    );
  }

  /// Create station sequence trigger (e.g., 2 stations before target)
  factory NotificationTrigger.stationsBefore(int count) {
    return NotificationTrigger(
      type: NotificationTriggerType.stationSequence,
      stationsBefore: count,
    );
  }

  /// Phase 2: Create passenger count threshold trigger
  factory NotificationTrigger.passengerCountThreshold(int threshold) {
    return NotificationTrigger(
      type: NotificationTriggerType.passengerCountThreshold,
      conditions: {'passenger_threshold': threshold},
    );
  }

  /// Phase 2: Create train status change trigger
  factory NotificationTrigger.trainStatusChange({
    List<String>? statusTypes,
    Duration? debounceDelay,
  }) {
    return NotificationTrigger(
      type: NotificationTriggerType.trainStatusChange,
      delay: debounceDelay,
      conditions: {
        'status_types':
            statusTypes ?? ['delay', 'cancellation', 'platform_change'],
        'debounce_enabled': debounceDelay != null,
      },
    );
  }

  /// Phase 2: Create schedule change trigger
  factory NotificationTrigger.scheduleChange({
    Duration? minimumDelayThreshold,
    bool includeAdvanceNotifications = true,
  }) {
    return NotificationTrigger(
      type: NotificationTriggerType.scheduleChange,
      conditions: {
        'minimum_delay_minutes': minimumDelayThreshold?.inMinutes ?? 5,
        'include_advance_notifications': includeAdvanceNotifications,
      },
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'delay_seconds': delay?.inSeconds,
      'proximity_threshold_km': proximityThresholdKm,
      'stations_before': stationsBefore,
      'conditions': conditions,
      'is_repeating': isRepeating,
      'repeat_interval_seconds': repeatInterval?.inSeconds,
    };
  }

  factory NotificationTrigger.fromJson(Map<String, dynamic> json) {
    return NotificationTrigger(
      type: NotificationTriggerType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NotificationTriggerType.immediate,
      ),
      delay: json['delay_seconds'] != null
          ? Duration(seconds: json['delay_seconds'])
          : null,
      proximityThresholdKm: json['proximity_threshold_km']?.toDouble(),
      stationsBefore: json['stations_before'],
      conditions: Map<String, dynamic>.from(json['conditions'] ?? {}),
      isRepeating: json['is_repeating'] ?? false,
      repeatInterval: json['repeat_interval_seconds'] != null
          ? Duration(seconds: json['repeat_interval_seconds'])
          : null,
    );
  }
}
