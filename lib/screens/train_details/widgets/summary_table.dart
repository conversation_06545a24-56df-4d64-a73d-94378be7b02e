import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:railops/types/train_types/train_details_response.dart';

class SummaryTable extends StatefulWidget {
  final TrainDataResponse trainData;

  const SummaryTable({super.key, required this.trainData});

  @override
  State<SummaryTable> createState() => _SummaryTableState();
}

class _SummaryTableState extends State<SummaryTable> {
  bool _isExpanded = true;

  @override
  Widget build(BuildContext context) {
    int grandTotalBerths = 0;
    int grandTotalOffBoardingBerths = 0;

    List<DataGridRow> summaryRows = widget.trainData.trainsData.map((trainDetail) {
      int totalBerths = trainDetail.details
          .fold(0, (sum, detail) => sum + detail.countOfBirth);
      int totalOffBoardingBerths = trainDetail.details
          .fold(0, (sum, detail) => sum + detail.offBoardingCountOfBirth);

      grandTotalBerths += totalBerths;
      grandTotalOffBoardingBerths += totalOffBoardingBerths;

      return DataGridRow(cells: [
        DataGridCell<String>(
            columnName: 'stationCode', value: trainDetail.stationCode),
        DataGridCell<int>(columnName: 'totalBerths', value: totalBerths),
        DataGridCell<int>(
            columnName: 'totalOffBoardingBerths',
            value: totalOffBoardingBerths),
      ]);
    }).toList();

    summaryRows.add(DataGridRow(cells: [
      const DataGridCell<String>(columnName: 'stationCode', value: 'Total'),
      DataGridCell<int>(columnName: 'totalBerths', value: grandTotalBerths),
      DataGridCell<int>(
          columnName: 'totalOffBoardingBerths',
          value: grandTotalOffBoardingBerths),
    ]));
    
    // Calculate the height needed
    double calculatedHeight = _isExpanded 
        ? ((summaryRows.length * 40.0) + 56.0) // Full height when expanded
        : 120.0; // Fixed height for collapsed view
    
    double deviceWidth = MediaQuery.of(context).size.width;

    return Card(
      elevation: 2.0,
      margin: EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and expand/collapse button
          InkWell(
            onTap: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Summary',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Icon(
                    _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    size: 24,
                    color: Colors.blue,
                  ),
                ],
              ),
            ),
          ),
          // Divider between header and content
          Divider(height: 1, thickness: 1, color: Colors.grey[300]),
          // Expandable content
          AnimatedSize(
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: _isExpanded
                ? Container(
                    height: calculatedHeight,
                    width: deviceWidth - 32,
                    child: SfDataGrid(
                      source: SummaryDataSource(summaryRows),
                      columns: [
                        GridColumn(
                          columnName: 'stationCode',
                          label: _buildHeaderCell('Station Code'),
                        ),
                        GridColumn(
                          columnName: 'totalBerths',
                          label: _buildHeaderCell('Onboarding\nBerths'),
                          width: (deviceWidth - 32) * 0.35,
                        ),
                        GridColumn(
                          columnName: 'totalOffBoardingBerths',
                          label: _buildHeaderCell('Offboarding\nBerths'),
                          width: (deviceWidth - 32) * 0.35,
                        ),
                      ],
                      gridLinesVisibility: GridLinesVisibility.both,
                      headerGridLinesVisibility: GridLinesVisibility.both,
                      columnWidthMode: ColumnWidthMode.fill,
                      rowHeight: 40.0,
                      shrinkWrapRows: true,
                      verticalScrollPhysics: NeverScrollableScrollPhysics(),
                    ),
                  )
                : Container(
                    width: deviceWidth - 32, // Match the expanded table width
                    padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
                    child: _buildCollapsedSummary(grandTotalBerths, grandTotalOffBoardingBerths),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String title) {
    return Container(
      alignment: Alignment.center,
      color: Colors.blue,
      padding: const EdgeInsets.all(8.0),
      child: Text(
        title,
        textAlign: TextAlign.center,
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
    );
  }

  Widget _buildCollapsedSummary(int totalOnboarding, int totalOffboarding) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildSummaryItem('Onboarding', totalOnboarding, Colors.green),
        SizedBox(width: 12),
        _buildSummaryItem('Off-boarding', totalOffboarding, Colors.red),
      ],
    );
  }

  Widget _buildSummaryItem(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 10.0),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: color.withOpacity(0.8),
              ),
            ),
            SizedBox(height: 4),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SummaryDataSource extends DataGridSource {
  final List<DataGridRow> summaryRows;

  SummaryDataSource(this.summaryRows);

  @override
  List<DataGridRow> get rows => summaryRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    bool isTotalRow = row.getCells().first.value == 'Total';
    int rowIndex = summaryRows.indexOf(row);

    return DataGridRowAdapter(
      color: rowIndex % 2 == 0 ? Colors.grey[200] : Colors.white,
      cells: row.getCells().map((dataGridCell) {
        Color cellColor;

        if (dataGridCell.columnName == 'stationCode') {
          cellColor = isTotalRow ? Colors.yellow[100]! : Colors.transparent;
        } else if (dataGridCell.columnName == 'totalBerths') {
          cellColor = isTotalRow 
              ? Colors.yellow[100]!
              : rowIndex % 2 == 0 ? Colors.green[200]! : Colors.green[100]!;
        } else if (dataGridCell.columnName == 'totalOffBoardingBerths') {
          cellColor = isTotalRow 
              ? Colors.yellow[100]!
              : rowIndex % 2 == 0 ? Colors.red[200]! : Colors.red[100]!;
        } else {
          cellColor = Colors.transparent;
        }

        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.all(8.0),
          color: cellColor,
          child: Text(
            dataGridCell.value.toString(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: isTotalRow ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        );
      }).toList(),
    );
  }
}