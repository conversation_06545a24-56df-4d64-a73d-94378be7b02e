import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/screens/profile_screen/widgets/toggle_switch_widget.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';

class TrainPopup extends StatefulWidget {
  @override
  _TrainPopupState createState() => _TrainPopupState();
}

class _TrainPopupState extends State<TrainPopup> {
  List<String> _trainNumbers = [];
  List<AddTrainDetail> _trainDetails = []; // Train details for the table
  String? _selectedTrainNumber;
  bool _insideTrain = false;
  bool _needAlarm = false;
  bool _isLoadingAddTrains = false;
  bool _isFetchingTrainNumbers = false;
  bool _isFetchingTrainDetails = false; // To track train details loading state
  bool _dataInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeDataOnce();
  }

  void _initializeDataOnce() {
    if (_dataInitialized) {
      print("Data already initialized, skipping...");
      return;
    }
    _dataInitialized = true;
    print("Initializing data...");
    _initializeData();
  }

  Future<void> _initializeData() async {
    await _fetchToggleSwitchStatuses();
    await _fetchTrainNumbers();
    await _fetchTrainDetails(); // Fetch train details for the table
  }

  Future<void> _fetchToggleSwitchStatuses() async {
    try {
      print("Fetching toggle switch statuses...");
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(token);
      final needAlarmStatus =
          await ProfileTrainServices.getNeedAlarmStatus(token);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
        _selectedTrainNumber = insideTrainStatus['inside_train_number'];
        _needAlarm = needAlarmStatus;
      });
    } catch (e) {
      print('Error fetching toggle switch statuses: $e');
    }
  }

  Future<void> _fetchTrainNumbers() async {
    setState(() {
      _isFetchingTrainNumbers = true;
    });

    try {
      print("Fetching train numbers...");
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final response = await TrainService.getTrainDetails(token);

      if (response.trainDetails.isNotEmpty) {
        setState(() {
          _trainNumbers = response.trainDetails.values
              .map((detail) => detail.trainNumber)
              .toList();
        });
      } else {
        print("No train details found.");
      }
    } catch (e) {
      print('Error fetching train numbers: $e');
    } finally {
      setState(() {
        _isFetchingTrainNumbers = false;
      });
    }
  }

  Future<void> _fetchTrainDetails() async {
    setState(() {
      _isFetchingTrainDetails = true;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final response = await TrainService.getTrainDetails(token);

      setState(() {
        _trainDetails = response.trainDetails.values.toList();
      });
    } catch (e) {
      print('Error fetching train details: $e');
    } finally {
      setState(() {
        _isFetchingTrainDetails = false;
      });
    }
  }

  Widget _buildPopupContent() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        DropdownSearch<String>(
          items: _trainNumbers,
          selectedItem: _selectedTrainNumber,
          onChanged: (value) {
            setState(() {
              _selectedTrainNumber = value;
              _updateInsideTrainStatus();
            });
          },
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: InputDecoration(
              labelText: 'Select Train Number',
              border: OutlineInputBorder(),
            ),
          ),
          popupProps: PopupProps.menu(
            showSearchBox: true,
            itemBuilder: (context, item, isSelected) {
              return ListTile(
                title: Text(item),
                selected: isSelected,
              );
            },
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select a train number';
            }
            return null;
          },
        ),
        SizedBox(height: 10),
        _buildToggleSection(
          "inside train",
          _insideTrain,
          (index) {
            setState(() {
              _onInsideTrainToggle(index);
            });
          },
        ),
        SizedBox(height: 10),
        _buildToggleSection(
          "need alarm",
          _needAlarm,
          (index) {
            setState(() {
              _onNeedAlarmToggle(index);
            });
          },
        ),
        SizedBox(height: 20),
        _buildTrainDetailsTable(), // Add the train details table here
      ],
    );
  }

  Widget _buildTrainDetailsTable() {
    if (_isFetchingTrainDetails) {
      return CircularProgressIndicator();
    }

    if (_trainDetails.isEmpty) {
      return Text('No train details available.');
    }

    return SingleChildScrollView(
      scrollDirection: Axis.vertical,
      child: DataTable(
        dataRowMaxHeight: double.infinity,
        columnSpacing: 16,
        border: TableBorder.all(color: Colors.black, width: 1),
        columns: const [
          DataColumn(
            label: Text(
              'Train',
              softWrap: true,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                overflow: TextOverflow.visible,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'Coaches',
              softWrap: true,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                overflow: TextOverflow.visible,
              ),
            ),
          ),
          DataColumn(
            label: Text(
              'Origin Date',
              softWrap: true,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                overflow: TextOverflow.visible,
              ),
            ),
          ),
        ],
        rows: _trainDetails.map(
          (detail) {
            return DataRow(
              cells: [
                DataCell(
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          detail.trainNumber,
                          softWrap: true,
                          style:
                              const TextStyle(overflow: TextOverflow.visible),
                        ),
                      ),
                    ],
                  ),
                ),
                DataCell(
                  Text(
                    detail.coachNumbers.join(', '),
                    softWrap: true,
                    style: const TextStyle(overflow: TextOverflow.visible),
                  ),
                ),
                DataCell(
                  Text(
                    detail.originDate,
                    softWrap: true,
                    style: const TextStyle(overflow: TextOverflow.visible),
                  ),
                ),
              ],
            );
          },
        ).toList(),
      ),
    );
  }

 void _updateInsideTrainStatus() async {
  try {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    await ProfileTrainServices.toggleInsideTrain(
      token,
      _insideTrain,
      _selectedTrainNumber ?? '',
    );
  } catch (e) {
    print('Error updating inside train status: $e');
  }
}

void _onInsideTrainToggle(int index) async {
  final isInsideTrain = index == 0;
  setState(() {
    _insideTrain = isInsideTrain;
  });

  _updateInsideTrainStatus();
}

  void _onNeedAlarmToggle(int index) async {
    final needAlarm = index == 0;
    setState(() {
      _needAlarm = needAlarm;
    });

    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      await ProfileTrainServices.toggleNeedAlarm(token, _needAlarm);
    } catch (e) {
      print('Error toggling need alarm status: $e');
      setState(() {
        _needAlarm = !needAlarm;
      });
    }
  }

  Future<void> _AddTrains() async {
    setState(() {
      _isLoadingAddTrains = true;
    });

    await Future.delayed(const Duration(seconds: 1));
    Navigator.pushNamed(context, '/add-train-profile');

    setState(() {
      _isLoadingAddTrains = false;
    });
  }

  Widget _buildToggleSection(
      String label, bool initialState, Function(int) onToggle) {
    final mediaQuery = MediaQuery.of(context);
    final toggleWidth = (mediaQuery.size.width * 0.9 - 40) *
        0.5; // 50% of the popup width minus padding adjustments

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(width: 10),
        SizedBox(
          width: toggleWidth,
          child: ToggleSwitchWidget(
            onToggleCallback: onToggle,
            initialIndex: initialState ? 0 : 1,
          ),
        ),
      ],
    );
  }

  Widget _buildButton(String label, bool isLoading, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: isLoading ? null : onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white, 
        side: BorderSide(color: Colors.black87 , width: 0.5)// Set background color to blue
      ),
      child: isLoading
          ? CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white), // White color
              strokeWidth: 2.0,
            )
          : Text(
              label,
              style: TextStyle(color: Colors.black), // Text color white
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final dialogWidth = mediaQuery.size.width * 0.9; 
    final dialogHeight = mediaQuery.size.height * 0.75;

    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Container(
        width: dialogWidth,
        height: dialogHeight,
        padding: EdgeInsets.all(16), // Adjust the padding as needed
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Train Details',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: _buildPopupContent(),
              ),
            ),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _buildButton(
                  'Add Trains',
                  _isLoadingAddTrains,
                  () {
                    setState(() {
                      _AddTrains();
                    });
                  },
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        Colors.white,
                        side: BorderSide(color: Colors.black87 , width: 0.5) // Set background color to blue
                  ),
                  child: Text(
                    'Close',
                    style: TextStyle(color: Colors.black), // Text color white
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
