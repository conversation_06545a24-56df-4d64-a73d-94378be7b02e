import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/services.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/train_services/index.dart';

class TrainFiltersWithStation extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final void Function(String? trainNumber, String? trainName,
      List<String> selectedStations, DateTime? selectedDate) onSubmit;

  final String initialTrainNumber;
  final String initialDate;

  TrainFiltersWithStation({
    required this.formKey,
    required this.onSubmit,
    required this.initialTrainNumber,
    required this.initialDate,
  });

  @override
  _TrainFiltersWithStationState createState() =>
      _TrainFiltersWithStationState();
}

class Station {
  final String name;
  final int id;

  Station({required this.name, required this.id});

  @override
  String toString() {
    return 'Station(name: $name, id: $id)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Station && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class _TrainFiltersWithStationState extends State<TrainFiltersWithStation> {
  final _formKey = GlobalKey<FormState>();
  final controller = MultiSelectController<Station>();
  late final String _initialTrainNumber = widget.initialTrainNumber;

  String? _selectedTrainNumber;
  String? _trainName;
  DateTime? _selectedDate = DateTime.now();
  List<String> _trainNumbers = [];
  List<DropdownItem<Station>> stationItems = [];

  bool _selectAllTriggered = false;
  Set<Station> _previousSelection = Set<Station>();
  bool _isSelectingAll = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _fetchTrainNumbers();
  }

  Future<void> _initializeForm() async {
    if (widget.initialTrainNumber.isNotEmpty) {
      _selectedTrainNumber = widget.initialTrainNumber;
      _trainName = await fetchTrainName(widget.initialTrainNumber);
      _selectedDate =
          DateTime.parse(widget.initialDate); // Ensure the date format matches

      // Fetch stations for the initial train number
      final stations = await fetchStations(widget.initialTrainNumber);
      setState(() {
        stationItems = stations;
        // Pre-select stations if available
        controller.setItems(stationItems);
        controller.selectAll();
      });
    }
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {});
    } catch (e) {
      print('Error fetching train numbers: $e');
    }
  }

  Future<String?> fetchTrainName(String? trainNumber) async {
    return await TrainService.getTrainName(trainNumber!);
  }

  Future<List<DropdownItem<Station>>> fetchStations(String? trainNumber) async {
    final result = await TrainService.getTrainStations(trainNumber!);
    List<String> stationNames = result['stationList'];

    List<DropdownItem<Station>> stationItems = stationNames
        .asMap()
        .entries
        .map((entry) => DropdownItem<Station>(
              label: entry.value,
              value: Station(name: entry.value, id: entry.key + 1),
            ))
        .toList();

    stationItems.insert(
      0,
      DropdownItem<Station>(
          label: 'Select All', value: Station(name: 'Select All', id: 0)),
    );

    return stationItems;
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    final trainName = await fetchTrainName(trainNumber);
    final stations = await fetchStations(trainNumber);

    setState(() {
      _selectedTrainNumber = trainNumber;
      _trainName = trainName;
      stationItems = stations;
      controller.clearAll();
      controller.setItems(stationItems);
    });

    Provider.of<UserModel>(context, listen: false).setTrainNo(trainNumber!);
  }

  void _handleSubmit() {
    if (_formKey.currentState?.validate() ?? false) {
      final selectedItems = controller.selectedItems;
      widget.onSubmit(
        _selectedTrainNumber,
        _trainName,
        selectedItems.map((item) => item.value.name).toList(),
        _selectedDate,
      );
    }
  }

  ElevatedButton _buildSubmitButton() {
    return ElevatedButton(
      onPressed: _handleSubmit,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        textStyle: const TextStyle(fontSize: 16),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        side: const BorderSide(color: Colors.black87, width: 0.5),
      ),
      child: const Text('Submit'),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Flexible(
                        flex: 2,
                        child: DropdownSearch<String>(
                          items: _trainNumbers,
                          onChanged: _onTrainNumberChanged,
                          dropdownDecoratorProps: const DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              labelText: 'Train Number',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 8),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          popupProps: PopupProps.menu(
                            showSearchBox: true,
                            searchFieldProps: TextFieldProps(
                              keyboardType: TextInputType.number,
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(6),
                              ],
                            ),
                            itemBuilder: (context, item, isSelected) {
                              return ListTile(
                                title: Text(item,
                                    style: const TextStyle(fontSize: 14)),
                                selected: isSelected,
                              );
                            },
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a train number';
                            }
                            return null;
                          },
                          // Set initial value for the train number
                          selectedItem: _selectedTrainNumber,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        flex: 3,
                        child: TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'Train Name',
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 8),
                          ),
                          textAlign: TextAlign.center,
                          readOnly: true,
                          controller: TextEditingController(text: _trainName),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  MultiDropdown<Station>(
                    items: stationItems,
                    controller: controller,
                    enabled: stationItems.isNotEmpty,
                    searchEnabled: true,
                    chipDecoration: ChipDecoration(
                      backgroundColor: Colors.white,
                      border: Border.all(
                        color: Colors.blue,
                        width: 1.0,
                      ),
                      wrap: false,
                      runSpacing: 4,
                      spacing: 8,
                    ),
                    fieldDecoration: FieldDecoration(
                      hintText: 'Select Stations',
                      hintStyle: const TextStyle(color: Colors.black54),
                      showClearIcon: true,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(color: Colors.blueAccent),
                      ),
                    ),
                    dropdownDecoration: const DropdownDecoration(
                      marginTop: 2,
                      maxHeight: 300,
                      header: Padding(
                        padding: EdgeInsets.all(8),
                        child: Text(
                          'Select stations from the list',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    dropdownItemDecoration: DropdownItemDecoration(
                      selectedIcon:
                          const Icon(Icons.check_box, color: Colors.green),
                      disabledIcon:
                          Icon(Icons.lock, color: Colors.grey.shade300),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select at least one station';
                      }
                      return null;
                    },
                    onSelectionChange: (selectedItems) {
                      final selectedItemsSet = Set<Station>.from(selectedItems);
                      final addedItems =
                          selectedItemsSet.difference(_previousSelection);
                      final removedItems =
                          _previousSelection.difference(selectedItemsSet);

                      if (addedItems
                          .contains(Station(name: 'Select All', id: 0))) {
                        if (!_isSelectingAll) {
                          _isSelectingAll = true;
                          setState(() {
                            controller.selectAll();
                          });
                        }
                      } else if (removedItems
                          .contains(Station(name: 'Select All', id: 0))) {
                        if (_isSelectingAll) {
                          _isSelectingAll = false;
                          setState(() {
                            controller.clearAll();
                          });
                        }
                      } else if (_isSelectingAll && removedItems.isNotEmpty) {
                        _isSelectingAll = false;
                        setState(() {
                          controller.clearAll();
                          controller.selectWhere((item) =>
                              selectedItemsSet.contains(item.value) &&
                              item.value.id != 0);
                        });
                      }

                      if (!(selectedItemsSet.length == 1 &&
                          selectedItemsSet
                              .contains(Station(name: 'Select All', id: 0)))) {
                        _previousSelection = selectedItemsSet;
                      }
                    },
                  ),
                  const SizedBox(height: 8),
                  TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Select Date',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    readOnly: true,
                    textAlign: TextAlign.center,
                    onTap: () async {
                      DateTime? selectedDate = await showDatePicker(
                        context: context,
                        initialDate: _selectedDate ?? DateTime.now(),
                        firstDate: DateTime(2000),
                        lastDate: DateTime(2100),
                      );
                      if (selectedDate != null) {
                        setState(() {
                          _selectedDate = selectedDate;
                        });
                      }
                    },
                    controller: TextEditingController(
                      text: _selectedDate != null
                          ? "${_selectedDate!.day}-${_selectedDate!.month}-${_selectedDate!.year}"
                          : '',
                    ),
                    validator: (value) {
                      if (_selectedDate == null) {
                        return 'Please select a date';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 12),
                  if (_initialTrainNumber.isEmpty) ...[
                    Row(
                      children: [
                        Text(
                          'Not inside train',
                          style: Theme.of(context).textTheme.titleSmall,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(width: 10),
                        Center(
                          child: _buildSubmitButton(),
                        )
                      ],
                    )
                  ] else
                    Center(child: _buildSubmitButton()),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
