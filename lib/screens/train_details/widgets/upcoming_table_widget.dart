import 'package:flutter/material.dart';

class UpcomingTableWidget extends StatefulWidget {
  final Map<String, Map<String, List<int>>> details; // Onboarding details
  final Map<String, Map<String, List<int>>>
      detailsOffBoarding; // Offboarding details

  UpcomingTableWidget({
    required this.details,
    required this.detailsOffBoarding,
  });

  @override
  _UpcomingTableWidgetState createState() => _UpcomingTableWidgetState();
}

class _UpcomingTableWidgetState extends State<UpcomingTableWidget> {
  bool _isExpanded = false; // Track the expanded/collapsed state

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isExpanded = !_isExpanded;
        });
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border.all(color: Colors.blueAccent, width: 2), // Bolder border
          borderRadius: BorderRadius.circular(16.0), // More radius
        ),
        margin: const EdgeInsets.fromLTRB(16.0, 4.0, 16.0, 8.0), // Added margin with different top and bottom values
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconButton(
                  icon: Icon(
                    _isExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                    size: 24,
                  ),
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ),
                Text(
                  'Upcoming stations around 10 kms',
                  style: Theme.of(context)
                      .textTheme
                      .titleSmall, // Updated text style
                ),
              ],
            ),
            if (_isExpanded)
              widget.details.isEmpty && widget.detailsOffBoarding.isEmpty
                  ? Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16.0),
                      child: Center(
                        child: Text(
                          "No passenger onboarding/offboarding details available around 10 kms",
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 16, color: Colors.grey),
                        ),
                      ),
                    )
                  : Column(
                      children: [
                        _buildTopLevelTable(),
                        Table(
                          border: TableBorder.all(),
                          columnWidths: const {
                            0: FlexColumnWidth(2), // Station column
                            1: FlexColumnWidth(6), // Coaches column
                          },
                          children: _buildTableRows(),
                        ),
                      ],
                    ),
          ],
        ),
      ),
    );
  }

  List<TableRow> _buildTableRows() {
    List<TableRow> rows = [];

    // Iterate over each station
    for (var entry in widget.details.entries) {
      final station = entry.key;
      final onboardingDetails = entry.value;
      final offboardingDetails = widget.detailsOffBoarding[station] ?? {};

      // Create nested table for coaches
      final coachTable = Table(
        border: TableBorder.all(),
        columnWidths: const {
          0: FlexColumnWidth(1),
          1: FlexColumnWidth(2),
        },
        children: _buildCoachRows(onboardingDetails, offboardingDetails),
      );

      // Add row for the current station
      rows.add(
        TableRow(
          children: [
            _buildStationCell(station),
            TableCell(
              verticalAlignment: TableCellVerticalAlignment.top,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  coachTable,
                ],
              ),
            ),
          ],
        ),
      );
    }

    return rows;
  }

  List<TableRow> _buildCoachRows(Map<String, List<int>> onboardingDetails,
      Map<String, List<int>> offboardingDetails) {
    Set<String> allCoaches = {
      ...onboardingDetails.keys,
      ...offboardingDetails.keys
    };

    return allCoaches.map((coach) {
      final onboarding = onboardingDetails[coach] ?? [];
      final offboarding = offboardingDetails[coach] ?? [];

      return TableRow(
        children: [
          _buildCoachCell(coach),
          _buildNestedDetailsTable(onboarding, offboarding),
        ],
      );
    }).toList();
  }

  Table _buildNestedDetailsTable(List<int> onboarding, List<int> offboarding) {
    return Table(
      border: TableBorder.all(),
      columnWidths: const {
        0: FlexColumnWidth(1),
        1: FlexColumnWidth(1),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(
              color: Colors.green.shade100), // Green color for onboarding
          children: [
            _buildHeaderCell(onboarding.length.toString()),
            _buildBerthNumbersCell(onboarding),
          ],
        ),
        TableRow(
          decoration: BoxDecoration(
              color: Colors.red.shade100), // Red color for offboarding
          children: [
            _buildHeaderCell(offboarding.length.toString()),
            _buildBerthNumbersCell(offboarding),
          ],
        ),
      ],
    );
  }

  Table _buildTopLevelTable() {
    return Table(
      border: TableBorder.all(),
      columnWidths: const {
        0: FlexColumnWidth(2),
        1: FlexColumnWidth(2),
        2: FlexColumnWidth(2),
        3: FlexColumnWidth(2),
      },
      children: [
        TableRow(
          decoration: BoxDecoration(color: Colors.blue),
          children: [
            _buildHeaderCell('Station', textColor: Colors.white),
            _buildHeaderCell('Coach', textColor: Colors.white),
            _buildHeaderCell('Count of Berths', textColor: Colors.white),
            _buildHeaderCell('Berth Numbers', textColor: Colors.white),
          ],
        ),
      ],
    );
  }

  TableCell _buildHeaderCell(String text, {Color textColor = Colors.black}) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
      ),
    );
  }

  TableCell _buildStationCell(String station) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          station,
          textAlign: TextAlign.center,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  TableCell _buildCoachCell(String coach) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          coach,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  TableCell _buildBerthNumbersCell(List<int> berthNumbers) {
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.middle,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Text(
          berthNumbers.join(', '),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
