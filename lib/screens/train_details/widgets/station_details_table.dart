import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:railops/types/train_types/train_details_response.dart';

class StationDetailsTable extends StatefulWidget {
  final TrainDetail trainDetail;

  const StationDetailsTable({Key? key, required this.trainDetail})
      : super(key: key);

  @override
  State<StationDetailsTable> createState() => _StationDetailsTableState();
}

class _StationDetailsTableState extends State<StationDetailsTable> {
  late TrainDataSource _trainDataSource;
  final GlobalKey _dataGridKey = GlobalKey();
  double _totalHeight = 0.0;
  bool _isExpanded = true; // Track expanded/collapsed state

  @override
  void initState() {
    super.initState();
    _trainDataSource = TrainDataSource(widget.trainDetail.details);
    _calculateTotalHeight();
  }

  void _calculateTotalHeight() {
    if (widget.trainDetail.details.isEmpty) {
      _totalHeight = 100.0; // Height for just showing "No Users" message
      return;
    }

    double headerHeight = 56.0;
    double totalRowsHeight = 0.0;
    
    // Calculate height for each row
    for (int i = 0; i < widget.trainDetail.details.length; i++) {
      var berthDetail = widget.trainDetail.details[i];
      
      // Calculate how many berths in each column
      int berthNumbersCount = berthDetail.birthNumbers.length;
      int offBoardingBerthsCount = berthDetail.offBoardingBirthNumbers.length;
      int vacantBerthsCount = berthDetail.vacantBerths.length;
      
      // Get maximum berth text length from each column
      int maxBerthTextLength = _getMaxBerthLength(berthDetail.birthNumbers);
      int maxOffBoardingBerthTextLength = _getMaxBerthLength(berthDetail.offBoardingBirthNumbers);
      int maxVacantBerthTextLength = _getMaxBerthLength(berthDetail.vacantBerths);
      
      // Estimate how many berths can fit in a row based on text length
      int berthsPerRow = _estimateBerthsPerRow(maxBerthTextLength);
      int offBoardingBerthsPerRow = _estimateBerthsPerRow(maxOffBoardingBerthTextLength);
      int vacantBerthsPerRow = _estimateBerthsPerRow(maxVacantBerthTextLength);
      
      // Calculate how many rows we'll need
      int berthNumbersRows = berthNumbersCount > 0 ? (berthNumbersCount / berthsPerRow).ceil() : 1;
      int offBoardingBerthsRows = offBoardingBerthsCount > 0 ? (offBoardingBerthsCount / offBoardingBerthsPerRow).ceil() : 1;
      int vacantBerthsRows = vacantBerthsCount > 0 ? (vacantBerthsCount / vacantBerthsPerRow).ceil() : 1;
      
      // Find the maximum number of rows needed
      int maxRows = [
        berthNumbersRows,
        offBoardingBerthsRows,
        vacantBerthsRows
      ].reduce((a, b) => a > b ? a : b);
      
      // Base height + extra height for each row of berths + padding + extra buffer
      double rowHeight = 48.0 + (maxRows > 1 ? (maxRows - 1) * 28.0 : 0) + 20.0 + 20.0;
      totalRowsHeight += rowHeight;
    }
    
    // Add header height
    _totalHeight = headerHeight + totalRowsHeight;
    
    // Add some padding
    _totalHeight += 30.0;
  }

  int _getMaxBerthLength(List<String> berths) {
    if (berths.isEmpty) return 0;
    return berths.map((b) => b.length).reduce((max, len) => len > max ? len : max);
  }

  int _estimateBerthsPerRow(int maxBerthLength) {
    // Assuming average character width of ~8 pixels and container padding/margin of ~16 pixels
    int berthWidth = (maxBerthLength * 8) + 20;  // Increased from 16 to 20
    // Assuming column width of ~220 pixels (increased from 200)
    int columnWidth = 220;
    // Calculate how many berths can fit
    int berthsPerRow = columnWidth ~/ berthWidth;
    // Ensure at least 1 berth per row, maximum 4 (reduced from 5 to ensure all fit)
    return berthsPerRow.clamp(1, 4);
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;

    if (widget.trainDetail.details.isEmpty) {
      return Text(
        'No Users Onboarding for : ${widget.trainDetail.stationCode}',
        style: TextStyle(
            fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black),
      );
    }

    return Card(
      elevation: 2.0,
      margin: EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with station code and expand/collapse button
          InkWell(
            onTap: _toggleExpanded,
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Station Code: ${widget.trainDetail.stationCode}',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Icon(
                    _isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    size: 24,
                    color: Colors.blue,
                  ),
                ],
              ),
            ),
          ),
          // Divider between header and content
          Divider(height: 1, thickness: 1, color: Colors.grey[300]),
          // Expandable content
          AnimatedSize(
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: _isExpanded
                ? Container(
                    height: _totalHeight,
                    width: deviceWidth - 32,
                    child: SfDataGrid(
                      key: _dataGridKey,
                      source: _trainDataSource,
                      columns: _buildColumns(),
                      gridLinesVisibility: GridLinesVisibility.both,
                      headerGridLinesVisibility: GridLinesVisibility.both,
                      columnWidthMode: ColumnWidthMode.auto,
                      allowColumnsResizing: true,
                      horizontalScrollPhysics: AlwaysScrollableScrollPhysics(),
                      verticalScrollPhysics: NeverScrollableScrollPhysics(),
                      frozenColumnsCount: 1,  // Freeze the coach info column
                      navigationMode: GridNavigationMode.row,
                      onQueryRowHeight: (RowHeightDetails details) {
                        // For header row, return default height
                        if (details.rowIndex == 0) {
                          return 56.0;
                        }
                        
                        // For data rows
                        if (details.rowIndex > 0 && details.rowIndex <= widget.trainDetail.details.length) {
                          var berthDetail = widget.trainDetail.details[details.rowIndex - 1];
                          
                          // Calculate how many berths in each column
                          int berthNumbersCount = berthDetail.birthNumbers.length;
                          int offBoardingBerthsCount = berthDetail.offBoardingBirthNumbers.length;
                          int vacantBerthsCount = berthDetail.vacantBerths.length;

                          // Get maximum berth text length from each column
                          int maxBerthTextLength = _getMaxBerthLength(berthDetail.birthNumbers);
                          int maxOffBoardingBerthTextLength = _getMaxBerthLength(berthDetail.offBoardingBirthNumbers);
                          int maxVacantBerthTextLength = _getMaxBerthLength(berthDetail.vacantBerths);
                          
                          // Estimate how many berths can fit in a row based on text length
                          int berthsPerRow = _estimateBerthsPerRow(maxBerthTextLength);
                          int offBoardingBerthsPerRow = _estimateBerthsPerRow(maxOffBoardingBerthTextLength);
                          int vacantBerthsPerRow = _estimateBerthsPerRow(maxVacantBerthTextLength);
                          
                          // Calculate how many rows we'll need
                          int berthNumbersRows = berthNumbersCount > 0 ? (berthNumbersCount / berthsPerRow).ceil() : 1;
                          int offBoardingBerthsRows = offBoardingBerthsCount > 0 ? (offBoardingBerthsCount / offBoardingBerthsPerRow).ceil() : 1;
                          int vacantBerthsRows = vacantBerthsCount > 0 ? (vacantBerthsCount / vacantBerthsPerRow).ceil() : 1;
                          
                          // Find the maximum number of rows needed
                          int maxRows = [
                            berthNumbersRows,
                            offBoardingBerthsRows,
                            vacantBerthsRows
                          ].reduce((a, b) => a > b ? a : b);
                          
                          // Base height + extra height for each row of berths + padding + extra buffer
                          return 48.0 + (maxRows > 1 ? (maxRows - 1) * 28.0 : 0) + 20.0 + 20.0;
                        }
                        
                        return 48.0;
                      },
                    ),
                  )
                : Container(
                    width: deviceWidth - 32, // Match the expanded table width
                    padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
                    child: _buildCollapsedSummary(),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCollapsedSummary() {
    // Calculate totals for collapsed view
    int totalOnboarding = 0;
    int totalOffboarding = 0;
    int totalVacant = 0;
    
    for (var detail in widget.trainDetail.details) {
      totalOnboarding += detail.countOfBirth;
      totalOffboarding += detail.offBoardingCountOfBirth;
      totalVacant += detail.countOfVacantBerths;
    }
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildSummaryItem('Onboarding', totalOnboarding, Colors.green),
        SizedBox(width: 12), // Add more space between items
        _buildSummaryItem('Off-boarding', totalOffboarding, Colors.red),
        SizedBox(width: 12), // Add more space between items
        _buildSummaryItem('Vacant', totalVacant, Colors.blue),
      ],
    );
  }

  Widget _buildSummaryItem(String label, int count, Color color) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 10.0),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: color.withOpacity(0.8),
              ),
            ),
            SizedBox(height: 4),
            Text(
              count.toString(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<GridColumn> _buildColumns() {
    return [
      GridColumn(
        columnName: 'coachInfo',
        width: 60,
        label: _buildHeaderCell('Coach Info'),
      ),
      GridColumn(
        columnName: 'berthNumbers',
        width: 290,  // Increased width
        allowSorting: false,
        label: _buildHeaderCell('Onboarding Berths'),
      ),
      GridColumn(
        columnName: 'offBoardingBerthNumbers',
        width: 290,  // Increased width
        allowSorting: false,
        label: _buildHeaderCell('Off-Boarding Berths'),
      ),
      GridColumn(
        columnName: 'vacantBerthNumbers',
        width: 290,  // Increased width
        allowSorting: false,
        label: _buildHeaderCell('Vacant Berths'),
      ),
    ];
  }

  Widget _buildHeaderCell(String title) {
    return Container(
      alignment: Alignment.center,
      color: Colors.blue,
      padding: const EdgeInsets.all(8.0),
      child: Text(
        title,
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
    );
  }
}

class TrainDataSource extends DataGridSource {
  final List<BerthDetail> berthDetails;

  TrainDataSource(this.berthDetails);

  @override
  List<DataGridRow> get rows => berthDetails.map((berthDetail) {
        return DataGridRow(cells: [
          DataGridCell<BerthDetail>(
              columnName: 'coachInfo', value: berthDetail),
          DataGridCell<BerthDetail>(
              columnName: 'berthNumbers', value: berthDetail),
          DataGridCell<BerthDetail>(
              columnName: 'offBoardingBerthNumbers', value: berthDetail),
          DataGridCell<BerthDetail>(
              columnName: 'vacantBerthNumbers', value: berthDetail),
        ]);
      }).toList();

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    int rowIndex = berthDetails.indexWhere(
        (berthDetail) => berthDetail.coachNumber == (row.getCells()[0].value as BerthDetail).coachNumber);
    bool isEvenRow = rowIndex % 2 == 0;

    return DataGridRowAdapter(
      color: isEvenRow ? Colors.grey[200] : Colors.white,
      cells: row.getCells().map((dataGridCell) {
        Color columnBackgroundColor = _getColumnColor(dataGridCell.columnName, rowIndex);
        BerthDetail detail = dataGridCell.value as BerthDetail;

        if (dataGridCell.columnName == 'coachInfo') {
          return Container(
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8.0),
            color: isEvenRow ? Colors.grey[300] : Colors.grey[100],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${detail.coachNumber}',
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text(
                  'On: ${detail.countOfBirth}',
                  style: TextStyle(fontSize: 13, color: Colors.green[800]),
                ),
                Text(
                  'Off: ${detail.offBoardingCountOfBirth}',
                  style: TextStyle(fontSize: 13, color: Colors.red[800]),
                ),
                Text(
                  'Vac: ${detail.countOfVacantBerths}',
                  style: TextStyle(fontSize: 13, color: Colors.blue[800]),
                ),
              ],
            ),
          );
        } else if (dataGridCell.columnName == 'berthNumbers') {
          return _buildBerthNumbersCell(
            detail.birthNumbers, 
            detail.inrouteBerths, 
            isEvenRow ? Colors.green[100]! : Colors.green[50]!
          );
        } else if (dataGridCell.columnName == 'offBoardingBerthNumbers') {
          return _buildBerthNumbersCell(
            detail.offBoardingBirthNumbers, 
            detail.offBoardingInrouteBerths, 
            isEvenRow ? Colors.red[100]! : Colors.red[50]!
          );
        } else if (dataGridCell.columnName == 'vacantBerthNumbers') {
          return _buildBerthNumbersCell(
            detail.vacantBerths, 
            [], // No inroute concept for vacant berths
            isEvenRow ? Colors.blue[100]! : Colors.blue[50]!
          );
        }

        return Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.all(8.0),
          color: columnBackgroundColor,
          child: Text(
            dataGridCell.value.toString(),
            style: TextStyle(fontSize: 14),
          ),
        );
      }).toList(),
    );
  }

  Color _getColumnColor(String columnName, int rowIndex) {
    bool isEvenRow = rowIndex % 2 == 0;
    
    if (columnName == 'coachInfo') {
      return isEvenRow ? Colors.grey[300]! : Colors.grey[100]!;
    } else if (columnName == 'berthNumbers') {
      return isEvenRow ? Colors.green[100]! : Colors.green[50]!;
    } else if (columnName == 'offBoardingBerthNumbers') {
      return isEvenRow ? Colors.red[100]! : Colors.red[50]!;
    } else if (columnName == 'vacantBerthNumbers') {
      return isEvenRow ? Colors.blue[100]! : Colors.blue[50]!;
    } else {
      return Colors.transparent;
    }
  }

  Widget _buildBerthNumbersCell(
    List<String> allBerths, 
    List<String> inrouteBerths, 
    Color backgroundColor
  ) {
    return Container(
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 6.0), // Increased padding
      color: backgroundColor,
      constraints: BoxConstraints(minHeight: 80), // Increased from 66 to 80
      child: allBerths.isEmpty 
        ? Text('None', style: TextStyle(fontStyle: FontStyle.italic))
        : Wrap(
            alignment: WrapAlignment.center,
            spacing: 8.0, // Increased from 6.0 to 8.0
            runSpacing: 8.0, // Increased from 6.0 to 8.0
            children: allBerths.map((berth) {
              bool isInroute = inrouteBerths.contains(berth);
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Increased padding
                margin: EdgeInsets.all(2.0),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color.fromARGB(31, 131, 131, 131),
                    width: 1.0, // Slightly thicker border
                  ),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isInroute ? "($berth)" : "$berth" ,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isInroute ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              );
            }).toList(),
          ),
    );
  }
}