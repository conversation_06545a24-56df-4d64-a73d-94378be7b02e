import 'dart:typed_data';
import 'dart:io' as io;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/train_details/widgets/station_details_table.dart';
import 'package:railops/screens/train_details/widgets/summary_table.dart';
import "package:universal_html/html.dart" as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:railops/services/train_services/train_pdf_services.dart';
import 'package:railops/services/train_services/mail_service.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:universal_html/js.dart';
import 'package:railops/widgets/index.dart';
import 'package:path_provider/path_provider.dart';
import 'package:railops/services/notify_services/notify_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:open_filex/open_filex.dart';

class TrainDetailsTable extends StatefulWidget {
  final TrainDataResponse trainData;
  final UserModel userModel;

  const TrainDetailsTable(
      {Key? key, required this.trainData, required this.userModel})
      : super(key: key);

  @override
  _TrainDetailsTableState createState() => _TrainDetailsTableState();
}

class _TrainDetailsTableState extends State<TrainDetailsTable> {
  String? token;
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    token = widget.userModel.token;
    _initializeNotifications();
    _checkNotificationPermission();
  }

  Future<void> _checkNotificationPermission() async {
    if (!io.Platform.isAndroid) return;

    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);

    if (androidVersion >= 13) {
      final status = await Permission.notification.status;
      setState(() {
        _notificationsEnabled = status == PermissionStatus.granted;
      });
    } else {
      setState(() {
        _notificationsEnabled = true;
      });
    }
  }

  Future<void> _requestNotificationPermission() async {
    if (!io.Platform.isAndroid) return;

    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);

    if (androidVersion >= 13) {
      final status = await Permission.notification.request();
      setState(() {
        _notificationsEnabled = status == PermissionStatus.granted;
      });
    }
  }

  Future<void> _initializeNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const initializationSettings =
        InitializationSettings(android: androidSettings);

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        final String? payload = response.payload;
        if (payload != null) {
          final result = await OpenFilex.open(payload);
          if (result.type != ResultType.done) {
            if (mounted) {
              ScaffoldMessenger.of(this.context).showSnackBar(
                SnackBar(
                    content: Text('Could not open file: ${result.message}')),
              );
            }
          }
        }
      },
    );
  }

  Future<void> _showNotification(String filePath) async {
    if (!_notificationsEnabled) {
      await _requestNotificationPermission();
      if (!_notificationsEnabled) {
        // If user denied notification permission, just return
        return;
      }
    }

    const androidDetails = AndroidNotificationDetails(
      'pdf_download_channel',
      'PDF Downloads',
      channelDescription: 'Notifications for downloaded PDF files',
      importance: Importance.high,
      priority: Priority.high,
      actions: [
        AndroidNotificationAction('open_file', 'Open File'),
      ],
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await flutterLocalNotificationsPlugin.show(
      0,
      'Download Complete',
      'Tap to open the PDF file',
      notificationDetails,
      payload: filePath,
    );
  }

  @override
  Widget build(BuildContext context) {
    double deviceWidth = MediaQuery.of(context).size.width;

    // Extract all station codes from the train data
    List<String> allStationCodes = widget.trainData.trainsData
        .map((trainDetail) => trainDetail.stationCode)
        .toList();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10.0),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      _downloadReport(
                        widget.trainData.trainsData[0].trainNumber,
                        widget.trainData.trainsData[0].date,
                        allStationCodes.join(','),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.teal,
                      padding: const EdgeInsets.all(10.0),
                      textStyle: const TextStyle(fontSize: 12.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      side: const BorderSide(color: Colors.black87, width: 0.5),
                    ),
                    child: const Text('Download PDF for all stations'),
                  ),
                  const SizedBox(width: 15.0),
                  ElevatedButton(
                    onPressed: () {
                      _sendMailReport(
                        widget.trainData.trainsData[0].trainNumber,
                        widget.trainData.trainsData[0].date,
                        allStationCodes,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.orange,
                      padding: const EdgeInsets.all(10.0),
                      textStyle: const TextStyle(fontSize: 12.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(5),
                      ),
                      side: const BorderSide(color: Colors.black87, width: 0.5),
                    ),
                    child: const Text('Mail PDF for all stations'),
                  ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                // maxHeight: 400,
                maxWidth: deviceWidth - 32,
              ),
              child: SummaryTable(trainData: widget.trainData),
            ),
          ),
          ...widget.trainData.trainsData.isEmpty
              ? [
                  const Text(
                    'No Users Onboarding',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ]
              : widget.trainData.trainsData.map((trainDetail) {
                  return Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Center(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (!trainDetail.details.isEmpty)
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ElevatedButton(
                                    onPressed: () {
                                      _downloadReport(
                                        trainDetail.trainNumber,
                                        trainDetail.date,
                                        trainDetail.stationCode,
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.white,
                                      foregroundColor: Colors.teal,
                                      padding: const EdgeInsets.all(10.0),
                                      textStyle:
                                          const TextStyle(fontSize: 12.0),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      side: const BorderSide(color: Colors.black87, width: 0.5),
                                    ),
                                    child: Text(
                                        'Download  PDF for ${trainDetail.stationCode}'),
                                  ),
                                  const SizedBox(width: 15.0),
                                  // Spacing between buttons
                                  ElevatedButton(
                                    onPressed: () {
                                      _sendMailReport(
                                        trainDetail.trainNumber,
                                        trainDetail.date,
                                        [trainDetail.stationCode],
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.white,
                                      foregroundColor: Colors.orange,
                                      padding: const EdgeInsets.all(10.0),
                                      textStyle:
                                          const TextStyle(fontSize: 12.0),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(5),
                                      ),
                                      side: const BorderSide(color: Colors.black87, width: 0.5),
                                    ),
                                    child: Text(
                                        'Mail PDF for ${trainDetail.stationCode}'),
                                  ),
                                ],
                              ),
                            const SizedBox(
                                height: 8.0), // Optional: add spacing
                            StationDetailsTable(trainDetail: trainDetail),
                          ],
                        ),
                      ));
                }).toList(),
        ],
      ),
    );
  }

  Future<void> _downloadReport(
      String trainNo, String date, String stationCode) async {
    try {
      ScaffoldMessenger.of(this.context).showSnackBar(
        const SnackBar(content: Text('Download Started!')),
      );
      final Uint8List pdfBytes =
          await TrainPdfService.downloadPdf(trainNo, date, stationCode);
      final fileName = '${trainNo}_${date}.pdf';
      final baseFileName = '${trainNo}_${date}';

      if (kIsWeb) {
        final blob = html.Blob([pdfBytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', fileName)
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        if (await _requestPermissions()) {
          String path = '/storage/emulated/0/Download/$fileName';

          int counter = 1;
          while (io.File(path).existsSync()) {
            path = '/storage/emulated/0/Download//${baseFileName}_$counter.pdf';
            counter++;
          }

          final file = io.File(path);
          await file.writeAsBytes(pdfBytes);
          await _triggerMediaScanner(path);

          await _showNotification(path);
          print(path);
          ScaffoldMessenger.of(this.context).showSnackBar(
            SnackBar(content: Text('PDF downloaded successfully to ${path}')),
          );
        }
      }
    } catch (e) {
      print(e.toString());
      ScaffoldMessenger.of(this.context).showSnackBar(
        SnackBar(content: Text('$e')),
      );
    } finally {}
  }

  Future<void> _triggerMediaScanner(String filePath) async {
    try {
      const platform = MethodChannel('com.biputri.railops/media_scanner');
      await platform.invokeMethod('scanMedia', {"path": filePath});
    } on PlatformException catch (e) {
      print("Failed to trigger media scanner: '${e.message}'.");
    }
  }

  Future<void> _sendMailReport(
      String trainNo, String date, List<String> stationCodes) async {
    try {
      loader(this.context, 'Sending mail...');
      final responseMessage = await MailService.sendMailOnboardingTableBtn(
        date: date,
        trainNo: trainNo,
        stations: stationCodes,
        token: token,
      );
      Navigator.of(this.context).pop();
      showSuccessModal(this.context, responseMessage, 'Mail Sent', () {});
    } catch (e) {
      Navigator.of(this.context).pop();
      showErrorModal(
          this.context, "Couldn't send email: $e", 'Mail Error', () {});
    } finally {
      Navigator.of(this.context).pop();
    }
  }

  Future<bool> _requestPermissions() async {
    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);
    if (io.Platform.isAndroid && !(androidVersion >= 13)) {
      if (await Permission.storage.request().isGranted) {
        return true;
      } else {
        print("Storage permission denied");
        return false;
      }
    } else {
      return true;
    }
  }
}
