import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:intl/intl.dart';
import 'package:railops/screens/train_details/widgets/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/models/auth_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'widgets/index.dart';
import 'package:geolocator/geolocator.dart';
import 'package:in_app_update/in_app_update.dart';

class TrainDetailsScreen extends StatefulWidget {
  @override
  _TrainDetailsScreenState createState() => _TrainDetailsScreenState();
}

class _TrainDetailsScreenState extends State<TrainDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late Future<TrainDataResponse>? _futureTrainData;
  String _trainNumber = '';
  String _trainName = '';
  String insideTrainNumber = '';
  String insideTrainDate = '';
  List<String> _selectedStations = [];
  String _selectedDate = '';
  bool _isSubmitted = false;
  bool _isPopupShown = false;
  bool _hasFetchedUpcomingStations = false;
  Map<String, Map<String, List<int>>> DetailsOnBoarding = {};
  Map<String, Map<String, List<int>>> DetailsOffBoarding = {};
  Future<Position>? _futurePosition;
  bool _hasCheckedTrainNumber = false;

  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkPopupCondition();
    });
    _futurePosition = _getCurrentLocation();
    _setInitialTrainNoFromUserModel();
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;

    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        // _trainNumber = trainNumber;
        insideTrainNumber = trainNumber;
      });
    }
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        setState(() {
          _updateInfo = updateInfo;
          _flexibleUpdateAvailable = true;
        });
      }
    } catch (e) {
      print("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        print("Error starting flexible update: $e");
      }
    }
  }

  // Complete the flexible update
  Future<void> _completeUpdate() async {
    try {
      await InAppUpdate.completeFlexibleUpdate();
      setState(() {
        _flexibleUpdateAvailable = false;
      });
    } catch (e) {
      print("Error completing update: $e");
    }
  }

  Future<void> _handleRefresh() async {
    // Re-fetch position and upcoming station details
    Position position = await _getCurrentLocation();
    await getUpcomingStationDetails(position);

    // Simulate some delay (for demonstration purposes, you can remove this later)
    await Future.delayed(const Duration(seconds: 2));
  }

  void _handleFormSubmit(String? trainNumber, String? trainName,
      List<String> selectedStations, DateTime? selectedDate) async {
    DateFormat formatter = DateFormat('yyyy-MM-dd');
    String formattedDate = formatter.format(selectedDate!);

    setState(() {
      _trainNumber = trainNumber!;
      _trainName = trainName!;
      _selectedStations =
          selectedStations.where((station) => station != 'Select All').toList();
      _selectedDate = formattedDate;
      _futureTrainData = TrainService.fetchTrainData(
        trainNumber: trainNumber,
        date: formattedDate,
        stationCode:
            _selectedStations.isNotEmpty ? _selectedStations.join(',') : '',
      );
      _isSubmitted = true;
    });
  }

  Future<void> _checkPopupCondition() async {
    final prefs = await SharedPreferences.getInstance();
    final lastShownDate = prefs.getString('lastPopupShownDate');
    final now = DateTime.now();

    if (lastShownDate != now.toIso8601String().split('T').first &&
        now.hour >= 12 &&
        !_isPopupShown) {
      _showTrainPopup();
      await prefs.setString(
          'lastPopupShownDate', now.toIso8601String().split('T').first);
    }
  }

  Future<void> getUpcomingStationDetails(Position position) async {
    try {
      if (_hasFetchedUpcomingStations) return; // Prevent multiple calls
      _hasFetchedUpcomingStations = true; // Set the flag after first fetch

      String lat = position.latitude.toString();
      String lng = position.longitude.toString();

      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      UpcomingStationResponse response =
          await UpcomingStationService.fetchUpcomingStationDetails(
        lat: lat,
        lng: lng,
        token: token,
      );

      setState(() {
        DetailsOnBoarding = response.details;
        DetailsOffBoarding = response.detailsOffBoarding;
        insideTrainNumber = response.trainNumber;
        insideTrainDate = response.date;
        _hasCheckedTrainNumber = true;
      });

      final result = await TrainService.getTrainStations(response.trainNumber);
      List<String> stationNames = result['stationList'];

      _handleFormSubmit(response.trainNumber, "", stationNames,
          DateTime.tryParse(response.date));
    } catch (e) {
      setState(() {
        DetailsOnBoarding = {};
        DetailsOffBoarding = {};
        // insideTrainNumber = '';
        insideTrainDate = '';
        _hasCheckedTrainNumber = true;
      });
      print('Error: $e');
    }
  }

  Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      bool userResponse = await showDialog(
        // ignore: use_build_context_synchronously
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Location Services Disabled'),
            content: const Text('Please enable location services to proceed.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  await Geolocator.openLocationSettings();
                  // ignore: use_build_context_synchronously
                  Navigator.of(context).pop(true);
                },
                child: const Text('Enable'),
              ),
            ],
          );
        },
      );

      if (!userResponse) {
        return Future.error('Location services are disabled.');
      }
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        bool userResponse = await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Location Permission Denied'),
              content: const Text(
                  'Location permissions are required to fetch upcoming station details. Please allow location access.'),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () async {
                    await Geolocator.openAppSettings();
                    Navigator.of(context).pop(true);
                  },
                  child: const Text('Open Settings'),
                ),
              ],
            );
          },
        );

        if (!userResponse) {
          return Future.error('Location permissions are denied.');
        }
      }
    }

    if (permission == LocationPermission.deniedForever) {
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: const Text('Location Permission Denied Forever'),
            content: const Text(
                'Location permissions are permanently denied. Please go to app settings to allow location access.'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () async {
                  await Geolocator.openAppSettings();
                  Navigator.of(context).pop();
                },
                child: const Text('Open Settings'),
              ),
            ],
          );
        },
      );
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    Position position = await Geolocator.getCurrentPosition();

    setState(() {
      _futurePosition = Future.value(position);
    });

    return position;
  }

  void _showTrainPopup() {
    if (_isPopupShown) return;
    _isPopupShown = true;

    showDialog(
      context: context,
      builder: (context) {
        return TrainPopup();
      },
    ).then((_) async {
      _isPopupShown = false;
      _hasFetchedUpcomingStations = false;
      Position position = await _futurePosition!;
      await getUpcomingStationDetails(position);
    });
  }

  Future<void> _reloadPage() async {
   try{
        if (_trainNumber.isNotEmpty && _selectedDate.isNotEmpty && _selectedStations.isNotEmpty) {
        DateFormat formatter = DateFormat('yyyy-MM-dd');

        setState(() {
          _futureTrainData = TrainService.fetchTrainData(
            trainNumber: _trainNumber,
            date: _selectedDate,
            stationCode:
                _selectedStations.isNotEmpty ? _selectedStations.join(',') : '',
          );
          _isSubmitted = true;
        });
        }
      }
    catch(e){
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Refresh failed: ${e}')),
        );
      }
  }


  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate(); // Start update when available
      });
    }
    return Scaffold(
      appBar: const CustomAppBar(title: 'Train details'),
      drawer: const CustomDrawer(),
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _reloadPage, // Refresh function that gets called
        child: SingleChildScrollView(
          physics:
              const AlwaysScrollableScrollPhysics(), // Ensure scrolling is enabled
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context)
                  .size
                  .height, // Ensures it fills screen height
            ),
            child: Column(
              children: [
                const SizedBox(height: 10),
                const Text(
                  'Passenger Chart',
                  style: TextStyle(fontSize: 18.0),
                  textAlign: TextAlign.center,
                ),
                if (_hasCheckedTrainNumber)
                  Container(
                    height: 250,
                    width: MediaQuery.of(context).size.width,
                    color: Colors.green,
                    child: TrainFiltersWithStation(
                      formKey: _formKey,
                      onSubmit: _handleFormSubmit,
                      initialTrainNumber: insideTrainNumber,
                      initialDate: insideTrainDate,
                    ),
                  ),
                FutureBuilder<Position>(
                  future: _futurePosition,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            'Please turn on location services to see upcoming station onboarding/offboarding details.',
                            style: TextStyle(color: Colors.red, fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      );
                    } else if (snapshot.hasData) {
                      if (!_hasFetchedUpcomingStations) {
                        getUpcomingStationDetails(snapshot.data!);
                      }
                      if (_hasCheckedTrainNumber) {
                        if (insideTrainNumber.isNotEmpty) {
                          return UpcomingTableWidget(
                            details: DetailsOnBoarding,
                            detailsOffBoarding: DetailsOffBoarding,
                          );
                        } else {
                          return Container();
                        }
                      } else {
                        return const Center(child: CircularProgressIndicator());
                      }
                    } else {
                      return const Center(
                        child: Text('No location data available.'),
                      );
                    }
                  },
                ),
                _isSubmitted
                    ? Padding(
                        padding: const EdgeInsets.all(0.0),
                        child: FutureBuilder<TrainDataResponse>(
                          future: _futureTrainData,
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            } else if (snapshot.hasError) {
                              return Center(
                                  child: Text('Error: ${snapshot.error}'));
                            } else if (!snapshot.hasData ||
                                snapshot.data!.trainsData.isEmpty) {
                              return const Center(
                                  child: Text('No data available'));
                            } else {
                              final authModel = Provider.of<AuthModel>(context,
                                  listen: false);
                              if (authModel.isAuthenticated) {
                                return SingleChildScrollView(
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Column(
                                      children: [
                                        Center(
                                          child: TrainDetailsTable(
                                            trainData: snapshot.data!,
                                            userModel:
                                                Provider.of<UserModel>(context),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              } else {
                                WidgetsBinding.instance.addPostFrameCallback(
                                  (_) {
                                    Navigator.pushReplacementNamed(
                                        context, Routes.login);
                                  },
                                );
                                return const CircularProgressIndicator();
                              }
                            }
                          },
                        ),
                      )
                    : Container(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
