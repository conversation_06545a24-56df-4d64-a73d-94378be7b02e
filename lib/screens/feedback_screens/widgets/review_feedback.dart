import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart';
import 'package:railops/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart';
import 'package:railops/services/feedback_services/confirm_email.dart';
import 'package:railops/services/feedback_services/delete_feedback.dart';
import 'package:railops/services/feedback_services/fetch_feedbacks.dart';
import 'package:railops/services/feedback_services/update_feedback.dart';
import 'package:railops/services/feedback_services/verify_email.dart';
import 'package:railops/widgets/loader.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class FeedbackTable extends StatefulWidget {
  final String date;
  final String token;
  final String trainNumber;
  final String selectedCategory;
  final int currentTimeInSeconds;

  const FeedbackTable({
    Key? key,
    required this.date,
    required this.token,
    required this.trainNumber,
    required this.currentTimeInSeconds,
    required this.selectedCategory,
  }) : super(key: key);

  @override
  _FeedbackTableState createState() => _FeedbackTableState();
}

class _FeedbackTableState extends State<FeedbackTable> {
  late Future<List<dynamic>> feedbackFuture;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    fetchFeedbackData();
  }

  void fetchFeedbackData() {
    feedbackFuture = FeedbackService.fetchFeedback(
      widget.date,
      widget.token,
      widget.trainNumber,
    ).then((data) {
      if (!mounted) return <dynamic>[];
      if (data is List) return data;
      if (data is Map<String, dynamic> && data.containsKey('feedbacks')) {
        var feedbacks = data['feedbacks'];
        if (feedbacks is String) {
          try {
            return jsonDecode(feedbacks) as List<dynamic>;
          } catch (e) {
            print("Error parsing feedbacks as JSON: $e");
            return <dynamic>[];
          }
        } else if (feedbacks is List) {
          return feedbacks;
        }
      }
      if (data is Map<String, dynamic> && data.containsKey('data')) {
        var feedbackData = data['data'];
        if (feedbackData is List) return feedbackData;
      }
      return <dynamic>[];
    }).catchError((error) {
      print("Error fetching feedback: $error");
      return <dynamic>[];
    });
  }

  void fetchRmFeedbackData() {
    feedbackFuture = FeedbackService.fetchRmFeedback(
      widget.token,
    ).then((data) {
      if (!mounted) return <dynamic>[];
      if (data is List) return data;
      if (data is Map<String, dynamic> && data.containsKey('feedbacks')) {
        var feedbacks = data['feedbacks'];
        if (feedbacks is String) {
          try {
            return jsonDecode(feedbacks) as List<dynamic>;
          } catch (e) {
            print("Error parsing feedbacks as JSON: $e");
            return <dynamic>[];
          }
        } else if (feedbacks is List) {
          return feedbacks;
        }
      }
      if (data is Map<String, dynamic> && data.containsKey('data')) {
        var feedbackData = data['data'];
        if (feedbackData is List) return feedbackData;
      }
      return <dynamic>[];
    }).catchError((error) {
      print("Error fetching feedback: $error");
      return <dynamic>[];
    });
  }

  String formatDate(String date) {
    DateTime dateTime = DateTime.parse(date).toLocal();
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    return formattedDate;
  }

  void _showReviewDialog(Map<String, dynamic> feedback) {
    final String category = feedback['category'] is String
        ? feedback['category'].toString().toUpperCase()
        : '';

    if (!mounted) return;

    final currentContext = context;

    showDialog(
      context: currentContext,
      builder: (BuildContext dialogContext) {
        if (category == 'RM') {
          return RMReviewFeedbackDialog(
            feedback: feedback,
            onFeedbackUpdated: () {
              if (mounted) {
                setState(() {
                  fetchRmFeedbackData();
                });
              }
            },
          );
        } else {
          return FeedbackReviewDialog(
            feedback: feedback,
            onFeedbackUpdated: () {
              if (mounted) {
                setState(() {
                  fetchFeedbackData();
                });
              }
            },
          );
        }
      },
    );
  }

  Widget _buildMediaSection(Map<String, dynamic> feedback) {
    List<dynamic> pnrImages = [];
    List<dynamic> feedbackMedia = [];

    bool modernMediaExists = false;
    if (feedback['media_files'] != null && feedback['media_files'] is List) {
      modernMediaExists = true;
      for (var media in feedback['media_files']) {
        if (media is Map<String, dynamic> && media['is_pnr_image'] == true) {
          pnrImages.add(media);
        } else if (media is Map<String, dynamic>) {
          feedbackMedia.add(media);
        }
      }
    }

    if (!modernMediaExists || pnrImages.isEmpty) {
      if (feedback['pnr_image'] != null &&
          feedback['pnr_image'] is String &&
          feedback['pnr_image'].isNotEmpty) {
        pnrImages.add({'file_url': feedback['pnr_image']});
      }
      if (feedback['pnr_images'] != null && feedback['pnr_images'] is List) {
        for (var url in feedback['pnr_images']) {
          if (url is String && url.isNotEmpty) {
            pnrImages.add({'file_url': url});
          }
        }
      }
    }

    if (feedbackMedia.isEmpty) {
      if (feedback['media_url'] != null &&
          feedback['media_url'] is String &&
          feedback['media_url'].isNotEmpty) {
        feedbackMedia.add({'file_url': feedback['media_url']});
      } else if (feedback['media_url'] != null &&
          feedback['media_url'] is List) {
        for (var url in feedback['media_url']) {
          if (url is String &&
              url.isNotEmpty &&
              !pnrImages.any((item) => item['file_url'] == url)) {
            feedbackMedia.add({'file_url': url});
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (pnrImages.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'PNR Document:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...pnrImages
                  .map((media) => _buildMediaPreview(media['file_url']))
                  .toList(),
              const Divider(thickness: 1),
            ],
          ),
        if (feedbackMedia.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Feedback Media:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...feedbackMedia
                  .map((media) => _buildMediaPreview(media['file_url']))
                  .toList(),
            ],
          ),
      ],
    );
  }

  Widget _buildMediaPreview(dynamic media) {
    String? mediaUrl;
    if (media is String) {
      mediaUrl = media;
    } else if (media is Map<String, dynamic> && media.containsKey('file_url')) {
      final url = media['file_url'];
      if (url is String) {
        mediaUrl = url;
      } else if (url is List && url.isNotEmpty && url.first is String) {
        mediaUrl = url.first;
      }
    }
    if (mediaUrl == null || mediaUrl.isEmpty) {
      return const SizedBox.shrink();
    }
    final bool isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    final bool isVideo = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    if (isImage) {
      return Container(
        height: 200,
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            mediaUrl,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[300],
                alignment: Alignment.center,
                child: const Icon(Icons.error_outline),
              );
            },
          ),
        ),
      );
    } else if (isVideo) {
      return VideoThumbnail(videoUrl: mediaUrl);
    }
    return const SizedBox.shrink();
  }

  void deleteFeedbackExample(feedbackId, BuildContext context) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text("Deleting feedback..."),
            ],
          ),
        );
      },
    );

    try {
      await DeleteFeedback.deleteFeedback(
        feedbackId: feedbackId,
        token: userModel.token,
      );

      Navigator.of(context, rootNavigator: true).pop();

      if (mounted) {
        setState(() {
          fetchFeedbackData();
        });
      }

      scaffoldMessenger.showSnackBar(
        const SnackBar(content: Text("Feedback deleted successfully")),
      );
    } catch (e) {
      if (Navigator.canPop(context)) {
        Navigator.of(context, rootNavigator: true).pop();
      }
      scaffoldMessenger.showSnackBar(
        SnackBar(content: Text("Error deleting feedback: $e")),
      );
    }
  }

  Color getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'ac':
        return Colors.blue;
      case 'nonac':
        return Colors.green;
      case 'tt':
        return Colors.orange;
      case 'rm':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  Widget _buildCategoryBadge(String category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
      decoration: BoxDecoration(
        color: getCategoryColor(category),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        category.toUpperCase(),
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<dynamic>>(
      future: feedbackFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 48, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Error: ${snapshot.error}',
                  style: const TextStyle(color: Colors.red),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (mounted) {
                      setState(() {
                        fetchFeedbackData();
                      });
                    }
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }
        if (!snapshot.hasData ||
            snapshot.data == null ||
            snapshot.data!.isEmpty) {
          return const Center(
              child: Text('No feedback available for this train.'));
        }

        return SingleChildScrollView(
          child: Column(
            children: snapshot.data!.map((feedback) {
              if (feedback is! Map<String, dynamic>) {
                return const SizedBox.shrink();
              }

              final String passengerName = feedback['passenger_name'] is String
                  ? feedback['passenger_name']
                  : 'N/A';

              final String trainNumber = widget.trainNumber.toString();

              final String time =
                  feedback['time'] is String ? feedback['time'] : '';

              final String mobileNumber = feedback['mobile_no'] != null
                  ? feedback['mobile_no'].toString()
                  : 'N/A';

              final String crnNo = feedback['crn_no'] != null
                  ? feedback['crn_no'].toString()
                  : 'N/A';

              final String status =
                  feedback['status'] is String ? feedback['status'] : '';

              final String category =
                  feedback['category'] is String ? feedback['category'] : '';

              final String date = widget.date.toString();

              final String comment =
                  feedback['comment'] is String ? feedback['comment'] : '';

              final bool verified = feedback['verified'] == true;

              final String issueType = feedback['issue_type'] is String
                  ? feedback['issue_type']
                  : 'N/A';

              final String subIssueType = feedback['sub_issue_type'] is String
                  ? feedback['sub_issue_type']
                  : 'N/A';

              final String emailId =
                  feedback['email'] is String ? feedback['email'] : 'N/A';

              final bool resolved = feedback['resolved'] == true;

              final String marksGiven = feedback['rating_out_of_10'] != null
                  ? feedback['rating_out_of_10'].toString()
                  : '';

              final String coachNo =
                  feedback['coach'] is String ? feedback['coach'] : 'N/A';

              final String berthNo = feedback['berth_no'] != null
                  ? feedback['berth_no'].toString()
                  : '';

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                elevation: 5,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              passengerName,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blueAccent,
                                  ),
                            ),
                          ),
                          _buildCategoryBadge(category),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.train, size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text('Train No: $trainNumber'),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(Icons.date_range,
                              size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                              '${DateFormat('yyyy-MM-dd').format(DateTime.parse(date))}'),
                          if (time.isNotEmpty) ...[
                            const SizedBox(width: 6),
                            Text(time),
                          ],
                        ],
                      ),
                      const SizedBox(height: 12),
                      _buildMediaSection(feedback),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                              'Status: ${status == 'P' ? 'Pending' : 'Completed'}'),
                        ],
                      ),
                      if (feedback.containsKey('verified')) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              verified ? Icons.verified : Icons.not_interested,
                              size: 16,
                              color: verified ? Colors.green : Colors.red,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Email: ${verified ? 'Verified' : 'Not Verified'}',
                              style: TextStyle(
                                color: verified ? Colors.green : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ],
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: () => _showReviewDialog(feedback),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Review',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                          ElevatedButton(
                            onPressed: () =>
                                deleteFeedbackExample(feedback['id'], context),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 24, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text(
                              'Delete',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
