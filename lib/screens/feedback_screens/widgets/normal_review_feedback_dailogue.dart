import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/services/feedback_services/confirm_email.dart';
import 'package:railops/services/feedback_services/update_feedback.dart';
import 'package:railops/services/feedback_services/verify_email.dart';
import 'package:railops/widgets/loader.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class FeedbackReviewDialog extends StatefulWidget {
  final Map<String, dynamic> feedback;
  final Function onFeedbackUpdated;

  const FeedbackReviewDialog({
    Key? key,
    required this.feedback,
    required this.onFeedbackUpdated,
  }) : super(key: key);

  @override
  _FeedbackReviewDialogState createState() => _FeedbackReviewDialogState();
}

class _FeedbackReviewDialogState extends State<FeedbackReviewDialog> {
  late TextEditingController passengerNameController;
  late TextEditingController pnrNoController;
  late TextEditingController mobileNoController;
  late TextEditingController coachNoController;
  late TextEditingController berthNoController;
  late TextEditingController emailIdController;
  late TextEditingController otpController;
  late TextEditingController commentController;
  late TextEditingController trainNumberController;
  late TextEditingController trainNameController;

  late List<int?> feedbackRatings = [];
  late List<String> feedbackItems = [];
  String? selectedCategory;
  String? selectedStatus;
  bool isEmailVerified = false;
  bool showOtpInput = false;
  bool isEditable = false;
  String feedbackMessage = '';
  String formattedDate = '';
  VideoPlayerController? videoPlayerController;
  ChewieController? chewieController;
  bool isVideoPlaying = false;
  File? _pnrImage;
  final ImagePicker _picker = ImagePicker();
  String? originalStatus;

  @override
  void initState() {
    super.initState();
    trainNumberController =
        TextEditingController(text: widget.feedback['train_number']);
    trainNameController =
        TextEditingController(text: widget.feedback['train_name']);
    passengerNameController =
        TextEditingController(text: widget.feedback['passenger_name']);
    pnrNoController = TextEditingController(text: widget.feedback['pnr_no']);
    mobileNoController =
        TextEditingController(text: widget.feedback['mobile_no']);
    coachNoController = TextEditingController(text: widget.feedback['coach']);
    var berthNoValue = widget.feedback['berth_no'];
    berthNoController = TextEditingController(
        text: berthNoValue is int
            ? berthNoValue.toString()
            : (berthNoValue ?? ''));
    emailIdController = TextEditingController(text: widget.feedback['email']);
    otpController = TextEditingController();
    commentController =
        TextEditingController(text: widget.feedback['comment'] ?? '');

    selectedStatus = widget.feedback['status'];
    originalStatus = widget.feedback['status'];
    formattedDate = widget.feedback['date'];
    isEmailVerified = widget.feedback['verified'] == true; // for verified badge
    isEditable = selectedStatus == 'P';
    selectedCategory = widget.feedback['category']?.toString() ?? '';
    feedbackItems = getFilteredFeedbackItems(selectedCategory!);
    feedbackRatings =
        List<int?>.filled(feedbackItems.length, null, growable: true);

    for (int i = 1; i <= feedbackItems.length; i++) {
      String fieldName = 'feedback_value_$i';
      if (widget.feedback.containsKey(fieldName)) {
        var value = widget.feedback[fieldName];
        if (value != null) {
          if (value is int) {
            feedbackRatings[i - 1] = value;
          } else if (value is String) {
            feedbackRatings[i - 1] = int.tryParse(value);
          }
        }
      }
    }
  }

  @override
  void dispose() {
    passengerNameController.dispose();
    pnrNoController.dispose();
    mobileNoController.dispose();
    coachNoController.dispose();
    berthNoController.dispose();
    emailIdController.dispose();
    otpController.dispose();
    commentController.dispose();
    trainNumberController.dispose();
    trainNameController.dispose();
    videoPlayerController?.dispose();
    chewieController?.dispose();
    super.dispose();
  }

  List<String> getFilteredFeedbackItems(String category) {
    switch (category) {
      case 'AC':
        return [
          'Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc',
          'Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)',
          'Collection of gargage from the coach compartments and clearance of dustbins.',
          'Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers',
          'Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/'
        ];
      case 'NONAC':
        return [
          'Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.',
          'Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin)',
          'Behavior of Janitors / Supervisor (Including hygiene & cleanliness of'
        ];
      case 'TT':
        return [
          'Cleaning of toilets (including tollet floor, commode pan, wall panels, shelf, miror, wash basin. Disinfectionnand provision of deodorant etc',
          'Cleaning of Passenger Compartment (including cleaning or passenger aisle, Vestibule area,, Doorway area and doorway wash basin, spraying of air freshene and cleaning of dust bin)',
          'Collection of gargage from the coach compartments and clearance of dustbins.',
          'Spraying of Mosquito/Cockroach/Fly Repellent and Providing Glue Board Whenever required or on demand by passengers',
          'Behavior/Response of Janitors / Supervisor (Including hygiene & cleanliness of Janitor/',
          'Cleaning of toilets, Wash Basin and other fittings (including Disinfection and provision of deodorant etc.',
          'Complete Cleaning of Passenger Compartment (Including spraying of air freshener and cleaning of dust bin)',
          'Behavior of Janitors / Supervisor (Including hygiene & cleanliness of'
        ];
      default:
        return [];
    }
  }

  Widget _buildMediaPreview(dynamic media) {
    String? mediaUrl;
    if (media is String) {
      mediaUrl = media;
    } else if (media is Map<String, dynamic> && media.containsKey('file_url')) {
      var url = media['file_url'];
      if (url is String) {
        mediaUrl = url;
      } else if (url is List && url.isNotEmpty && url.first is String) {
        mediaUrl = url.first;
      }
    }
    if (mediaUrl == null || mediaUrl.isEmpty) {
      return const SizedBox.shrink();
    }
    final bool isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    final bool isVideo = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    if (isImage) {
      return Container(
        height: 200,
        width: double.infinity,
        margin: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            mediaUrl,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[300],
                alignment: Alignment.center,
                child: const Icon(Icons.error_outline),
              );
            },
          ),
        ),
      );
    } else if (isVideo) {
      return VideoThumbnail(videoUrl: mediaUrl);
    }
    return const SizedBox.shrink();
  }

  Widget _buildMediaSection(Map<String, dynamic> feedback) {
    List<dynamic> pnrImages = [];
    List<dynamic> feedbackMedia = [];

    bool modernMediaExists = false;
    if (feedback['media_files'] != null && feedback['media_files'] is List) {
      modernMediaExists = true;
      for (var media in feedback['media_files']) {
        if (media is Map<String, dynamic> && media['is_pnr_image'] == true) {
          pnrImages.add(media);
        } else if (media is Map<String, dynamic>) {
          feedbackMedia.add(media);
        }
      }
    }
    if (!modernMediaExists || pnrImages.isEmpty) {
      if (feedback['pnr_image'] != null &&
          feedback['pnr_image'] is String &&
          feedback['pnr_image'].isNotEmpty) {
        pnrImages.add({'file_url': feedback['pnr_image']});
      }
      if (feedback['pnr_images'] != null && feedback['pnr_images'] is List) {
        for (var url in feedback['pnr_images']) {
          if (url is String && url.isNotEmpty) {
            pnrImages.add({'file_url': url});
          }
        }
      }
    }
    if (feedbackMedia.isEmpty) {
      if (feedback['media_url'] != null &&
          feedback['media_url'] is String &&
          feedback['media_url'].isNotEmpty) {
        feedbackMedia.add({'file_url': feedback['media_url']});
      } else if (feedback['media_url'] != null &&
          feedback['media_url'] is List) {
        for (var url in feedback['media_url']) {
          if (url is String &&
              url.isNotEmpty &&
              !pnrImages.any((item) => item['file_url'] == url)) {
            feedbackMedia.add({'file_url': url});
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (pnrImages.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'PNR Document:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...pnrImages
                  .map((media) => _buildMediaPreview(media['file_url']))
                  .toList(),
              const Divider(thickness: 1),
            ],
          ),
        if (feedbackMedia.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Text(
                'Feedback Media:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...feedbackMedia
                  .map((media) => _buildMediaPreview(media['file_url']))
                  .toList(),
            ],
          ),
      ],
    );
  }

  void _openFeedback(BuildContext context, String category) {
    setState(() {
      selectedCategory = widget.feedback[category];
      feedbackItems = getFilteredFeedbackItems(category);
      feedbackRatings = List<int?>.filled(feedbackItems.length, null);
    });
  }

  Widget buildFeedbackTable2(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ElevatedButton(
            onPressed: isEditable ? () => _openFeedback(context, 'AC') : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isEditable
                  ? (selectedCategory == 'AC'
                      ? Colors.blue
                      : Colors.blue.shade300)
                  : Colors.grey[300],
              foregroundColor: isEditable ? Colors.white : Colors.black54,
            ),
            child: const Text('AC'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed:
                isEditable ? () => _openFeedback(context, 'NONAC') : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isEditable
                  ? (selectedCategory == 'NONAC'
                      ? Colors.blue
                      : Colors.blue.shade300)
                  : Colors.grey[300],
              foregroundColor: isEditable ? Colors.white : Colors.black54,
            ),
            child: const Text('Non AC'),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: isEditable ? () => _openFeedback(context, 'TT') : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isEditable
                  ? (selectedCategory == 'TT'
                      ? Colors.blue
                      : Colors.blue.shade300)
                  : Colors.grey[300],
              foregroundColor: isEditable ? Colors.white : Colors.black54,
            ),
            child: const Text('TT'),
          ),
        ],
      ),
    );
  }

  Widget buildFeedbackTable() {
    final List<Map<String, dynamic>> ratings = [
      {'label': 'Excellent', 'value': 5},
      {'label': 'Very Good', 'value': 4},
      {'label': 'Good', 'value': 3},
      {'label': 'Average', 'value': 2},
      {'label': 'Poor', 'value': 1},
    ];

    final Map<int, Color> ratingColors = {
      5: Colors.green,
      4: Colors.yellow,
      3: Colors.red,
      2: Colors.red.shade900,
      1: Colors.red.shade800,
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          "Please tick (✓) in appropriate column",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 10),
        Table(
          border: TableBorder.all(),
          columnWidths: const {
            0: FlexColumnWidth(2.3),
            1: FlexColumnWidth(0.5),
            2: FlexColumnWidth(0.5),
            3: FlexColumnWidth(0.5),
            4: FlexColumnWidth(0.5),
            5: FlexColumnWidth(0.5),
            6: FlexColumnWidth(0.5),
            7: FlexColumnWidth(0.5),
          },
          children: [
            TableRow(
              children: [
                const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text(
                    "ITEM",
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                ...ratings.map((rating) => Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: RotatedBox(
                        quarterTurns: 3,
                        child: Text(
                          rating['label'],
                          style: const TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 13),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    )),
              ],
            ),
            ...List.generate(feedbackItems.length, (index) {
              return TableRow(
                children: [
                  Container(
                    height: 100,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.vertical,
                      child: Text(
                        feedbackItems[index],
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ),
                  ...ratings.map((rating) {
                    bool isSelected = feedbackRatings[index] == rating['value'];
                    return GestureDetector(
                      onTap: isEditable
                          ? () {
                              setState(() {
                                int? newValue =
                                    feedbackRatings[index] == rating['value']
                                        ? null
                                        : rating['value'];
                                feedbackRatings[index] = newValue;

                                // Update the individual field in the feedback object
                                if (newValue != null) {
                                  widget.feedback[
                                          'feedback_value_${index + 1}'] =
                                      newValue.toString();
                                } else {
                                  widget.feedback[
                                      'feedback_value_${index + 1}'] = null;
                                }
                              });
                            }
                          : null,
                      child: Container(
                        alignment: Alignment.center,
                        color: isSelected
                            ? ratingColors[rating['value']]
                            : Colors.white,
                        height: 110,
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.black,
                              )
                            : null,
                      ),
                    );
                  }).toList(),
                ],
              );
            })
          ],
        ),
      ],
    );
  }

  void showWarning(BuildContext context, String message, Color color) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          backgroundColor: color,
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                "OK",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _initializeVideo(String url) async {
    setState(() {
      isVideoPlaying = false; // Reset state while loading
    });

    videoPlayerController = VideoPlayerController.network(url);

    try {
      await videoPlayerController!.initialize();

      if (mounted) {
        chewieController = ChewieController(
          videoPlayerController: videoPlayerController!,
          autoPlay: true,
          looping: false,
          aspectRatio: videoPlayerController!.value.aspectRatio,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(
                    'Error: $errorMessage',
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        );

        setState(() {
          isVideoPlaying = true;
        });
      }
    } catch (e) {
      print("Error initializing video: $e");
      if (mounted) {
        setState(() {
          isVideoPlaying = false;
        });

        showWarning(
            context, "Failed to load video: ${e.toString()}", Colors.red);
      }
    }
  }

  void _showSpamInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber),
              SizedBox(width: 5),
              Text(
                "Email Verification Info",
                style: TextStyle(fontSize: 18, color: Colors.red),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Please note that verification emails may sometimes be delivered to your spam/junk folder.",
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 12),
              Text(
                "After requesting OTP:",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text("• Check your inbox first"),
              Text("• If not found, check spam/junk folder"),
              Text("• Add our domain to your safe sender list"),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text("I Understand"),
            ),
          ],
        );
      },
    );
  }

  void handleVerifyEmail() async {
    if (!isEditable) {
      showWarning(
          context, "Cannot verify email for completed feedback.", Colors.red);
      return;
    }

    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    if (emailIdController.text.isEmpty) {
      showWarning(context, "Please enter a valid Email ID.", Colors.red);
      return;
    }

    loader(context, "sending otp");

    try {
      await VerifyPassengerEmailService.verifyEmail(
        emailIdController.text,
        userToken,
      );

      setState(() {
        showOtpInput = true;
      });

      Navigator.of(context, rootNavigator: true).pop();
      showWarning(
        context,
        "Email verification initiated. Please check your inbox.",
        Colors.blue,
      );
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      showWarning(context, "Error: ${e.toString()}", Colors.red);
    }
  }

  void handleVerifyOtp(String formattedDate) async {
    if (!isEditable) {
      showWarning(
          context, "Cannot verify OTP for completed feedback.", Colors.red);
      return;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    if (otpController.text.isEmpty) {
      showWarning(context, "Please enter the OTP.", Colors.red);
      return;
    }

    loader(context, "verifying otp");

    try {
      await ConfirmPassengerEmailService.confirmEmail(
        token: userToken,
        date: formattedDate,
        email: emailIdController.text,
        otp: otpController.text,
      );

      if (mounted) {
        setState(() {
          showOtpInput = false;
          isEmailVerified = true;
          widget.feedback['verified'] = true;
        });

        Navigator.of(context, rootNavigator: true).pop();

        showWarning(context, "OTP verified successfully.", Colors.blue);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      if (mounted) {
        String errorMessage = e.toString();
        showWarning(
          context,
          "Error: ${errorMessage.isEmpty ? 'Something went wrong' : errorMessage}",
          Colors.red,
        );
      }
    }
  }

  Widget _buildEmailVerificationSection() {
    // First, determine if we should show the verification section at all
    bool shouldShowVerificationControls = widget.feedback['verified'] == false;
    bool shouldShowEmailControls = widget.feedback['verified'] == false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (!shouldShowEmailControls)
              Expanded(
                child: TextFormField(
                  controller: emailIdController,
                  decoration: const InputDecoration(
                    labelText: "Email ID",
                    border: OutlineInputBorder(),
                  ),
                  readOnly: !isEditable || widget.feedback['verified'] == true,
                  enabled: isEditable && widget.feedback['verified'] != true,
                ),
              ),
            // Show info icon only if editable and not verified
            if (!shouldShowVerificationControls &&
                isEmailVerified &&
                isEditable &&
                !showOtpInput)
              IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                ),
                onPressed: () {
                  _showSpamInfoDialog();
                },
              ),
          ],
        ),

        // Only show verification button if editable and not verified
        if (!shouldShowVerificationControls &&
            isEmailVerified &&
            isEditable) ...[
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: handleVerifyEmail,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text("Verify Email"),
          ),
        ],

        // Only show OTP section if we're in OTP input mode, editable, and not verified
        if (showOtpInput && shouldShowVerificationControls) ...[
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.mark_email_unread, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    "OTP sent to your email. Please check both inbox and spam folders.",
                    style: TextStyle(color: Colors.blue.shade700),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          TextFormField(
            controller: otpController,
            decoration: const InputDecoration(
              labelText: "Enter OTP",
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            enabled: isEditable,
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () => handleVerifyOtp(formattedDate),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text("Verify OTP"),
          ),
        ],

        // Always show the verified indicator if the email is verified
        if (isEmailVerified)
          const Row(
            children: [
              Icon(Icons.verified, color: Colors.green),
              SizedBox(width: 5.0),
              Text(
                "verified",
                style: TextStyle(color: Colors.green),
              ),
            ],
          ),
      ],
    );
  }

  int _countCharacters(String text) {
    return text.length;
  }

  void updateFeedbackStatus() async {
    if (!isEditable && selectedStatus == widget.feedback['status']) {
      Navigator.of(context).pop();
      return;
    }

    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    final feedbackId = widget.feedback['id'];
    final data = widget.feedback;
    widget.feedback['comment'] = commentController.text;
    widget.feedback['status'] = selectedStatus;

    loader(context, "Updating feedback...");

    final response = await UpdateFeedback.updateFeedback(
      feedbackId: feedbackId,
      data: data,
      token: token,
    );

    if (response['success']) {
      if (selectedStatus == 'C' && widget.feedback['status'] == 'P') {
        setState(() {
          isEditable = false;
        });
      }

      Navigator.pushReplacementNamed(context, Routes.passengerFeedbackScreen);
      showWarning(context, "Feedback updated successfully", Colors.blue);
    } else {
      print('Failed to update feedback: ${response['message']}');
      if (response.containsKey('errors')) {
        print('Errors: ${response['errors']}');
      }

      setState(() {
        selectedStatus = widget.feedback['status'];
      });

      Navigator.of(context, rootNavigator: true).pop();
      showWarning(context, "Failed to update feedback", Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Review Feedback'),
      insetPadding: const EdgeInsets.all(10),
      contentPadding: const EdgeInsets.all(10),
      titlePadding: const EdgeInsets.all(10.0),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status indicator
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 15),
                decoration: BoxDecoration(
                  color: isEditable
                      ? Colors.blue.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isEditable ? Colors.blue : Colors.red,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      isEditable ? Icons.edit : Icons.lock,
                      color: isEditable ? Colors.blue : Colors.red,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isEditable
                          ? "Status Pending - Editable"
                          : "Status Completed - Not Editable",
                      style: TextStyle(
                        color: isEditable ? Colors.blue : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              if (feedbackMessage.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(10),
                  color: Colors.blue[100],
                  child: Text(
                    feedbackMessage,
                    style: const TextStyle(color: Colors.blue),
                  ),
                ),
              const SizedBox(height: 10),

              // Media viewer
              _buildMediaSection(widget.feedback),
              const SizedBox(height: 10),

              TextFormField(
                controller: passengerNameController,
                decoration: const InputDecoration(
                  labelText: "Passenger Name",
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['passenger_name'] = value;
                    });
                  }
                },
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: pnrNoController,
                decoration: const InputDecoration(
                  labelText: 'PNR Number',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['pnr_no'] = value;
                    });
                  }
                },
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: coachNoController,
                      decoration: const InputDecoration(
                        labelText: "Coach No",
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (isEditable) {
                          setState(() {
                            widget.feedback['coach'] = value;
                          });
                        }
                      },
                      readOnly: !isEditable,
                      enabled: isEditable,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: berthNoController,
                      decoration: const InputDecoration(
                        labelText: 'Berth No',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (isEditable) {
                          setState(() {
                            int? parsedValue = int.tryParse(value);
                            widget.feedback['berth_no'] = parsedValue ?? 0;
                          });
                        }
                      },
                      keyboardType: TextInputType.number,
                      readOnly: !isEditable,
                      enabled: isEditable,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: mobileNoController,
                decoration: const InputDecoration(
                  labelText: 'Mobile Number',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['mobile_no'] = value;
                    });
                  }
                },
                keyboardType: TextInputType.number,
                readOnly: !isEditable,
                enabled: isEditable,
              ),

              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: "Task Status",
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(
                    value: 'P',
                    child: Text("Pending"),
                  ),
                  DropdownMenuItem(
                    value: 'C',
                    child: Text("Completed"),
                  ),
                ],
                onChanged: isEditable
                    ? (value) {
                        if (value == null) return;
                        setState(() {
                          selectedStatus = value;
                        });
                      }
                    : null,
              ),
              const SizedBox(height: 10),
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 5.0, vertical: 5.0),
                  child: Stack(
                    children: [
                      TextField(
                        controller: commentController,
                        maxLines: 2,
                        style: const TextStyle(fontSize: 14),
                        decoration: const InputDecoration(
                          labelText: "Feedback",
                          hintText:
                              "Add your feedback here (max 100 Characters)...",
                          border: OutlineInputBorder(),
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r'[0-9]')), // Deny numeric input
                          LengthLimitingTextInputFormatter(100),
                        ],
                        onChanged: (value) {
                          if (_countCharacters(commentController.text) > 100) {
                            showWarning(
                              context,
                              "Feedback cannot exceed 100 characters",
                              Colors.red,
                            );
                          }
                          setState(() {});
                        },
                        readOnly: !isEditable,
                        enabled: isEditable,
                      ),
                      Positioned(
                        right: 8,
                        bottom: 4,
                        child: Text(
                          '${_countCharacters(commentController.text)}/100 characters', // Change to characters
                          style: TextStyle(
                            fontSize: 10,
                            color: _countCharacters(commentController.text) >
                                    100 // Change to character count
                                ? Colors.red
                                : Colors.grey[600],
                            fontWeight:
                                _countCharacters(commentController.text) >
                                        100 // Change to character count
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 12),
              _buildEmailVerificationSection(),
              const SizedBox(height: 10),
              buildFeedbackTable2(context),
              const SizedBox(height: 10),
              buildFeedbackTable(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isEditable
              ? () {
                  updateFeedbackStatus();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isEditable ? Colors.blue : Colors.grey,
          ),
          child: const Text('Update'),
        ),
      ],
    );
  }
}

class VideoThumbnail extends StatefulWidget {
  final String videoUrl;
  const VideoThumbnail({Key? key, required this.videoUrl}) : super(key: key);

  @override
  _VideoThumbnailState createState() => _VideoThumbnailState();
}

class _VideoThumbnailState extends State<VideoThumbnail> {
  late VideoPlayerController _videoController;
  ChewieController? _chewieController;
  bool _isPlaying = false;
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _videoController = VideoPlayerController.network(widget.videoUrl);
  }

  @override
  void dispose() {
    _videoController.dispose();
    _chewieController?.dispose();
    super.dispose();
  }

  void _initializePlayer() async {
    if (_isLoading) return;
    setState(() {
      _isLoading = true;
      _hasError = false;
    });
    try {
      await _videoController.initialize();
      if (mounted) {
        _chewieController = ChewieController(
          videoPlayerController: _videoController,
          autoPlay: true,
          looping: false,
          aspectRatio: _videoController.value.aspectRatio,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Text(
                'Error playing video: $errorMessage',
                style: const TextStyle(color: Colors.white),
              ),
            );
          },
        );
        setState(() {
          _isPlaying = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _hasError = true;
          _isLoading = false;
          _errorMessage = e.toString();
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: _isPlaying
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Chewie(controller: _chewieController!),
            )
          : GestureDetector(
              onTap: _isLoading ? null : _initializePlayer,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.black87,
                    ),
                    width: double.infinity,
                    height: double.infinity,
                  ),
                  if (_isLoading)
                    const CircularProgressIndicator(color: Colors.white)
                  else if (_hasError)
                    const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, color: Colors.red, size: 40),
                        SizedBox(height: 8),
                        Text(
                          'Failed to load video',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    )
                  else
                    const Icon(
                      Icons.play_circle_fill,
                      color: Colors.white,
                      size: 64,
                    ),
                ],
              ),
            ),
    );
  }
}
