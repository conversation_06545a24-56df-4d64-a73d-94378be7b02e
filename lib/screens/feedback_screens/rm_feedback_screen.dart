import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/services.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart';
import 'package:railops/services/feedback_services/confirm_email.dart';
import 'package:railops/services/feedback_services/feedback_submission.dart';
import 'package:railops/services/feedback_services/verify_email.dart';
import 'package:railops/services/profile_services/pnr_service.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/screens/feedback_screens/widgets/review_feedback.dart';
import 'package:railops/widgets/index.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

class RMPassengerFeedbackScreen extends StatefulWidget {
  final String date;
  final String token;
  final String trainNumber;
  final String trainName;
  final int currentTimeInSeconds;
  final String? pnrNumber;
  final String? passengerName;
  final String? coachNumber;
  final String? berthNumber;
  final String? mobileNumber;
  final String? selectedCategory;
  final bool isPNRVerified;

  const RMPassengerFeedbackScreen({
    Key? key,
    required this.date,
    required this.token,
    required this.trainNumber,
    required this.trainName,
    required this.currentTimeInSeconds,
    this.pnrNumber,
    this.passengerName,
    this.coachNumber,
    this.berthNumber,
    this.mobileNumber,
    required this.isPNRVerified,
    required this.selectedCategory,
  }) : super(key: key);

  @override
  _RMPassengerFeedbackScreenState createState() =>
      _RMPassengerFeedbackScreenState();
}

class _RMPassengerFeedbackScreenState extends State<RMPassengerFeedbackScreen> {
  bool _isWidgetActive = true;
  final _formKey = GlobalKey<FormState>();
  bool isEmailVerified = false;
  bool showOtpInput = false;
  bool loading = false;
  bool resolved = false;
  bool verified = false;
  bool isUploading = false;
  late bool _isPNRVerified;
  String taskStatus = 'pending';
  String _resolvedStatus = '';
  int _marksGiven = 0;
  Map<String, dynamic> issuesData = {
    'ISSUE_TYPES': <String>[],
    'SUB_ISSUE_TYPES': <List<dynamic>>[],
  };
  String? selectedIssue;
  String? selectedSubIssue;
  DateTime selectedDate = DateTime.now();
  int currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch;
  String formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
  final ImagePicker _imagePicker = ImagePicker();

  List<XFile> _selectedPNRFiles = [];
  List<XFile> _selectedFeedbackFiles = [];
  List<XFile> _selectedFeedbackVideos = [];

  // Controllers
  final TextEditingController pnrNoController = TextEditingController();
  final TextEditingController passengerNameController = TextEditingController();
  final TextEditingController coachNoController = TextEditingController();
  final TextEditingController berthNoController = TextEditingController();
  final TextEditingController trainNameController = TextEditingController();
  final TextEditingController trainNumberController = TextEditingController();
  final TextEditingController mobileNoController = TextEditingController();
  final TextEditingController emailIdController = TextEditingController();
  final TextEditingController otpController = TextEditingController();
  final TextEditingController remarksController = TextEditingController();
  final TextEditingController crnController = TextEditingController();
  final TextEditingController issueController = TextEditingController();
  final TextEditingController subIssueController = TextEditingController();

  List<String> _trainNumbers = [];

  @override
  void initState() {
    super.initState();
    _isWidgetActive = true;
    _fetchTrainNumbers();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeFormFields();
    });
    _fetchIssues();
  }

  @override
  void dispose() {
    _isWidgetActive = false;
    trainNumberController.dispose();
    passengerNameController.dispose();
    pnrNoController.dispose();
    coachNoController.dispose();
    berthNoController.dispose();
    trainNameController.dispose();
    remarksController.dispose();
    mobileNoController.dispose();
    crnController.dispose();
    issueController.dispose();
    subIssueController.dispose();
    emailIdController.dispose();
    otpController.dispose();
    super.dispose();
  }

  void _initializeFormFields() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    pnrNoController.text = widget.pnrNumber ?? '';
    passengerNameController.text = widget.passengerName ?? '';
    coachNoController.text = widget.coachNumber ?? '';
    berthNoController.text = widget.berthNumber ?? '';
    mobileNoController.text = widget.mobileNumber ?? '';
    trainNumberController.text = widget.trainNumber ?? '';
    trainNameController.text = widget.trainName ?? '';
    _isPNRVerified = widget.isPNRVerified;

    if (trainNumberController.text.isNotEmpty) {
      _onTrainNumberChanged(trainNumberController.text);
    }
  }

  // Fetch Train Numbers
  Future<void> _fetchTrainNumbers() async {
    try {
      if (!_isWidgetActive) return;
      setState(() => loading = true);
      _trainNumbers = await TrainService.getTrainNumbers();
      if (!_isWidgetActive) return;
      setState(() => loading = false);
    } catch (e) {
      if (!_isWidgetActive) return;
      setState(() => loading = false);
    }
  }

  // Train Number Change Handler
  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null && !_isPNRVerified) {
      setState(() {
        loading = true;
        trainNumberController.text = trainNumber;
      });

      try {
        final trainName = await fetchTrainName(trainNumber);

        if (!_isWidgetActive) return;
        setState(() {
          trainNameController.text = trainName ?? '';
          loading = false;
        });
        Provider.of<UserModel>(context, listen: false).setTrainNo(trainNumber);
      } catch (e) {
        if (!_isWidgetActive) return;
        setState(() {
          loading = false;
        });
        _showWarning(
          context,
          "Failed to fetch train name",
          const Color.fromARGB(255, 147, 208, 227),
        );
      }
    }
  }

  Future<String?> fetchTrainName(String trainNumber) async {
    try {
      return await TrainService.getTrainName(trainNumber);
    } catch (e) {
      _showWarning(
        context,
        "Failed to fetch train name",
        const Color.fromARGB(255, 147, 208, 227),
      );
      return null;
    }
  }

  Future<void> _fetchIssues() async {
    try {
      final data = await SubmitPassengerFeedback.fetchIssues();
      if (!_isWidgetActive || !mounted) return;
      setState(() {
        if (data.containsKey('ISSUE_TYPES')) {
          if (data['ISSUE_TYPES'] is List) {
            issuesData['ISSUE_TYPES'] = data['ISSUE_TYPES'];
          } else {
            issuesData['ISSUE_TYPES'] = <String>[];
          }
        }
        issuesData['SUB_ISSUE_TYPES'] = _buildSubIssueList(data);
      });
    } catch (e) {
      if (!_isWidgetActive || !mounted) return;
      setState(() {
        issuesData = {
          'ISSUE_TYPES': <String>[],
          'SUB_ISSUE_TYPES': <List<dynamic>>[],
        };
      });
    }
  }

  List<List<dynamic>> _buildSubIssueList(Map<String, dynamic> data) {
    List<List<dynamic>> result = [];
    if (data.containsKey('SUB_ISSUE_TYPES')) {
      final subIssueTypes = data['SUB_ISSUE_TYPES'];
      if (subIssueTypes is Map<String, dynamic>) {
        subIssueTypes.forEach((issueType, subIssues) {
          if (subIssues is List) {
            for (var subIssue in subIssues) {
              result.add([issueType, subIssue]);
            }
          }
        });
      } else if (subIssueTypes is List) {
        for (var item in subIssueTypes) {
          if (item is List && item.length >= 2) {
            result.add([item[0], item[1]]);
          }
        }
      }
    }
    return result;
  }

  void _handleDateSelection(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );
    if (pickedDate != null && pickedDate != selectedDate) {
      if (!_isWidgetActive) return;
      setState(() {
        selectedDate = pickedDate;
        formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate);
      });
    }
  }

  Future<void> _showWarning(
      BuildContext context, String message, Color color) async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isWidgetActive || !mounted) return;
      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          backgroundColor: color,
          content: Text(
            message,
            style: const TextStyle(color: Colors.black),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                "OK",
                style: TextStyle(color: Colors.black),
              ),
            ),
          ],
        ),
      );
    });
  }

  bool _validatepnrNumber(String pnr) {
    if (pnr.isEmpty) return false;
    return (pnr.length == 8 || pnr.length == 10) && int.tryParse(pnr) != null;
  }

  int _countCharacters(String text) {
    return text.length;
  }

  Future<void> _validatePNR() async {
    if (!_validatepnrNumber(pnrNoController.text)) {
      _showWarning(context, "Invalid PNR number.", Colors.red);
      return;
    }
    setState(() => loading = true);
    try {
      //on local use this
      // final token = Provider.of<UserModel>(context, listen: false).token;
      // final pnrData = await PnrService.checkPNR(pnrNoController.text,token);
      final pnrData = await PnrService.checkPNR(pnrNoController.text);
      if (!_isWidgetActive || !mounted) return;
      setState(() {
        trainNumberController.text = pnrData!.trainNumber;

        try {
          selectedDate = DateTime.parse(pnrData.dateOfJourney);
        } catch (e) {
          // Try alternative formats
          List<String> dateParts = pnrData.dateOfJourney.split('-');
          if (dateParts.length == 3) {
            selectedDate = DateTime(
              int.parse(dateParts[2]), // Year
              int.parse(dateParts[1]), // Month
              int.parse(dateParts[0]), // Day
            );
          }
        }

        formattedDate = DateFormat('yyyy-MM-dd').format(selectedDate);

        if (pnrData.passengerCoaches.isNotEmpty) {
          coachNoController.text = pnrData.passengerCoaches[0];
        }

        if (pnrData.passengerBerths.isNotEmpty) {
          berthNoController.text = pnrData.passengerBerths[0].toString();
        } else {
          berthNoController.text = '';
        }

        if (pnrData.trainName.isNotEmpty) {
          trainNameController.text = pnrData.trainName;
        }

        _isPNRVerified = true;
      });

      if ((trainNameController.text == null ||
              trainNameController.text.isNotEmpty) &&
          trainNumberController.text.isNotEmpty) {
        {
          try {
            final trainName = await fetchTrainName(trainNumberController.text);
            setState(() {
              trainNameController.text = trainName ?? '';
            });
          } catch (e) {
            _showWarning(
              context,
              "Failed to fetch train name",
              const Color.fromARGB(255, 144, 139, 139),
            );
          }
        }
      }

      _showWarning(context, "PNR details fetched successfully.", Colors.green);
    } catch (e) {
       print("Error validating PNR: $e");
       if (mounted) {
      _showWarning(
        context,
        "Failed to validate PNR Details\n\n Invalid PNR Number})",
         const Color.fromARGB(255, 241, 71, 71),
      );
    }
     
    } finally {
      if (!_isWidgetActive || !mounted) return;
      setState(() => loading = false);
    }
  }

  Future<Uint8List> _getFileData(XFile file) async {
    if (kIsWeb) {
      return await file.readAsBytes();
    } else {
      return await File(file.path).readAsBytes();
    }
  }

  Future<Map<String, dynamic>> _submitFeedback(String userToken) async {
    if (_formKey.currentState?.validate() == true) {
      String taskStatusSH = taskStatus == 'pending' ? 'P' : 'C';
      String verificationStatus = verified ? "yes" : "no";

      try {
        int? parsedBerthNo;
        if (berthNoController.text.isNotEmpty) {
          parsedBerthNo = int.tryParse(berthNoController.text);
          if (parsedBerthNo == null) {
            throw Exception('Berth number must be a valid number');
          }
        }

        List<Uint8List> pnrImageData = [];
        for (int i = 0; i < _selectedPNRFiles.length; i++) {
          final file = _selectedPNRFiles[i];
          final imageData = await _getFileData(file);
          pnrImageData.add(imageData);
        }

        List<Uint8List> feedbackImageData = [];
        for (int i = 0; i < _selectedFeedbackFiles.length; i++) {
          final file = _selectedFeedbackFiles[i];
          final imageData = await _getFileData(file);
          feedbackImageData.add(imageData);
        }

        List<Uint8List> feedbackVideoData = [];
        for (int i = 0; i < _selectedFeedbackVideos.length; i++) {
          final file = _selectedFeedbackVideos[i];
          final videoData = await _getFileData(file);
          feedbackVideoData.add(videoData);
        }

        final issueValue = selectedIssue ?? '';
        final subIssueValue = selectedSubIssue ?? '';

        String? remarksText =
            remarksController.text.isNotEmpty ? remarksController.text : null;

        final response = await SubmitPassengerFeedback.rmSubmitFeedback(
          token: userToken,
          date: formattedDate,
          passengerName: passengerNameController.text,
          mobileNo: mobileNoController.text,
          pnrNo: pnrNoController.text,
          status: taskStatusSH,
          coach: coachNoController.text,
          berthNo: parsedBerthNo,
          trainNumber: widget.trainNumber,
          remarks: remarksText,
          category: 'RM',
          crnNo: crnController.text,
          issue: issueValue,
          subIssue: subIssueValue,
          resolved: resolved,
          verificationStatus: verificationStatus,
          marks: _marksGiven.toString(),
          pnrImages: pnrImageData,
          imageFiles: feedbackImageData,
          videoFiles: feedbackVideoData,
        );

        if (!_isWidgetActive || !mounted) return response;
        setState(() {
          currentTimeInSeconds = DateTime.now().millisecondsSinceEpoch;
        });

        return response;
      } catch (e) {
        return {'success': false, 'message': e.toString()};
      }
    } else {
      return {'success': false, 'message': 'Form validation failed'};
    }
  }

  void _handleSubmit() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (_validateFields()) {
      try {
        loader(context, "submitting Feedback");
        final Map<String, dynamic> response =
            await _submitFeedback(userModel.token);
        if (mounted) Navigator.of(context, rootNavigator: true).pop();

        if (response.containsKey('success') && response['success'] == true) {
          _showWarning(
            context,
            response['message'] ?? "Feedback submitted successfully!",
            Colors.green,
          );
          _resetForm();
        } else {
          _showWarning(
            context,
            response['message'] ?? "Failed to submit feedback",
            Colors.red,
          );
        }
      } catch (e) {
        if (Navigator.canPop(context)) {
          Navigator.of(context, rootNavigator: true).pop();
        }
        print('Error in feedback submission: $e');
        _showWarning(
          context,
          "An unexpected error occurred. Please try again.",
          Colors.red,
        );
      }
    }
  }

  bool _isVideo(XFile file) {
    final extension = file.name.split('.').last.toLowerCase();
    return ['mp4', 'mov', 'avi', 'mkv'].contains(extension);
  }

  Widget _buildPNRImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: _pickTicketImage,
          icon: const Icon(Icons.upload_file, color: Colors.blue),
          label: const Text('Upload PNR Image'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            side: const BorderSide(color: Colors.black87, width: 0.5),
            minimumSize: const Size(double.infinity, 50),
          ),
        ),
        const SizedBox(height: 10.0),
        _selectedPNRFiles.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Selected Images:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 4,
                        mainAxisSpacing: 4,
                      ),
                      itemCount: _selectedPNRFiles.length,
                      itemBuilder: (context, index) {
                        final file = _selectedPNRFiles[index];
                        return Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Container(
                              width: 100,
                              height: 100,
                              margin: const EdgeInsets.only(right: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey),
                              ),
                              child: kIsWeb
                                  ? Image.network(file.path, fit: BoxFit.cover)
                                  : Image.file(File(file.path),
                                      fit: BoxFit.cover),
                            ),
                            IconButton(
                              icon: const Icon(Icons.cancel,
                                  color: Colors.red, size: 20),
                              onPressed: () {
                                setState(() {
                                  _selectedPNRFiles.removeAt(index);
                                });
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              )
            : const Center(
                child: Text(
                  'No PNR images selected',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
      ],
    );
  }

  Future<void> _pickTicketImage() async {
    if (_selectedPNRFiles.length >= 3) {
      _showWarning(
        context,
        'You can only select up to 3 PNR images',
        Colors.red,
      );
      return;
    }
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );

    if (source != null) {
      try {
        if (source == ImageSource.camera) {
          final XFile? pickedFile = await _imagePicker.pickImage(
            source: source,
            maxWidth: 800,
            maxHeight: 800,
            imageQuality: 90,
          );

          if (pickedFile != null) {
            setState(() {
              if (_selectedPNRFiles.length < 3) {
                _selectedPNRFiles.add(pickedFile);
              } else {
                _showWarning(
                  context,
                  'Maximum 3 PNR images allowed',
                  Colors.red,
                );
              }
            });
          }
        } else {
          final List<XFile>? pickedFiles = await _imagePicker.pickMultiImage(
            maxWidth: 800,
            maxHeight: 800,
            imageQuality: 90,
          );

          if (pickedFiles != null && pickedFiles.isNotEmpty) {
            setState(() {
              int remainingSlots = 3 - _selectedPNRFiles.length;

              if (remainingSlots <= 0) {
                _showWarning(
                  context,
                  'Maximum 3 PNR images allowed',
                  Colors.red,
                );
              } else if (pickedFiles.length <= remainingSlots) {
                _selectedPNRFiles.addAll(pickedFiles);
              } else {
                _selectedPNRFiles.addAll(pickedFiles.take(remainingSlots));
                _showWarning(
                  context,
                  'Only added ${remainingSlots} images. Maximum limit of 3 reached.',
                  Colors.red,
                );
              }
            });
          }
        }
      } catch (e) {
        _showWarning(
          context,
          'Error picking media: ${e.toString()}',
          Colors.red,
        );
      }
    }
  }

  // Pick feedback image/video
  Future<void> _pickFeedbackMedia() async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
      ),
    );

    if (source != null) {
      try {
        final bool? isVideo = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Select Media Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.camera),
                  title: const Text('Image'),
                  onTap: () => Navigator.pop(context, false),
                ),
                ListTile(
                  leading: const Icon(Icons.videocam),
                  title: const Text('Video'),
                  onTap: () => Navigator.pop(context, true),
                ),
              ],
            ),
          ),
        );

        if (isVideo == true) {
          // Check if already at video limit
          if (_selectedFeedbackVideos.length >= 3) {
            _showWarning(
              context,
              'You can only select up to 3 videos',
              Colors.red,
            );
            return;
          }

          final XFile? file = await _imagePicker.pickVideo(
            source: source,
            maxDuration: const Duration(minutes: 5),
          );

          if (file != null) {
            setState(() {
              if (_selectedFeedbackVideos.length < 3) {
                _selectedFeedbackVideos.add(file);
              } else {
                _showWarning(
                  context,
                  'Maximum 3 videos allowed',
                  Colors.red,
                );
              }
            });
          }
        } else {
          // Check if already at image limit
          if (_selectedFeedbackFiles.length >= 3) {
            _showWarning(
              context,
              'You can only select up to 3 feedback images',
              Colors.red,
            );
            return;
          }

          if (source == ImageSource.camera) {
            // For camera, pick a single image
            final XFile? pickedFile = await _imagePicker.pickImage(
              source: source,
              maxWidth: 800,
              maxHeight: 800,
              imageQuality: 90,
            );

            if (pickedFile != null) {
              setState(() {
                if (_selectedFeedbackFiles.length < 3) {
                  _selectedFeedbackFiles.add(pickedFile);
                } else {
                  _showWarning(
                    context,
                    'Maximum 3 feedback images allowed',
                    Colors.red,
                  );
                }
              });
            }
          } else {
            // For gallery, allow multiple selection with limit
            final List<XFile>? pickedFiles = await _imagePicker.pickMultiImage(
              maxWidth: 800,
              maxHeight: 800,
              imageQuality: 90,
            );

            if (pickedFiles != null && pickedFiles.isNotEmpty) {
              setState(() {
                // Calculate how many more images can be added
                int remainingSlots = 3 - _selectedFeedbackFiles.length;

                if (remainingSlots <= 0) {
                  _showWarning(
                    context,
                    'Maximum 3 feedback images allowed',
                    Colors.red,
                  );
                } else if (pickedFiles.length <= remainingSlots) {
                  // Add all selected images if within limit
                  _selectedFeedbackFiles.addAll(pickedFiles);
                } else {
                  // Add only up to the remaining slots
                  _selectedFeedbackFiles
                      .addAll(pickedFiles.take(remainingSlots));
                  _showWarning(
                    context,
                    'Only added ${remainingSlots} images. Maximum limit of 3 reached.',
                    Colors.red,
                  );
                }
              });
            }
          }
        }
      } catch (e) {
        _showWarning(
          context,
          'Error picking media: ${e.toString()}',
          Colors.red,
        );
      }
    }
  }

  Widget _buildFeedbackMediaSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ElevatedButton.icon(
          onPressed: _pickFeedbackMedia,
          icon: const Icon(Icons.photo_camera, color: Colors.blue),
          label: const Text('Pick Images/Videos for Feedback'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            side: const BorderSide(color: Colors.black87, width: 0.5),
            minimumSize: const Size(double.infinity, 50),
          ),
        ),
        const SizedBox(height: 10),
        _selectedFeedbackFiles.isNotEmpty
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Selected Images:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 200,
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 4,
                        mainAxisSpacing: 4,
                      ),
                      itemCount: _selectedFeedbackFiles.length,
                      itemBuilder: (context, index) {
                        final file = _selectedFeedbackFiles[index];
                        return Stack(
                          alignment: Alignment.topRight,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: Colors.grey[200],
                              ),
                              child: Center(
                                child: kIsWeb
                                    ? Image.network(file.path,
                                        fit: BoxFit.cover)
                                    : Image.file(File(file.path),
                                        fit: BoxFit.cover),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(Icons.cancel,
                                  color: Colors.red, size: 20),
                              onPressed: () {
                                setState(() {
                                  _selectedFeedbackFiles.removeAt(index);
                                });
                              },
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              )
            : const Center(
                child: Text(
                  'No feedback images selected',
                  style: TextStyle(
                    color: Colors.grey,
                    fontSize: 16,
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildFeedbackVideoSection() {
    if (_selectedFeedbackVideos.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Selected Videos:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 200,
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: _selectedFeedbackVideos.length,
              itemBuilder: (context, index) {
                final file = _selectedFeedbackVideos[index];
                return Stack(
                  alignment: Alignment.topRight,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.grey[300],
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.video_file,
                                size: 40, color: Colors.blue),
                            const SizedBox(height: 5),
                            Text(
                              file.name,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ),
                    IconButton(
                      icon:
                          const Icon(Icons.cancel, color: Colors.red, size: 20),
                      onPressed: () {
                        setState(() {
                          _selectedFeedbackVideos.removeAt(index);
                        });
                      },
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    } else {
      return const SizedBox
          .shrink(); // Return empty widget if no videos selected
    }
  }

  bool _validateFields() {
    if (passengerNameController.text.isEmpty ||
        pnrNoController.text.isEmpty ||
        trainNumberController.text.isEmpty ||
        mobileNoController.text.isEmpty ||
        coachNoController.text.isEmpty ||
        berthNoController.text.isEmpty ||
        crnController.text.isEmpty) {
      _showWarning(
        context,
        "Please fill all fields with valid information.",
        Colors.red,
      );
      return false;
    }
    if (!_validatepnrNumber(pnrNoController.text)) {
      _showWarning(
        context,
        "Pnr number should be 8 or 10 digits",
        Colors.red,
      );
      return false;
    }
    if (berthNoController.text.isNotEmpty) {
      final parsedBerthNo = int.tryParse(berthNoController.text);
      if (parsedBerthNo == null) {
        _showWarning(
          context,
          "Berth number must be a valid number",
          Colors.red,
        );
        return false;
      }
    }
    if (_countCharacters(remarksController.text) > 100) {
      _showWarning(
        context,
        "Feedback cannot exceed 100 characters",
        Colors.red,
      );
      return false;
    }
    return true;
  }

  void _resetForm() {
    if (!_isWidgetActive || !mounted) return;
    setState(() {
      passengerNameController.clear();
      pnrNoController.clear();
      mobileNoController.clear();
      emailIdController.clear();
      otpController.clear();
      coachNoController.clear();
      berthNoController.clear();
      trainNameController.clear();
      remarksController.clear();
      trainNumberController.clear();
      selectedDate = DateTime.now();
      formattedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
      isEmailVerified = false;
      _isPNRVerified = false;
      showOtpInput = false;
      taskStatus = 'pending';
      _selectedPNRFiles = [];
      _selectedFeedbackFiles = [];
      _selectedFeedbackVideos = [];
    });
  }

  // Email Verification Methods
  void _handleVerifyEmail() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (emailIdController.text.isEmpty) {
      _showWarning(context, "Please enter a valid Email ID.", Colors.white);
      return;
    }
    loader(context, "sending otp");
    try {
      await VerifyPassengerEmailService.verifyEmail(
        emailIdController.text,
        userModel.token,
      );
      setState(() => showOtpInput = true);
      Navigator.of(context, rootNavigator: true).pop();
      _showWarning(
        context,
        "Email verification initiated. Please check both your inbox and spam folders..",
        Colors.blue,
      );
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      _showWarning(
        context,
        "Error: ${e.toString()}",
        Colors.red,
      );
    }
  }

  void _handleVerifyOtp(String formattedDate) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    if (otpController.text.isEmpty) {
      _showWarning(context, "Please enter the OTP.", Colors.red);
      return;
    }
    loader(context, "verifying otp");
    try {
      await ConfirmPassengerEmailService.confirmEmail(
        token: userModel.token,
        date: formattedDate,
        email: emailIdController.text,
        otp: otpController.text,
      );
      if (mounted) {
        setState(() {
          showOtpInput = false;
          isEmailVerified = true;
        });
      }
      Navigator.of(context, rootNavigator: true).pop();
      _showWarning(
        context,
        "OTP verified successfully.",
        Colors.blue,
      );
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      if (mounted) {
        _showWarning(
          context,
          "Error: ${e.toString().isEmpty ? 'Something went wrong' : e.toString()}",
          Colors.red,
        );
      }
    }
  }

  // Consistent Button Style
  ButtonStyle _buttonStyle() {
    return ElevatedButton.styleFrom(
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      side: const BorderSide(color: Colors.black87, width: 0.5),
    );
  }

  // Email Verification Section
  Widget _buildEmailVerificationSection() {
    return Column(
      children: [
        Container(
          width: double.infinity, // This makes the container take full width
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(4),
          child: const Text(
            "Email Verification (Optional)",
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ),
        const SizedBox(height: 10.0),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: emailIdController,
                decoration: const InputDecoration(
                  labelText: "Email ID",
                  border: OutlineInputBorder(),
                ),
                readOnly: isEmailVerified || showOtpInput,
              ),
            ),
            if (!isEmailVerified && !showOtpInput)
              IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                ),
                onPressed: () {
                  _showSpamInfoDialog();
                },
              ),
          ],
        ),
        const SizedBox(height: 10),
        if (!isEmailVerified && !showOtpInput)
          ElevatedButton(
            onPressed: _handleVerifyEmail,
            style: _buttonStyle(),
            child: const Row(
              //mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.email),
                SizedBox(width: 8),
                Text("Verify Email"),
              ],
            ),
          ),
        if (showOtpInput) ...[
          const SizedBox(height: 10),
          // Notice about checking spam folder
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.mark_email_unread, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Flexible(
                  flex: 1,
                  child: Text(
                    "OTP sent to your email. Please check both inbox and spam folders.",
                    style: TextStyle(color: Colors.blue.shade700),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          TextFormField(
            controller: otpController,
            decoration: const InputDecoration(
              labelText: "Enter OTP",
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () => _handleVerifyOtp(formattedDate),
            style: _buttonStyle(),
            child: const Text("Verify OTP"),
          ),
        ],
        if (isEmailVerified)
          const Row(
            children: [
              Icon(Icons.verified, color: Colors.green),
              SizedBox(width: 5.0),
              Text(
                "verified",
                style: TextStyle(color: Colors.green),
              ),
            ],
          ),
      ],
    );
  }

  void _showSpamInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber),
              SizedBox(width: 5),
              Text(
                "Email Verification Info",
                style: TextStyle(fontSize: 18, color: Colors.red),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Please note that verification emails may sometimes be delivered to your spam/junk folder.",
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 12),
              Text(
                "After requesting OTP:",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 4),
              Text("• Check your inbox first"),
              Text("• If not found, check spam/junk folder"),
              Text("• Add our domain to your safe sender list"),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("I Understand"),
            ),
          ],
        );
      },
    );
  }

  Widget _buildIssueDropdowns() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        DropdownButtonFormField<String>(
          decoration: const InputDecoration(
            labelText: "Issue Type",
            border: OutlineInputBorder(),
          ),
          value: selectedIssue,
          items: issuesData.containsKey('ISSUE_TYPES') &&
                  issuesData['ISSUE_TYPES'] is List
              ? (issuesData['ISSUE_TYPES'] as List)
                  .map<DropdownMenuItem<String>>((dynamic issue) {
                  return DropdownMenuItem<String>(
                    value: issue.toString(),
                    child: Text(issue.toString()),
                  );
                }).toList()
              : <DropdownMenuItem<String>>[],
          onChanged: (value) {
            setState(() {
              selectedIssue = value;
              selectedSubIssue = null;
              issueController.text = value ?? '';
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return "Please select an issue type";
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        if (selectedIssue != null)
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: "Sub Issue Type",
              border: OutlineInputBorder(),
            ),
            value: selectedSubIssue,
            items: issuesData.containsKey('SUB_ISSUE_TYPES') &&
                    issuesData['SUB_ISSUE_TYPES'] is List
                ? (issuesData['SUB_ISSUE_TYPES'] as List)
                    .where((dynamic issue) =>
                        issue is List &&
                        issue.length > 0 &&
                        issue[0] == selectedIssue)
                    .map<DropdownMenuItem<String>>((dynamic issue) {
                    if (issue is List && issue.length > 1) {
                      return DropdownMenuItem<String>(
                        value: issue[1].toString(),
                        child: Text(issue[1].toString()),
                      );
                    } else {
                      return const DropdownMenuItem<String>(
                        value: '',
                        child: Text(''),
                      );
                    }
                  }).toList()
                : <DropdownMenuItem<String>>[],
            onChanged: (value) {
              setState(() {
                selectedSubIssue = value;
                subIssueController.text = value ?? '';
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "Please select a sub-issue type";
              }
              return null;
            },
          ),
      ],
    );
  }

  Widget _buildResolvedStatusDropdown() {
    return DropdownButtonFormField<String>(
      decoration: const InputDecoration(
        labelText: 'Resolved (Yes/No) *',
        border: OutlineInputBorder(),
      ),
      value: _resolvedStatus.isEmpty ? null : _resolvedStatus,
      hint: const Text('Select'),
      items: ['Yes', 'No']
          .map((status) => DropdownMenuItem(
                value: status,
                child: Text(status),
              ))
          .toList(),
      onChanged: (value) {
        setState(() {
          _resolvedStatus = value ?? '';
          resolved = value == 'Yes';
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please select resolved status';
        }
        return null;
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final userModel = Provider.of<UserModel>(context, listen: false);
    return Scaffold(
      appBar: const CustomAppBar(title: "Railmadad Passenger Feedback"),
      drawer: const CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                 const Padding(
                    padding: EdgeInsets.only(top: 5),
                    child: Text("Railmadad Feedback",
                        style: TextStyle(fontSize: 18,
                                         fontWeight: FontWeight.normal
                               ),
                        textAlign: TextAlign.center,),
                  ),
                    const SizedBox(height: 10.0),
                 
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: pnrNoController,
                        keyboardType: TextInputType.number,
                        maxLength: 10,
                        decoration: const InputDecoration(
                          labelText: "PNR Number *",
                          border: OutlineInputBorder(),
                          counterText: '',
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10)
                        ],
                        onChanged: (value) {
                          setState(() {});
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    SizedBox(
                      width: 120,
                      child: _isPNRVerified
                          ? Container(
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.check, color: Colors.white),
                                  SizedBox(width: 4),
                                  Text(
                                    'Verified',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                            )
                          : ElevatedButton(
                              onPressed: (loading ||
                                      !_validatepnrNumber(pnrNoController.text))
                                  ? null
                                  : _validatePNR,
                              style: ElevatedButton.styleFrom(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                side: const BorderSide(
                                    color: Colors.blue, width: 0.5),
                                minimumSize: const Size(double.infinity, 56),
                                disabledBackgroundColor: Colors.grey.shade300,
                              ),
                              child: loading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.black,
                                        strokeWidth: 1.0,
                                      ),
                                    )
                                  : const Text(
                                      "Validate",
                                      style: TextStyle(color: Colors.white),
                                    ),
                            ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                _buildPNRImageSection(),
                const SizedBox(height: 10),
                TextFormField(
                  controller: crnController,
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                  decoration: const InputDecoration(
                    labelText: "CRN Number*",
                    border: OutlineInputBorder(),
                    counterText: '',
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                ),
                const SizedBox(height: 20),
                TextFormField(
                  controller: trainNumberController,
                  decoration: const InputDecoration(
                    labelText: "Train No *",
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Train No is required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: trainNameController,
                  decoration: const InputDecoration(
                    labelText: "Train Name *",
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Train Name is required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 10),

                // Date of Journey
                GestureDetector(
                  onTap: () => _handleDateSelection(context),
                  child: AbsorbPointer(
                    child: TextFormField(
                      decoration: InputDecoration(
                        labelText: "Date of Journey *",
                        border: const OutlineInputBorder(),
                        suffix: const Icon(Icons.calendar_today),
                        hintText: formattedDate,
                      ),
                      controller: TextEditingController(text: formattedDate),
                    ),
                  ),
                ),

                const SizedBox(height: 15),
                TextFormField(
                  controller: passengerNameController,
                  decoration: const InputDecoration(
                    labelText: "Passenger Name *",
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Passenger Name is required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: coachNoController,
                  decoration: const InputDecoration(
                    labelText: "Coach No *",
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: berthNoController,
                  decoration: const InputDecoration(
                    labelText: "Berth No *",
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: mobileNoController,
                  decoration: const InputDecoration(
                    labelText: "Mobile Number *",
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Mobile Number is required";
                    }
                    if (value.length != 10) {
                      return "Mobile Number must be 10 digits";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15),
                _buildResolvedStatusDropdown(),
                const SizedBox(height: 20),
                DropdownButtonFormField<int>(
                  decoration: const InputDecoration(
                    labelText: 'Marks (1 to 10) *',
                    border: OutlineInputBorder(),
                  ),
                  value: _marksGiven == 0 ? null : _marksGiven,
                  hint: const Text('Select'),
                  items: List.generate(10, (index) => index + 1)
                      .map((mark) => DropdownMenuItem(
                            value: mark,
                            child: Text(mark.toString()),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _marksGiven = value ?? 0;
                    });
                  },
                  validator: (value) {
                    if (value == null || value == 0) {
                      return 'Please select marks';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 20),
                _buildIssueDropdowns(),
                const SizedBox(height: 20),
                _buildFeedbackMediaSection(),
                _buildFeedbackVideoSection(),
                const SizedBox(height: 15),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                      controller: remarksController,
                      maxLines: 2,
                      style: const TextStyle(fontSize: 14),
                      decoration: InputDecoration(
                        labelText: 'Remarks by Passenger',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                          borderSide: const BorderSide(color: Colors.grey),
                        ),
                        counterText: '',
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.deny(
                          RegExp(r'[0-9]'), // Deny numeric input
                        ),
                        LengthLimitingTextInputFormatter(100),
                      ],
                      onChanged: (value) {
                        setState(() {});
                      },
                    ),
                    const SizedBox(height: 5),
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        '${_countCharacters(remarksController.text)}/100 characters',
                        style: TextStyle(
                          fontSize: 10,
                          color: _countCharacters(remarksController.text) > 100
                              ? Colors.red
                              : Colors.grey[600],
                          fontWeight:
                              _countCharacters(remarksController.text) > 100
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                _buildEmailVerificationSection(),

                const SizedBox(height: 30),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: "Task Status *",
                    border: OutlineInputBorder(),
                  ),
                  value: taskStatus,
                  items: ['pending', 'completed']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) => setState(() => taskStatus = value!),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    if (!isUploading) {
                      // Form is valid, proceed with submission
                      _handleSubmit();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Submit Feedback'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
