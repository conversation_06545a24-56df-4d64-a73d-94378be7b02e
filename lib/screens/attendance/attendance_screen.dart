import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/attendance_details.dart';
import 'package:railops/screens/attendance/image_upload.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/attendance_services/location_service.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart';
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/utils/permission_handler_service.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_update/in_app_update.dart';

class AttendanceScreen extends StatefulWidget {
  @override
  _AttendanceScreenState createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  String? selectedTrain;
  List<String> stations = [];
  String startTime = "NA";
  String endTime = "NA";

  String trainScheduledAt = "NA";
  List<String> refreshedTimes = [];
  String expectedChartingTime = "NA";
  String loadedAtRecent = "NA";
  String loadedAtEarliest = "NA";
  String ehkName = "Na";
  String depot = "Na";
  bool isRunningDay = false;

  DateTime selectedDate = DateTime.now();
  List<String> _trainNumbers = [];
  Map<String, String> stationNames = {};
  String? token;
  Map<String, int> stationAttendanceCount = {};
  OnboardingResponse onboardingResponse = OnboardingResponse(
      message: '',
      stations: [],
      lastLocationFetched: null,
      lastLocationFetchedFromUser: null,
      date: null,
      trainNumber: null,
      coachNumbers: [],
      details: {},
      detailsOffBoarding: {},
      detailsOffBoardingInroute: {},
      detailsInroute: {});
  List<String> onboardingStations = [];
  List<String> coachNumbers = [];
  TrainChartingResponse? chartData;
  List<String> attendanceStations = [];
  final ScrollController _scrollController = ScrollController();
  bool _isRefreshing = false;
  String locationStatus = "Checking location...";
  bool _insideTrain = false;
  bool trainLoading = false;

  String insideTrainNumber = "";
  String insideTrainDate = "";
  List<String> insideTraincoachNumbers = [];
  EditTrainsData? trainData;
  List<int> frequency = [];

  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;

  List<String> fetchCoachNumbers() {
    if (insideTrainNumber == selectedTrain &&
        DateTime.parse(insideTrainDate) == selectedDate) {
      return insideTraincoachNumbers;
    } else {
      return coachNumbers;
    }
  }

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _requestPermissions();
    _fetchTrainNumbers();
    fetchInsideTrainDetails();
    checkLocationStatus();
    _checkAndShowChangeEmailModal(token!);
    _fetchInsideTrainStatus();
    if (selectedTrain != null || selectedTrain != "") {
      fetchChartingTime();
    }
    _setInitialTrainNoFromUserModel();
  }

  void _setInitialTrainNoFromUserModel() {
  final userModel = Provider.of<UserModel>(context, listen: false);
  final initialTrainNo = userModel.trainNo;

  if (initialTrainNo.isNotEmpty) {
    _onTrainNumberChanged(initialTrainNo);
  }
}

  void _requestPermissions() async {
    final permissionHandler = PermissionHandlerService();
    await permissionHandler.requestLocationPermission(context);
  }

void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        selectedTrain = trainNumber;
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _showForceUpdateDialog() async {
    if (!mounted) return;

    return SystemChannels.platform
        .invokeMethod<void>('SystemNavigator.preventPopInvokeMethod')
        .then((_) {
      if (!mounted) return;

      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WillPopScope(
          onWillPop: () async => false,
          child: AlertDialog(
            title: const Text('Update Required'),
            content: const Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.system_update, size: 50, color: Colors.blue),
                SizedBox(height: 16),
                Text(
                  'A new version of the app is available. You must update to continue using the app.',
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            actions: [
              ElevatedButton(
                onPressed: () async {
                  try {
                    await InAppUpdate.performImmediateUpdate();
                  } catch (e) {
                    //print("Update error: $e");
                    if (mounted) {
                      _showForceUpdateDialog();
                    }
                  }
                },
                child: const Text('Update Now'),
              ),
            ],
          ),
        ),
      );
    });
  }

  Future<void> _saveTrainLocation() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    setState(() {
      _isRefreshing = true;
    });
    try {
      Position position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);

      await LocationService.saveTrainLocation(
        userToken,
        position.latitude.toString(),
        position.longitude.toString(),
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Train Location Saved Successfully')),
      );
    } catch (e) {
      //print('Error refreshing data: $e');
      // ScaffoldMessenger.of(context).showSnackBar(
      //   SnackBar(content: Text('${e.toString()}')),
      // );
    } finally {
      fetchInsideTrainDetails();
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  Future<void> checkLocationStatus() async {
    try {
      bool isLocationEnabled = await Geolocator.isLocationServiceEnabled();

      if (isLocationEnabled) {
        setState(() {
          locationStatus = "Location is ON";
        });
      } else {
        setState(() {
          locationStatus = "Location is OFF";
        });
      }
    } catch (e) {
      setState(() {
        locationStatus = "Error checking location: $e";
      });
    }
  }

  void scrollToCurrentStation(String currentStation) {
    if (stations.isEmpty) return;

    final stationIndex = stations.indexOf(currentStation);
    if (stationIndex != -1) {
      final scrollPosition =
          stationIndex * 100.0; // Approximate height of each station item

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController.animateTo(
          scrollPosition,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      });
    }
  }

  Future<void> _fetchAttendanceStations(String trainNo) async {
    try {
      final stationAttendanceList =
          await EditTrainServices.fetchStoppagesForAttendanceByTrainNo(trainNo,token!);
      if (stationAttendanceList != null) {
        setState(() {
          attendanceStations =
              stationAttendanceList.attendanceStations ?? ['N/A'];
        });
        //print(stationAttendanceList);
      }
    } catch (e) {
      //print('Error fetching train details: $e');
    }
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        await _showForceUpdateDialog();
        return;
      }

      try {
        updateInfo = await InAppUpdate.checkForUpdate();
        if (updateInfo.updateAvailability ==
            UpdateAvailability.updateAvailable) {
          setState(() {
            _updateInfo = updateInfo;
            _flexibleUpdateAvailable = true;
          });
        }
      } catch (e) {
        //print("Flexible update failed: $e");
      }
    } catch (e) {
      //print("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        //print("Error starting flexible update: $e");
      }
    }
  }

  Future<void> _checkAndShowChangeEmailModal(String token) async {
    //print("Checking and showing modal");
    final prefs = await SharedPreferences.getInstance();
    final lastShownTime = prefs.getInt('lastChangeEmailModalTime');
    final currentTime = DateTime.now().millisecondsSinceEpoch;

    if (lastShownTime == null ||
        currentTime - lastShownTime >= 2 * 60 * 60 * 1000) {
      // 2 hours have passed or modal has never been shown
      final profileResponse = await ProfileService.getProfile(token);
      final email = await profileResponse.user?.email ?? '';
      //print(email);
      _showChangeEmailModalOnce(email);

      prefs.setInt('lastChangeEmailModalTime', currentTime);
    }
  }

  Future<void> _showChangeEmailModalOnce(String email) async {
    //print('Email: $email');
    if (email.startsWith("noemail")) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (BuildContext context) => ChangeEmailModal(),
        );
      });
    }
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        trainLoading = true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        trainLoading = false;
      });
    } catch (e) {
      //print('Error fetching train numbers: $e');
      setState(() {
        trainLoading = false;
      });
    }
  }

  Future<void> _fetchOnboardingDetails(String trainNumber, String date) async {
    //print(trainNumber);
    try {
      onboardingResponse =
          await OnboardingService.fetchOnboardingDetails(trainNumber, date);
      //print(onboardingResponse);
      setState(() {
        onboardingResponse = onboardingResponse;
        onboardingStations = onboardingResponse.stations ?? [];
        coachNumbers = onboardingResponse.coachNumbers ?? [];
        // Scroll to nearby station if available
        if (onboardingStations.isNotEmpty) {
          int middleIndex = (onboardingStations.length / 2).floor();
          String nearestStation = onboardingStations[middleIndex];
          scrollToCurrentStation(nearestStation);
        }
      });
    } catch (e) {
      setState(() {
        onboardingResponse = OnboardingResponse(
            message: '',
            stations: [],
            lastLocationFetched: null,
            date: null,
            trainNumber: null,
            coachNumbers: [],
            details: {},
            detailsOffBoarding: {},
            detailsOffBoardingInroute: {},
            detailsInroute: {});
        onboardingStations = [];
      });
      //print('Error fetching train numbers: $e');
    }
  }

  Map<String, List<int>>? getStationOnboardingDetails(String stationCode) {
    return onboardingResponse.details?[stationCode];
  }

  Map<String, List<int>>? getStationOffboardingDetails(String stationCode) {
    return onboardingResponse.detailsOffBoarding?[stationCode];
  }

  Map<String, List<int>>? getStationVacantDetails(String stationCode) {
    return onboardingResponse.detailsVacant?[stationCode];
  }

  Map<String, List<int>>? getStationonboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsInroute?[stationCode];
  }

  Map<String, List<int>>? getStationoffboardingDetailsInroute(
      String stationCode) {
    return onboardingResponse.detailsOffBoardingInroute?[stationCode];
  }

  Future<void> _fetchTrainDetails(String trainNo) async {
    try {
      final trainDetails =
          await EditTrainServices.fetchTrainDetailsByTrainNo(trainNo);
      if (trainDetails != null) {
        setState(() {
          startTime = trainDetails.startTime ?? 'NA';
          endTime = trainDetails.endTime ?? 'NA';
          trainData = trainDetails;
          frequency = trainDetails.frequency ?? [];
        });
      }
    } catch (e) {
      //print('Error fetching train details: $e');
    }
  }

  String formatTimeinHHMM(String time) {
    List<String> parts = time.split(":");
    if (parts.length >= 2) {
      return "${parts[0]}:${parts[1]}";
    }
    return time;
  }

  Future<void> updateStations(String trainNumber) async {
    final result = await TrainService.getTrainStations(trainNumber!);
    List<String> _stations = result['stationList'];
    final Map<String, String> _stationNames = result['stationsDict'];
    setState(() {
      stationNames = _stationNames;
      stations = _stations;
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );
    if (pickedDate != null && pickedDate != selectedDate) {
      setState(() {
        selectedDate = pickedDate;
      });

      if (selectedTrain != null) {
        fetchAttendance();
        fetchChartingTime();
        _fetchOnboardingDetails(
            selectedTrain!, DateFormat('yyyy-MM-dd').format(selectedDate));
      }
    }
  }

  void fetchAttendance() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    try {
      final attendanceCount =
          await AttendanceService.fetchStationAttendanceCount(
              trainNumber: selectedTrain!,
              date: DateFormat('yyyy-MM-dd').format(selectedDate),
              token: userToken);

      setState(() {
        stationAttendanceCount = attendanceCount;
      });

      //print('Attendance Count: $attendanceCount');
    } catch (e) {
      //print('Error: $e');
    }
  }

  void fetchChartingTime() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    //print("Selected Date : $selectedDate");
    try {
      final chartResponse = await TrainService.fetchChartingTime(
          trainNumber: selectedTrain,
          date: DateFormat('yyyy-MM-dd').format(selectedDate),
          token: userToken);
      //print("Charting Response : $chartResponse");
      setState(() {
        chartData = chartResponse;
        depot = chartResponse!.depot;
        ehkName = chartResponse.ehkDict;
        trainScheduledAt = chartResponse.startTime;
        refreshedTimes = chartResponse.refreshedTimes;
        expectedChartingTime = chartResponse.chartingTime;
        loadedAtEarliest = chartResponse.loadedAtEarliest;
        loadedAtRecent = chartResponse.loadedAtRecent;
        isRunningDay = chartResponse.isRunningDay;
      });
      _fetchTrainDetails(selectedTrain!);
      _showTrainStatusSnackbar(isRunningDay);
      //print(chartResponse!.chartingTime);
    } catch (e) {
      //print('Error: $e');
    }
  }

  void fetchInsideTrainDetails() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final userToken = userModel.token == null
        ? prefs.getString('authToken')
        : userModel.token;
    try {
      final trainDetails =
          await AttendanceService.fetchInsideTrainDetails(userToken);
      setState(() {
        selectedTrain = trainDetails["insideTrainNumber"];
        selectedDate = DateTime.parse(trainDetails["date"]);
        insideTrainNumber = trainDetails["insideTrainNumber"];
        selectedTrain = trainDetails["insideTrainNumber"];
        insideTrainDate = trainDetails["date"];
        insideTraincoachNumbers = trainDetails["coachNumbers"];
      });
      if (trainDetails["insideTrainNumber"] != null &&
          trainDetails["date"] != null) {
        fetchAttendance();
        updateStations(trainDetails["insideTrainNumber"]);
        _fetchTrainDetails(trainDetails["insideTrainNumber"]);
        _fetchOnboardingDetails(
            trainDetails["insideTrainNumber"], trainDetails["date"]);
        _fetchAttendanceStations(trainDetails["insideTrainNumber"]);
        fetchChartingTime();
      }
    } catch (e) {
      //print('Error: $e');
    }
  }

  Future<void> _fetchInsideTrainStatus() async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      SharedPreferences prefs = await SharedPreferences.getInstance();
      final userToken = userModel.token == null
          ? prefs.getString('authToken')
          : userModel.token;
      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(userToken!);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
      });
    } catch (e) {
      //print('Error fetching toggle switch statuses: $e');
    }
  }

  Future<void> _handleRefresh() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    setState(() => _isRefreshing = true);

    try {
      if (selectedTrain != null) {
        // Re-fetch all relevant data
        fetchAttendance();
        await _fetchOnboardingDetails(
          selectedTrain!,
          DateFormat('yyyy-MM-dd').format(selectedDate),
        );
        await _fetchTrainDetails(selectedTrain!);
        await _fetchAttendanceStations(selectedTrain!);
        fetchChartingTime();
        await checkLocationStatus();
        await _saveTrainLocation();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Data refreshed successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Refresh failed: ${e.toString()}')),
      );
    } finally {
      setState(() => _isRefreshing = false);
    }
  }

  bool _showDetails = false;

  String? getArrivalTimeForStation(
      String stationName, EditTrainsData trainData) {
    final arrival = trainData.arrivalTime?.firstWhere(
        (map) => map["station_code"] == stationName,
        orElse: () => {});

    return arrival != null && arrival.isNotEmpty
        ? arrival["arrival_time"]
        : null;
  }

  Widget _buildLastFetchedTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      final istTime =
          lastFetched.toUtc().add(const Duration(hours: 5, minutes: 30));
      formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
    }

    (onboardingResponse.lastLocationFetchedFromUser == null)
        ? locationFetchedFromUser = "NA"
        : locationFetchedFromUser =
            (onboardingResponse.lastLocationFetchedFromUser);

    return Positioned(
      left: 5,
      bottom: 5,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'Last fetched: $formattedTime\nLast Fetched From User: $locationFetchedFromUser',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  String formatTime(DateTime ogTime) {
    final istTime = ogTime.toUtc().add(const Duration(hours: 5, minutes: 30));
    String formattedTime = DateFormat('dd-MM-yyyy HH:mm').format(istTime);
    return formattedTime;
  }

  Widget _buildChartingTime() {
    String formattedTime = "";
    String? locationFetchedFromUser;
    if (onboardingResponse.lastLocationFetched == null) {
      formattedTime = "location not fetched";
    } else {
      final lastFetched = onboardingResponse.lastLocationFetched!;
      formattedTime = formatTime(lastFetched);
    }

    return Positioned(
      left: 5,
      top: 110,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _showDetails = !_showDetails;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            borderRadius: BorderRadius.circular(2),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _showDetails
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(width: 4),
                    const SizedBox(width: 4),
                    Text(
                      'Expected Charting Time: $trainScheduledAt',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting started at: ${loadedAtEarliest != "NA" ? formatTime(DateTime.parse(loadedAtEarliest)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Charting refreshed at: ${loadedAtRecent != "NA" ? formatTime(DateTime.parse(loadedAtRecent)) : "NA"}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    SizedBox(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxHeight: 100,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            children: refreshedTimes.map((time) {
                              return Row(
                                children: [
                                  const SizedBox(width: 4),
                                  Text(
                                    'Charting Time: $time',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                    Text(
                      'Train Depot:$depot',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'EHK Assigned for train:$ehkName',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'User Location:$locationStatus',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                )
              : const Icon(
                  Icons.access_time,
                  size: 14,
                  color: Colors.white,
                ),
        ),
      ),
    );
  }

  String _formatRunningDays() {
    if (frequency.isEmpty) return 'N/A';

    final dayAbbreviations = frequency.map((dayIndex) {
      final dayName =
          DateFormat('EEEE').format(DateTime(2023, 1, 1 + dayIndex));
      return dayName.substring(0, 3);
    }).toList();

    if (dayAbbreviations.length == 7) return 'Daily';

    return dayAbbreviations.join(', ');
  }

  void _showTrainStatusSnackbar(bool isRunning) {
    String dayOfWeek = DateFormat('EEEE').format(selectedDate);
    if (!isRunning) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "Train $selectedTrain is NOT running on specific date (${dayOfWeek.substring(0, 3)})\nRunning Days: ${_formatRunningDays()}",
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate(); // Start update when available
      });
    }
    return Scaffold(
      appBar: const CustomAppBar(title: "Attendance"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: Stack(
          children: [
            Column(
              children: [
                // Filters Section (Full Width)
                // Filters Section (Updated)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                      vertical: 8.0, horizontal: 12.0),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(239, 239, 239, 1),
                  ),
                  child: Row(
                    children: [
                      // Train dropdown
                      Expanded(
                        flex: 1,
                        child: DropdownSearch<String>(
                          popupProps: PopupProps.menu(
                            showSearchBox: true,
                            searchFieldProps: TextFieldProps(
                              keyboardType: TextInputType.number,
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(5),
                              ],
                            )
                          ),
                          items: _trainNumbers,
                          dropdownDecoratorProps: const DropDownDecoratorProps(
                            dropdownSearchDecoration: InputDecoration(
                              labelText: "Train Number",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                          ),
                          dropdownButtonProps: DropdownButtonProps(
                            icon: trainLoading
                                ? const Padding(
                                    padding: EdgeInsets.all(8.0),
                                    child: SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 2),
                                    ),
                                  )
                                : const Icon(Icons.arrow_drop_down),
                          ),
                          onChanged: (value) async {
                            selectedTrain = value;
                            updateStations(value!);
                            _fetchTrainDetails(value);
                            if (value != null && selectedDate != null) {
                              _fetchAttendanceStations(value);
                              fetchAttendance();
                              fetchChartingTime();
                              _fetchOnboardingDetails(
                                  selectedTrain!,
                                  DateFormat('yyyy-MM-dd')
                                      .format(selectedDate));
                            await Provider.of<UserModel>(context, listen: false).setTrainNo(value);
                            }
                          },
                          selectedItem: selectedTrain,
                        ),
                      ),
                      if (_insideTrain)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                          child: Image.asset(
                            'assets/images/user_inside_train.png',
                            width: 50,
                            height: 50,
                          ),
                        ),
                      const SizedBox(width: 8),
                      // Date field
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context),
                          child: InputDecorator(
                            decoration: const InputDecoration(
                              labelText: "Date",
                              labelStyle: TextStyle(fontSize: 14),
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 12),
                            ),
                            child: Text(
                              DateFormat('yyyy-MM-dd').format(selectedDate),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Container(
                  color: Colors.blueGrey.shade200,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: const Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Left Section: Timings and Stoppages
                      Padding(
                        padding: EdgeInsets.only(left: 20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Timings",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                            SizedBox(
                              height: 5,
                              width: 25,
                            ), // Add some spacing
                            Text(
                              "Stoppages",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),

                      // Right Section: Attendance marked Count
                      Padding(
                        padding: EdgeInsets.only(right: 20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              "Passenger Chart   Atten..",
                              style:
                                  TextStyle(color: Colors.black, fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                Expanded(
                  child: Container(
                    color: const Color.fromRGBO(239, 239, 239, 1),
                    child: ListView.builder(
                      controller: _scrollController,
                      itemCount: stations.length,
                      itemBuilder: (context, index) {
                        final station = stations[index];
                        final arrivalTime = trainData != null
                            ? getArrivalTimeForStation(station, trainData!)
                            : null;
                        bool isAttendanceAllowed = false;
                        if ((stations.length - 1) == index || index == 0) {
                          isAttendanceAllowed = true;
                        } else {
                          isAttendanceAllowed =
                              attendanceStations.contains(station);
                        }

                        final hasOnboardingStations =
                            onboardingStations.isNotEmpty;
                        DateTime? trainStartDateTime;
                        try {
                          final selectedDateStr =
                              DateFormat('yyyy-MM-dd').format(selectedDate);
                          trainStartDateTime = DateFormat('yyyy-MM-dd HH:mm')
                              .parse('$selectedDateStr $startTime');
                        } catch (e) {
                          //print('Error parsing start time: $e');
                        }

                        final hasTrainStarted = trainStartDateTime != null &&
                            DateTime.now().isAfter(trainStartDateTime);

                        final isOnboardingStation = hasOnboardingStations
                            ? onboardingStations.contains(station)
                            : (hasTrainStarted && index == 0);

                        final firstOnboardingIndex = stations
                            .indexWhere((s) => onboardingStations.contains(s));

                        final isBeforeOnboarding = firstOnboardingIndex != -1 &&
                            index < firstOnboardingIndex;
                        final isAfterOnboarding = firstOnboardingIndex != -1 &&
                            index > firstOnboardingIndex;

                        return StationItem(
                          isAttendanceStation: isAttendanceAllowed,
                          arrivalTime: arrivalTime,
                          stationCode: station,
                          stationName: stationNames[station] ?? "station name",
                          attendanceCount: stationAttendanceCount[station] ?? 0,
                          showStartTime: index == 0,
                          showEndTime: index == stations.length - 1,
                          startTime: formatTimeinHHMM(startTime),
                          endTime: formatTimeinHHMM(endTime),
                          selectedTrain: selectedTrain!,
                          selectedDate: DateFormat('yyyy-MM-dd')
                              .format(selectedDate)
                              .toString(),
                          isOnboardingStation: isOnboardingStation,
                          onboardingDetails:
                              getStationOnboardingDetails(station),
                          offboardingDetails:
                              getStationOffboardingDetails(station),
                          vacantDetails: getStationVacantDetails(station),
                          isBeforeOnboarding: isBeforeOnboarding,
                          isAfterOnboarding: isAfterOnboarding,
                          coachNumbers: insideTrainNumber == selectedTrain &&
                                  DateTime.parse(insideTrainDate) ==
                                      selectedDate
                              ? insideTraincoachNumbers
                              : coachNumbers,
                          onboardingDetailsInroute:
                              getStationonboardingDetailsInroute(station),
                          offboardingDetailsInroute:
                              getStationoffboardingDetailsInroute(station),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
            _buildLastFetchedTime(),
            _buildChartingTime(),
          ],
        ),
      ),
      floatingActionButton: Container(
        padding: const EdgeInsets.only(bottom: 2),
        child: FloatingActionButton.small(
          onPressed: _isRefreshing ? null : _saveTrainLocation,
          backgroundColor:
              _isRefreshing ? Colors.grey : Colors.blueGrey.shade200,
          child: _isRefreshing
              ? const SizedBox(
                  width: 14,
                  height: 14,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 1.5,
                  ),
                )
              : const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.location_on,
                        color: Colors.black, size: 20), // Smaller icon
                    Text(
                      "Update", // Shortened text
                      style: TextStyle(
                          fontSize: 10,
                          color: Colors.black), // Adjust font size
                    ),
                  ],
                ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.endDocked,
    );
  }
}

Widget _buildTimeText(String? time) {
  return Text(
    time ?? "-",
    style: const TextStyle(
      fontSize: 12,
      fontWeight: FontWeight.bold,
    ),
    textAlign: TextAlign.right,
  );
}

// Custom widget for each station
class StationItem extends StatelessWidget {
  final String stationCode;
  final String stationName;
  final int attendanceCount;
  final bool showStartTime;
  final bool showEndTime;
  final String startTime;
  final String endTime;
  final String selectedDate;
  final String selectedTrain;
  final bool isOnboardingStation;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final bool isAttendanceStation;
  final bool isBeforeOnboarding;
  final bool isAfterOnboarding;
  final List<String> coachNumbers;
  final String? arrivalTime;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;

  const StationItem(
      {Key? key,
      required this.stationCode,
      required this.stationName,
      required this.attendanceCount,
      this.showStartTime = false,
      this.showEndTime = false,
      required this.startTime,
      required this.endTime,
      required this.selectedDate,
      required this.selectedTrain,
      required this.isOnboardingStation,
      required this.offboardingDetails,
      required this.onboardingDetails,
      required this.vacantDetails,
      required this.isAttendanceStation,
      required this.isBeforeOnboarding,
      required this.isAfterOnboarding,
      required this.coachNumbers,
      this.arrivalTime,
      required this.offboardingDetailsInroute,
      required this.onboardingDetailsInroute})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    Map<String, Map<String, int>> coachTotals = {};
    Map<String, Map<String, int>> filteredCoachTotals = {};
    // if (isOnboardingStation) {
    Set<String> allCoaches = {
      ...?onboardingDetails?.keys,
      ...?offboardingDetails?.keys,
      ...?vacantDetails?.keys,
    };
    for (String coach in allCoaches) {
      coachTotals[coach] = {
        'onboard': onboardingDetails?[coach]?.length ?? 0,
        'offboard': offboardingDetails?[coach]?.length ?? 0,
        'vacant': vacantDetails?[coach]?.length ?? 0,
      };

      if (coachNumbers.contains(coach)) {
        filteredCoachTotals[coach] = coachTotals[coach]!;
      }
    }
    // }

    var filteredEntries = coachTotals.entries
        .where((entry) =>
            entry.value['onboard']! > 0 ||
            entry.value['offboard']! > 0 ||
            entry.value['vacant']! > 0)
        .toList();

    final totalOnboard = filteredEntries
        .map((e) => e.value['onboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalOffboard = filteredEntries
        .map((e) => e.value['offboard']!)
        .fold<int>(0, (sum, count) => sum + count);
    final totalVacant = filteredEntries
        .map((e) => e.value['vacant']!)
        .fold<int>(0, (sum, count) => sum + count);

    filteredEntries = coachTotals.entries
        .where((entry) => coachNumbers.contains(entry.key))
        .toList();

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(width: 10),
        SizedBox(
          width: 80,
          child: Column(
            children: [
              if (showStartTime) _buildTimeText(startTime),
              if (!showStartTime)
                _buildTimeText(
                    arrivalTime?.isNotEmpty == true ? arrivalTime : " "),
              if (showEndTime) _buildTimeText(endTime),
              if (!showEndTime &&
                  !showStartTime &&
                  (arrivalTime == null || arrivalTime!.isEmpty))
                _buildTimeText("-"),
            ],
          ),
        ),
        const SizedBox(width: 10),
        Column(
          children: [
            Stack(
              alignment: showStartTime
                  ? Alignment.topCenter
                  : showEndTime
                      ? Alignment.bottomCenter
                      : Alignment.center,
              children: [
                Container(
                  width: 14,
                  height: showStartTime || showEndTime ? 90 : 100,
                  margin: EdgeInsets.only(
                    top: showStartTime ? 8 : 0,
                    bottom: showEndTime ? 8 : 0,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 198, 223, 239),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(showStartTime ? 8 : 0),
                      topRight: Radius.circular(showStartTime ? 8 : 0),
                      bottomLeft: Radius.circular(showEndTime ? 8 : 0),
                      bottomRight: Radius.circular(showEndTime ? 8 : 0),
                    ),
                  ),
                ),
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.green,
                          width: 2,
                        ),
                      ),
                    ),
                    if (isOnboardingStation)
                      Container(
                        width: 16,
                        height: 16,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                      ),
                    if (!isOnboardingStation && isBeforeOnboarding)
                      const Icon(
                        Icons.check,
                        size: 12,
                        color: Colors.green,
                      ),
                  ],
                ),
              ],
            ),
          ],
        ),
        const SizedBox(width: 30),
        Expanded(
          child: InkWell(
            onTapDown: (details) async {
              if (isAttendanceStation) {
                final result = await showMenu<String>(
                  context: context,
                  position: RelativeRect.fromLTRB(
                    details.globalPosition.dx,
                    details.globalPosition.dy,
                    details.globalPosition.dx,
                    details.globalPosition.dy,
                  ),
                  items: [
                    const PopupMenuItem(
                      value: 'self',
                      child: Text('Self'),
                    ),
                    const PopupMenuItem(
                      value: 'coach attendent',
                      child: Text('Other CA'),
                    ),
                    const PopupMenuItem(
                      value: 'OBHS',
                      child: Text('Other EHK/OBHS'),
                    ),
                  ],
                );

                if (result != null) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ImageUploadPage(
                          selectedOption: result == "self" ? "self" : "other",
                          forUserType: result,
                          trainNumber: selectedTrain,
                          journeyDate: selectedDate,
                          stationCode: stationCode,
                          stationName: stationName),
                    ),
                  );
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'Attendance is not allowed for $stationCode station'),
                    backgroundColor: const Color.fromARGB(255, 237, 162, 76),
                    duration: const Duration(seconds: 2),
                  ),
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    stationCode,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isOnboardingStation
                          ? Colors.green.shade700
                          : Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    stationName.length > 10
                        ? stationName.substring(0, 11)
                        : stationName,
                    style: TextStyle(
                      fontSize: 12,
                      color: isOnboardingStation
                          ? Colors.green.shade600
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // if (isOnboardingStation && coachTotals.isNotEmpty)
        GestureDetector(
          onTap: () {
            if (totalOnboard == 0 && totalOffboard == 0 && totalVacant == 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Chart has not been prepared for this station'),
                  backgroundColor: Colors.orange,
                ),
              );
            } else {
              showDialog(
                context: context,
                builder: (BuildContext context) => StationDetailsPopup(
                  stationCode: stationCode,
                  onboardingDetails: onboardingDetails,
                  offboardingDetails: offboardingDetails,
                  vacantDetails: vacantDetails,
                  coachTotals: coachTotals,
                  filteredCoachTotals: filteredCoachTotals,
                  coachNumbers: coachNumbers,
                  onboardingDetailsInroute: onboardingDetailsInroute,
                  offboardingDetailsInroute: offboardingDetailsInroute,
                ),
              );
            }
          },
          child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                // Optional: Add some hover effect or indication that it's tappable
                border: Border.all(color: Colors.transparent),
              ),
              child: Column(children: [
                Table(
                  defaultColumnWidth: const IntrinsicColumnWidth(),
                  border: TableBorder.all(
                    color: Colors.white,
                    width: 2,
                  ),
                  children: [
                    TableRow(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                      ),
                      children: [
                        _buildTableCell('Total',
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.blue.shade200
                                : Colors.blue.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOnboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.green.shade200
                                : Colors.green.shade50,
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalOffboard.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? const Color.fromARGB(255, 237, 162, 76)
                                : const Color.fromARGB(255, 252, 231, 174),
                            isOnboardingStation: isOnboardingStation),
                        _buildTableCell(totalVacant.toString(),
                            isBold: isOnboardingStation ? true : false,
                            backgroundColor: isOnboardingStation
                                ? Colors.grey.shade300
                                : Colors.grey.shade100,
                            isOnboardingStation: isOnboardingStation),
                      ],
                    ),
                    ...filteredEntries.take(3).map((entry) {
                      return TableRow(
                        children: [
                          _buildTableCell(entry.key,
                              backgroundColor: isOnboardingStation
                                  ? Colors.blue.shade200
                                  : Colors.blue.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['onboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.green.shade200
                                  : Colors.green.shade50,
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['offboard'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? const Color.fromARGB(255, 237, 162, 76)
                                  : const Color.fromARGB(255, 252, 231, 174),
                              isOnboardingStation: isOnboardingStation),
                          _buildTableCell(entry.value['vacant'].toString(),
                              backgroundColor: isOnboardingStation
                                  ? Colors.grey.shade200
                                  : Colors.grey.shade100,
                              isOnboardingStation: isOnboardingStation),
                        ],
                      );
                    }),
                  ],
                ),
                if (filteredEntries.length > 3)
                  Container(
                    padding: const EdgeInsets.only(top: 2),
                    child: Text(
                      'click for more...',
                      style: TextStyle(
                        fontSize: 8,
                        height: 0.5,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
              ])),
        ),
        if (isAttendanceStation)
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AttendanceDetailsPage(
                      trainNumber: selectedTrain,
                      date: selectedDate,
                      stationCode: stationCode),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                "A: $attendanceCount",
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
            ),
          ),
        const SizedBox(width: 20),
      ],
    );
  }

  Widget _buildTableCell(String text,
      {bool isBold = false,
      Color? backgroundColor,
      bool isOnboardingStation = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      color: backgroundColor,
      child: Text(
        text,
        style: TextStyle(
            fontSize: 10,
            fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
            color: isOnboardingStation ? Colors.black : Colors.grey.shade900),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class StationDetailsPopup extends StatefulWidget {
  final String stationCode;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final Map<String, Map<String, int>>? coachTotals;
  final Map<String, Map<String, int>>? filteredCoachTotals;
  final List<String> coachNumbers;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;

  const StationDetailsPopup(
      {Key? key,
      required this.stationCode,
      this.onboardingDetails,
      this.offboardingDetails,
      this.vacantDetails,
      this.coachTotals,
      this.filteredCoachTotals,
      required this.coachNumbers,
      this.offboardingDetailsInroute,
      this.onboardingDetailsInroute})
      : super(key: key);

  @override
  State<StationDetailsPopup> createState() => _StationDetailsPopupState();
}

class _StationDetailsPopupState extends State<StationDetailsPopup> {
  bool showSleeperCoaches = false;

  bool isSleeperCoach(String coach) {
    return coach.toLowerCase().startsWith('s');
  }

  Map<String, List<int>> filterSleeperCoaches(
      Map<String, List<int>> details, bool wantSleeper) {
    return Map.fromEntries(
      details.entries
          .where((entry) => isSleeperCoach(entry.key) == wantSleeper),
    );
  }

  Map<String, List<int>> _filterDetailsByCoaches(Map<String, List<int>> details,
      List<String> coachNumbers, bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  Map<String, List<int>> _excludeDetailsByCoaches(
      Map<String, List<int>> details,
      List<String> coachNumbers,
      bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          !coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  // Check if berth is in inroute onboarding or offboarding
  bool isInrouteBerth(String coach, int berth) {
    final onboardingInroute = widget.onboardingDetailsInroute ?? {};
    final offboardingInroute = widget.offboardingDetailsInroute ?? {};

    return (onboardingInroute.containsKey(coach) &&
            onboardingInroute[coach]!.contains(berth)) ||
        (offboardingInroute.containsKey(coach) &&
            offboardingInroute[coach]!.contains(berth));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: 0.0, horizontal: 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 40), // Placeholder for balance
                      Expanded(
                        child: Text(
                          'Station ${widget.stationCode} Details',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(), // Optional: Adds a subtle separation

                TextButton(
                  onPressed: () {
                    setState(() {
                      showSleeperCoaches = !showSleeperCoaches;
                    });
                  },
                  child: Text(
                    showSleeperCoaches
                        ? 'Back to AC Coaches'
                        : 'Show Sleeper Coaches',
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0.0),
                  child: Text(
                    showSleeperCoaches ? 'Sleeper Coaches' : 'AC coaches',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Onboarding Details (Your Coaches)',
                      Colors.green,
                      _filterDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Onboarding Details (All Coaches)',
                      Colors.green,
                      _excludeDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (Your Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _filterDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (All Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _excludeDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (Your Coaches)',
                      Colors.grey.shade600,
                      _filterDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (All Coaches)',
                      Colors.blue,
                      _excludeDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Color color,
      Map<String, List<int>> details, Map<String, Map<String, int>>? totals) {
    if (details.isEmpty) return const SizedBox.shrink();

    Color backgroundColor = HSLColor.fromColor(color)
        .withLightness(0.95) // Very light shade
        .toColor();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        _buildDetailsTable(details, totals, backgroundColor),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDetailsTable(
    Map<String, List<int>> details,
    Map<String, Map<String, int>>? coachTotals,
    Color backgroundColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: backgroundColor,
      ),
      child: Table(
        columnWidths: const {
          0: IntrinsicColumnWidth(),
          1: FlexColumnWidth(),
        },
        border: TableBorder.all(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(8),
        ),
        children: [
          TableRow(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
            ),
            children: [
              _buildTableCell('Coach', backgroundColor, isHeader: true),
              _buildTableCell('Berths', backgroundColor, isHeader: true),
            ],
          ),
          ...details.entries
              .where((entry) => entry.value.isNotEmpty)
              .map((entry) => _buildDataRow(entry, coachTotals))
              .toList(),
        ],
      ),
    );
  }

  TableRow _buildDataRow(MapEntry<String, List<int>> entry,
      Map<String, Map<String, int>>? coachTotals) {
    final berthCount = entry.value.length;

    if (berthCount > 18) {
      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(entry.key,
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text('${coachTotals?[entry.key]?['onboard'] ?? 0}',
                    style: const TextStyle(color: Colors.green)),
                Text('${coachTotals?[entry.key]?['offboard'] ?? 0}',
                    style: const TextStyle(
                        color: Color.fromARGB(255, 237, 162, 76))),
                Text('${coachTotals?[entry.key]?['vacant'] ?? 0}',
                    style: TextStyle(color: Colors.grey.shade600)),
              ],
            ),
          ),
          _buildBerthsList(entry.key, entry.value),
        ],
      );
    }

    return TableRow(
      children: [
        _buildCoachCell(entry.key, coachTotals),
        _buildBerthsList(entry.key, entry.value),
      ],
    );
  }

  Widget _buildTableCell(String text, backgroundColor,
      {bool isHeader = false}) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildCoachCell(
      String coach, Map<String, Map<String, int>>? coachTotals) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(coach,
              style:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          Row(
            children: [
              Text('${coachTotals?[coach]?['onboard'] ?? 0} - ',
                  style: const TextStyle(color: Colors.green, fontSize: 13)),
              Text('${coachTotals?[coach]?['offboard'] ?? 0} - ',
                  style: const TextStyle(
                      color: Color.fromARGB(255, 237, 162, 76), fontSize: 13)),
              Text('${coachTotals?[coach]?['vacant'] ?? 0}',
                  style: const TextStyle(color: Colors.grey, fontSize: 13)),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildBerthsList(String coach, List<int> berths) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 4,
        runSpacing: 4,
        children: berths.map((berth) {
          final isInroute = isInrouteBerth(coach, berth);
          return Text(
            isInroute ? "(${berth})," : "${berth},",
            style: TextStyle(
              fontSize: 13,
              fontWeight: isInroute ? FontWeight.bold : FontWeight.normal,
            ),
          );
        }).toList(),
      ),
    );
  }
}
