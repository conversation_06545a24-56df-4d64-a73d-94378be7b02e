import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';

class BackgroundUploadTask {
  final String taskId;
  final Function(double) onProgress;
  final Function() onComplete;
  final Function(String) onError;
  StreamController<double>? _progressController;
  double _currentProgress = 0.0;
  bool _isCompleted = false;
  
  BackgroundUploadTask({
    required this.taskId,
    required this.onProgress,
    required this.onComplete,
    required this.onError,
  }) {
    _progressController = StreamController<double>.broadcast();
    _progressController!.stream.listen((progress) {
      _currentProgress = progress;
      onProgress(progress);
    });
  }
  
  double get progress => _currentProgress;
  bool get isCompleted => _isCompleted;
  
  void updateProgress(double progress) {
    if (_progressController != null && !_progressController!.isClosed) {
      _progressController!.add(progress);
    }
  }
  
  void complete() {
    if (_progressController != null && !_progressController!.isClosed) {
      _progressController!.add(1.0);
      _currentProgress = 1.0;
      _isCompleted = true;
      _progressController!.close();
      onComplete();
    }
  }
  
  void error(String message) {
    if (_progressController != null && !_progressController!.isClosed) {
      _progressController!.close();
      onError(message);
    }
  }
  
  void dispose() {
    if (_progressController != null && !_progressController!.isClosed) {
      _progressController!.close();
    }
  }
}