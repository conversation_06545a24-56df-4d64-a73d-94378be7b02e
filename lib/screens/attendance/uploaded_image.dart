import 'package:flutter/material.dart';
import 'package:railops/screens/attendance/widget/delete_button.dart';
import 'package:railops/screens/pages/image_detail_page.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/types/attendance_types/attendance_data.dart';
import 'package:intl/intl.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';

class UploadedImage extends StatefulWidget {
  final AttendanceData imageResponse;
  final String trainNumber;
  final VoidCallback? onImageDelete;

  const UploadedImage({
    Key? key,
    required this.imageResponse,
    required this.trainNumber,
    this.onImageDelete,
  }) : super(key: key);

  @override
  State<UploadedImage> createState() => _UploadedImageState();
}

class _UploadedImageState extends State<UploadedImage> {
  String? stationCode;
  String? distance;

  @override
  void initState() {
    super.initState();
    _fetchNearestStation();
  }

  Future<void> _fetchNearestStation() async {
    try {
      final nearestStation = await LocationService.getNearestStation(
        widget.imageResponse.latitude.toString(),
        widget.imageResponse.longitude.toString(),
      );

      setState(() {
        stationCode = nearestStation['station_code'] ?? 'Unknown';
        distance = nearestStation['distance_km'] != null
            ? '${nearestStation['distance_km']} km'
            : 'N/A';
      });
    } catch (e) {
      setState(() {
        stationCode = 'Error';
        distance = 'Error';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime createdAt = DateTime.parse(widget.imageResponse.createdAt);
    DateTime localTime = createdAt.toLocal();
    String formattedDate =
        DateFormat('yyyy-MM-dd - kk:mm:ss').format(localTime);

    return GestureDetector(
      onTap: () {
        ImageResponse detailPageImageResponse =
            ImageResponse.fromAttendanceData(widget.imageResponse);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                ImageDetailPage(imageResponse: detailPageImageResponse),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.all(5),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.imageResponse.imageUrl,
                  width: 80,
                  height: 100,
                  fit: BoxFit.cover,
                  errorBuilder: (BuildContext context, Object exception,
                      StackTrace? stackTrace) {
                    return Container(
                      width: 80,
                      height: 100,
                      color: Colors.grey,
                      child: const Icon(Icons.error, color: Colors.red),
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Uploaded for: ${widget.imageResponse.username}',
                      style: const TextStyle(fontSize: 11),
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Text(
                    //   'Uploaded at: $formattedDate',
                    //   style: const TextStyle(fontSize: 11),
                    //   overflow: TextOverflow.ellipsis,
                    // ),
                    Text(
                      'Uploaded by: ${widget.imageResponse.createdBy}',
                      style: const TextStyle(fontSize: 11),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'latitude: ${widget.imageResponse.latitude}, longitude: ${widget.imageResponse.longitude}',
                      style: const TextStyle(fontSize: 11),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      'Station: ${stationCode ?? 'Fetching...'}, Distance: ${distance ?? 'Fetching...'}',
                      style: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    DeleteButton(
                      date: widget.imageResponse.originDate,
                      stationCode: widget.imageResponse.stationCode!,
                      trainNumber: widget.trainNumber,
                      username: widget.imageResponse.username,
                      onDeleteSuccess: () {
                        if (widget.onImageDelete != null) {
                          widget.onImageDelete!();
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
