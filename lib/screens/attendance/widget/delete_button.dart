import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';

class DeleteButton extends StatefulWidget {
  final String username;
  final String trainNumber;
  final String stationCode;
  final String date;
  final VoidCallback? onDeleteSuccess;

  const DeleteButton({
    Key? key,
    required this.username,
    required this.trainNumber,
    required this.stationCode,
    required this.date,
    this.onDeleteSuccess,
  }) : super(key: key);

  @override
  State<DeleteButton> createState() => _DeleteButtonState();
}

class _DeleteButtonState extends State<DeleteButton> {
  bool isDeleting = false;
  String? token;
  String? usertype;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    usertype=userModel.userType;
  }
  void deleteItem() async {
    print(usertype);
    if(usertype != "railway admin"){
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            "Only admins can delete attendance. You do not have permission to perform this action.",
          ),
          backgroundColor: kErrorColor,
        ),
      );
      return;
    }
    try{
      setState(() {
        isDeleting = true;
      });
      final response = await AttendanceService.deleteAttendance(widget.username,widget.stationCode,widget.date,widget.trainNumber,token);

      setState(() {
        isDeleting = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            response,
          ),
          backgroundColor: Colors.green,
        ),
      );

      if (widget.onDeleteSuccess != null) {
        widget.onDeleteSuccess!();
      }
    }
    catch(e){
      setState(() {
        isDeleting = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            "Error while deleting attendance:$e",
          ),
          backgroundColor: kErrorColor,
        ),
      );
    }
  }

  void confirmDelete() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Delete'),
          content: Text(
            'Are you sure you want to delete train ${widget.trainNumber} at station ${widget.stationCode}?',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                deleteItem();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: isDeleting
          ? null // Disable button during deletion
          : confirmDelete,
      icon: isDeleting
          ? const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 2,
              ),
            )
          : const Icon(Icons.delete),
      label: Text(isDeleting ? 'Deleting...' : 'Delete'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5),
        ),
      ),
    );
  }
}
