import 'dart:async';

import 'package:flutter/material.dart';
import 'package:railops/screens/attendance/background_upload_task.dart';

class UploadManager extends ChangeNotifier {
  // Use a static instance to ensure it's the same across the app
  static final UploadManager _instance = UploadManager._internal();
  
  factory UploadManager() {
    return _instance;
  }
  
  UploadManager._internal();
  
  final Map<String, BackgroundUploadTask> _backgroundTasks = {};
  bool _isCompressing = false;
  Timer? _cleanupTimer;

  Map<String, BackgroundUploadTask> get backgroundTasks => _backgroundTasks;
  bool get isCompressing => _isCompressing;
  int get uploadCount => _backgroundTasks.length;
  
  bool get hasActiveUploads => _isCompressing || _backgroundTasks.isNotEmpty;

  double get averageProgress {
    if (_backgroundTasks.isEmpty) return 0.0;
    
    double totalProgress = 0.0;
    for (var task in _backgroundTasks.values) {
      totalProgress += task.progress;
    }
    
    return totalProgress / _backgroundTasks.length;
  }

  void setCompressing(bool value) {
    _isCompressing = value;
    notifyListeners();
  }

  void registerTask(String taskId, BackgroundUploadTask task) {
    _backgroundTasks[taskId] = task;
    _startCleanupTimer();
    notifyListeners();
  }

  void removeTask(String taskId) {
    if (_backgroundTasks.containsKey(taskId)) {
      _backgroundTasks[taskId]?.dispose();
      _backgroundTasks.remove(taskId);
      notifyListeners();
    }
  }
  
  // Periodically clean up completed tasks
  void _startCleanupTimer() {
    _cleanupTimer?.cancel();
    _cleanupTimer = Timer.periodic(const Duration(seconds: 5), (_) {
      _cleanupCompletedTasks();
    });
  }
  
  void _cleanupCompletedTasks() {
    final completedTaskIds = <String>[];
    
    for (var entry in _backgroundTasks.entries) {
      if (entry.value.isCompleted || entry.value.progress >= 0.999) {
        completedTaskIds.add(entry.key);
      }
    }
    
    if (completedTaskIds.isNotEmpty) {
      for (var taskId in completedTaskIds) {
        removeTask(taskId);
      }
    }
    
    if (_backgroundTasks.isEmpty) {
      _cleanupTimer?.cancel();
      _cleanupTimer = null;
    }
  }

  @override
  void dispose() {
    _cleanupTimer?.cancel();
    for (var task in _backgroundTasks.values) {
      task.dispose();
    }
    _backgroundTasks.clear();
    super.dispose();
  }
}