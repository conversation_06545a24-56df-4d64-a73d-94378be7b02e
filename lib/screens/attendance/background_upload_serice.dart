import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart' as img;
import 'dart:io';
import 'package:cross_file/cross_file.dart';
import 'package:railops/screens/attendance/background_upload_task.dart';
import 'package:railops/screens/attendance/upload_manager.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';

class BackgroundUploadService {
  static final BackgroundUploadService _instance = BackgroundUploadService._internal();
  
  factory BackgroundUploadService() {
    return _instance;
  }
  
  BackgroundUploadService._internal();
  
  Future<void> uploadImageAndCreateAttendance({
    required String imagePath,
    required String username,
    required String trainNumber,
    required String stationCode,
    required String date,
    required String token,
    required double? latitude,
    required double? longitude,
    required UploadManager uploadManager,
    required Function() onSuccess,
    required Function(String) onError,
  }) async {
    final String taskId = DateTime.now().millisecondsSinceEpoch.toString();
    
    final task = BackgroundUploadTask(
      taskId: taskId,
      onProgress: (progress) {
        // Progress updated via the task
      },
      onComplete: () {
        onSuccess();
        uploadManager.removeTask(taskId);
      },
      onError: (message) {
        onError(message);
        uploadManager.removeTask(taskId);
      },
    );
    
    uploadManager.registerTask(taskId, task);
    uploadManager.setCompressing(true);
    
    try {
      // Get the root isolate token
      final rootIsolateToken = RootIsolateToken.instance!;
      
      // Create the isolate for background processing
      final receivePort = ReceivePort();
      await Isolate.spawn(
        _backgroundProcessingIsolate,
        {
          'sendPort': receivePort.sendPort,
          'imageFile': imagePath,
          'username': username,
          'trainNumber': trainNumber,
          'stationCode': stationCode, 
          'date': date,
          'token': token,
          'latitude': latitude?.toString() ?? '',
          'longitude': longitude?.toString() ?? '',
          'taskId': taskId,
          'rootIsolateToken': rootIsolateToken,
        },
      );
      
      // Listen for messages from the isolate
      receivePort.listen((message) {
        if (message is Map) {
          if (message.containsKey('progress')) {
            task.updateProgress(message['progress']);
            
            // Set compressing to false after the image has been resized (progress around 0.5)
            if (message['progress'] >= 0.5 && message['progress'] < 0.6) {
              uploadManager.setCompressing(false);
            }
          } else if (message.containsKey('error')) {
            task.error(message['error']);
            uploadManager.setCompressing(false); // Also set to false on error
          } else if (message.containsKey('complete')) {
            task.complete();
            uploadManager.setCompressing(false); // Also set to false on completion
          }
        }
      });
    } catch (e) {
      task.error('Failed to process: $e');
      uploadManager.setCompressing(false); // Set to false on exception
    }
  }
  
  static Future<XFile> _resizeImageInBackground(Map<String, dynamic> params) async {
    final String path = params['path'];
    final int maxWidth = params['maxWidth'] ?? 800;
    
    final File imageFile = File(path);
    final img.Image? image = img.decodeImage(await imageFile.readAsBytes());
    
    if (image == null) {
      throw Exception('Failed to decode image');
    }
    
    // Calculate new dimensions maintaining aspect ratio
    int newWidth = image.width;
    int newHeight = image.height;
    
    if (newWidth > maxWidth) {
      final double ratio = newWidth / maxWidth;
      newWidth = maxWidth;
      newHeight = (newHeight / ratio).round();
    }
    
    final img.Image resizedImage = img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
    );
    
    // Save to temporary file
    final Directory tempDir = await getTemporaryDirectory();
    final String tempPath = '${tempDir.path}/resized_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final File resizedFile = File(tempPath);
    
    await resizedFile.writeAsBytes(img.encodeJpg(resizedImage, quality: 85));
    
    return XFile(resizedFile.path);
  }
  
  static void _backgroundProcessingIsolate(Map<String, dynamic> params) async {
    // Get the root isolate token from params
    final rootIsolateToken = params['rootIsolateToken'] as RootIsolateToken;
    
    // Initialize binary messenger in the isolate with the token
    BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);
    
    final SendPort sendPort = params['sendPort'];
    
    try {
      sendPort.send({'progress': 0.1});
      
      final String imagePath = params['imageFile'];
      final XFile resizedImage = await _resizeImageInBackground({
        'path': imagePath,
        'maxWidth': 200
      });
      
      sendPort.send({'progress': 0.5});  // Image resizing is complete here
      
      final String latitude = params['latitude'];
      final String longitude = params['longitude'];
      sendPort.send({'progress': 0.7});

      final response = await AttendanceService.createAttendance(
        imageFile: resizedImage,
        username: params['username'],
        trainNumber: params['trainNumber'],
        stationCode: params['stationCode'],
        date: params['date'],
        token: params['token'],
        latitude: latitude,
        longitude: longitude,
      );
      
      if (response != null) {
        sendPort.send({'progress': 1.0});
        sendPort.send({'complete': true});
      } else {
        sendPort.send({'error': 'Failed to upload image'});
      }
      
    } catch (e) {
      sendPort.send({'error': 'An error occurred: $e'});
    }
  }
}