import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:file_picker/file_picker.dart';
import 'package:railops/screens/upload_screen/widgets/upload_widget.dart';
import 'dart:convert';
import 'package:railops/services/upload_services/upload_services.dart';
import 'package:railops/types/upload_types/upload_request.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/widgets/index.dart';

class UploadJsonScreen extends StatelessWidget {
  const UploadJsonScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Upload JSON File'),
      drawer: const CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: FutureBuilder(
            future: Provider.of<AuthModel>(context, listen: false).loadAuthState(),
            builder: (context, AsyncSnapshot<void> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              } else if (snapshot.connectionState == ConnectionState.done) {
                final authModel = Provider.of<AuthModel>(context, listen: false);
                if (authModel.isAuthenticated) {
                  return UploadWidget(userModel: Provider.of<UserModel>(context));
                } else {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Navigator.pushReplacementNamed(context, Routes.login);
                  });
                  return const CircularProgressIndicator();
                }
              } else if (snapshot.hasError) {
                return const Text('Error loading authentication state');
              }
              return const CircularProgressIndicator();
            },
          ),
        ),
      ),
    );
  }
}
