import 'package:flutter/material.dart';
import 'package:railops/core/comman_widgets/app_button_with_icon.dart';
import 'package:railops/core/comman_widgets/input_fields/app_textfield.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/comman_functions.dart';
import 'package:railops/services/profile_services/pnr_service.dart';
import 'package:railops/types/profile_types/pnr_response.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class PnrStatus extends StatefulWidget {
  const PnrStatus({super.key});

  @override
  State<PnrStatus> createState() => _PnrStatusState();
}

class _PnrStatusState extends State<PnrStatus> {
  final TextEditingController _pnrController = TextEditingController();
  bool _isLoading = false;
  PnrResponse? _pnrResponse;

  void checkPnr() async {
    if (_pnrController.text.isEmpty || _pnrController.text.length != 10) {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text("Please enter a valid 10-digit PNR number")));
      return;
    }

    try {
      setState(() => _isLoading = true);
      //on your local use this
      // final token = await CommanFunctions().getToken();
      // final response = await PnrService.checkPNR(_pnrController.text, token);
      final response = await PnrService.checkPNR(_pnrController.text);
      if (response == null) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text("Failed to fetch PNR data. Please try again.")));
        return;
      }
      setState(() => _pnrResponse = response);
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text(e.toString())));
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _formatUserList(List<dynamic>? userList) {
    if (userList == null || userList.isEmpty) return "-";
    return userList.join(", ");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'PNR Status'),
      drawer: const CustomDrawer(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text("Check PNR Status",
                style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: kTextColor)),
            const SizedBox(height: 30),
            AppTextField(
              controller: _pnrController,
              textInputType: TextInputType.phone,
              hint: "Enter PNR Number",
              maxLength: 10,
              label: const Text("PNR Number"),
              prefixIcon: const Icon(Icons.confirmation_number, color: kPrimaryColor),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: AppButtonWithIcon(
                  title: "Check PNR",
                  onTap: checkPnr,
                  fontColor: Colors.black,
                  buttonColor: Colors.white,
                  borderColor: Colors.black87),
            ),
            const SizedBox(height: 40),
            if (_isLoading) const CircularProgressIndicator(),
            if (!_isLoading && _pnrResponse == null)
              const Text("No PNR Data Found"),
            if (_pnrResponse != null) buildPnrTable(),
          ],
        ),
      ),
    );
  }

  Widget buildPnrTable() {
    return Column(
      children: [
        Table(
          border: TableBorder.all(color: Colors.black),
          columnWidths: const {0: FractionColumnWidth(0.3)},
          children: [
            buildTableRow("Train Number", _pnrResponse!.trainNumber),
            buildTableRow("Train Name", _pnrResponse!.trainName),
            buildTableRow("Boarding Date", _pnrResponse!.dateOfJourney),
            buildTableRow("From", _pnrResponse!.journeyFrom),
            buildTableRow("To", _pnrResponse!.journeyTo),
            buildTableRow("Class", _pnrResponse!.journeyClass),
            buildTableRow("Departure", _pnrResponse!.departureTime),
            buildTableRow("Arrival", _pnrResponse!.arrivalTime),
            buildTableRow("Overall Status", _pnrResponse!.overallStatus),
            buildTableRow("Booking Date", _pnrResponse!.bookingDate),
          ],
        ),

        const SizedBox(height: 20),
        Table(
          border: TableBorder.all(color: Colors.black),
          columnWidths: const {0: FractionColumnWidth(0.5)},
          children: [
            buildTableRow("Passenger", "Coach / Berth"),
            ..._buildPassengerRows(),
          ],
        ),

        const SizedBox(height: 20),

        // Service Staff Table
        Table(
          border: TableBorder.all(color: Colors.black),
          children: [
            buildTableRow(
                "EHK Users", _formatUserList(_pnrResponse!.ehkUsernames)),
            buildTableRow(
                "CA Users", _formatUserList(_pnrResponse!.caUsernames)),
            buildTableRow(
                "OBHS Users", _formatUserList(_pnrResponse!.obhsUsernames)),
          ],
        ),

        const SizedBox(height: 20),
        Table(
          border: TableBorder.all(color: Colors.black),
          children: [
            buildTableRow(
                "EHK Users", _formatUserList(_pnrResponse!.ehkUsernames)),
            buildTableRow(
                "CA Users", _formatUserList(_pnrResponse!.caUsernames)),
            buildTableRow(
                "OBHS Users", _formatUserList(_pnrResponse!.obhsUsernames)),
          ],
        ),
      ],
    );
  }

  List<TableRow> _buildPassengerRows() {
    return List.generate(_pnrResponse!.passengerNames.length, (index) {
      String passengerName = _pnrResponse!.passengerNames[index];

      // Special case handling for specific trains (as in your original code)
      if (passengerName == "1" &&
          (_pnrResponse!.trainNumber == "12334" ||
              _pnrResponse!.trainNumber == "22213")) {
        passengerName = "Atul Anand";
      }

      return buildTableRow(passengerName,
          "${_pnrResponse!.passengerCoaches[index]} / ${_pnrResponse!.passengerBerths[index]}");
    });
  }

  TableRow buildTableRow(String label, String value) {
    return TableRow(
      children: [
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: Text(value),
        ),
      ],
    );
  }
}
