import 'dart:io' as io;
import 'package:flutter/foundation.dart' show Uint8List, kIsWeb;
import 'package:flutter/material.dart';
import 'package:railops/screens/rail_sathi/view_complaints.dart';
import 'package:railops/screens/rail_sathi/write_complaint.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';


class RailSathi extends StatelessWidget {
  const RailSathi({super.key});

  @override
  Widget build(BuildContext context) {
    return const DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: CustomAppBar(title: 'Rail Sathi'),
        drawer: CustomDrawer(),
        body: Column(
          children: [
            TabBar(
              tabs: [
                Tab(text: 'Write Complaint'),
                Tab(text: 'View Complaints'),
              ],
            ),
            Expanded(
              child: TabBar<PERSON>iew(
                children: [
                  WriteComplaintTab(),
                  ViewComplaintsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
