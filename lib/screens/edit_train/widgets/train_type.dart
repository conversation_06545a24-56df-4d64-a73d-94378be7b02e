
import 'package:flutter/material.dart';

class TrainTypeField extends StatelessWidget {
  final TextEditingController controller;
  final bool enabled;
  const TrainTypeField({Key? key, required this.controller,this.enabled = true, required Function(String? p1) onChanged,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextForm<PERSON>ield(
        controller: controller,
        decoration: InputDecoration(
          labelText: 'Train Type',
          filled: true,
          fillColor: Colors.blue.shade50,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.blue, width: 1),
          ),
        ),
        textCapitalization: TextCapitalization.characters,
      ),
    );
  }
}
