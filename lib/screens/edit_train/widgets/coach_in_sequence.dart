import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:railops/screens/enable_disable_user/enable_disable_user.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/widgets/success_modal.dart';
import 'package:uuid/uuid.dart';

class ChipModel {
  final String id;
  final String name;
  ChipModel({required this.id, required this.name});
}

class CoachListWidget extends StatefulWidget {
  final String trainNo;
  final String token;

  const CoachListWidget({
    Key? key,
    required this.trainNo,
    required this.token,
  }) : super(key: key);

  @override
  _CoachListWidgetState createState() => _CoachListWidgetState();
}

class _CoachListWidgetState extends State<CoachListWidget> {
  List<ChipModel> _chipList = [];
  ChipModel? _draggedChip;
  late TextEditingController _chipTextController;
  final ScrollController _scrollController = ScrollController();
  bool isLoading = false;
  String? error;
  List<String> coaches = [];
  final Uuid _uuid = Uuid();

  @override
  void initState() {
    super.initState();
    _chipTextController = TextEditingController();
    _initializeCoaches();
  }

  void _initializeCoaches() {
    setState(() {
      isLoading = true;
    });
    _fetchCoaches();
  }

  @override
  void didUpdateWidget(CoachListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if trainNo has changed
    if (oldWidget.trainNo != widget.trainNo) {
      _initializeCoaches();
    }
  }

  Future<void> _fetchCoaches() async {
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      final response =
          await EditTrainServices.fetchCoachesByTrainNo(widget.trainNo);
      if (!mounted) return;

      if (response != null && response.data != null) {
        setState(() {
          coaches = response.data;
          _chipList = List<String>.from(response.data ?? [])
              .map((coach) => ChipModel(
                  id: _uuid.v4(), name: coach)) // Use coach name as ID
              .toList();
        });
      } else {
        setState(() {
          error = 'No coaches data received';
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        error = 'Error fetching coaches: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  void _deleteChip(String id) async {
    try {
      // Call backend service to delete the coach
      final coachToDelete = _chipList.firstWhere((chip) => chip.id == id);
      final coachName = coachToDelete.name;

      final response = await EditTrainServices.updateCoachByTrainNo(
        widget.trainNo,
        "delete_coach", // Assuming "delete" is the operation for removing a coach
        coachName, // Use the coach name directly as the identifier
        widget.token,
      );

      if (response != null) {
        // Update local state if the deletion is successful
        setState(() {
          _chipList.removeWhere((chip) => chip.id == id);
        });
        print("Coach $coachName deleted successfully.");
        showSuccessModal(context, '${response.message}', "Success", () {});
      } else {
        throw Exception("Failed to delete coach");
      }
    } catch (e) {
      print("Error deleting coach: $e");
      showErrorModal(context, '$e', "Error", () {});
    }
  }

  void _addChip() async {
    final newCoachName = _chipTextController.text.trim().toUpperCase();

    if (newCoachName.isNotEmpty) {
      final newCoaches =
          newCoachName.split(',').map((coach) => coach.trim()).toList();

      try {
        // Make a backend call to update the coach
        final response = await EditTrainServices.updateCoachByTrainNo(
          widget.trainNo,
          "add_coach",
          newCoachName, // Specify the update type as "add"
          widget.token,
        );

        if (response == null) {
          throw Exception("Failed to update coaches on the backend");
        }

        for (final newCoach in newCoaches) {
          if (newCoach.isEmpty) continue;
          // bool isCoachAlreadyAdded =
          //     _chipList.any((chip) => chip.id == newCoach);
          // if (!isCoachAlreadyAdded) {
          setState(() {
            _chipList.add(ChipModel(
              id: _uuid.v4(), // Use coach name as ID
              name: newCoach,
            ));
          });
          // }
        }

        // Scroll to the end of the chip list
        Future.delayed(const Duration(milliseconds: 100), () {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 360),
              curve: Curves.easeOut,
            );
          }
        });

        showSuccessModal(context, '${response.message}', "Success", () {});
        print("Coach $newCoachName added successfully.");
      } catch (e) {
        print("Error adding coach: $e");
        // Show an error message

        showErrorModal(context, '$e', "Error", () {});
      }
    }
  }

  Widget _getCoachIcon(String coachName) {
    final Map<String, String> iconMapping = {
      'H': 'AC1.png',
      'GSL': 'AC3.png',
      'LSL': 'AC3.png',
      'EO': 'EOG.png',
      'LS': 'EOG.png',
      'A': 'AC2.png',
      'E': 'E.png',
      'B': 'AC3.png',
      'M': 'AC3E.png',
      'S': 'S.png',
      'GS': 'GS.png',
      'PC': 'PC.png',
      'AC3': 'AC3.png',
      'PW': 'EOG.png',
    };

    String? iconPath = iconMapping.entries
        .firstWhere((entry) => coachName.startsWith(entry.key),
            orElse: () => const MapEntry('', ''))
        .value;

    return iconPath.isNotEmpty
        ? Image.asset('assets/images/train_icons/$iconPath',
            height: 36, width: 36, fit: BoxFit.contain)
        : Icon(Icons.train, color: Colors.blue, size: 36);
  }

  Future<void> _updateCoachOrder() async {
    try {
      final reorderedCoaches = _chipList.map((chip) => chip.name).toList();
      print(reorderedCoaches);
      final response = await EditTrainServices.updateCoachByTrainNo(
        widget.trainNo,
        "reorder_coach",
        reorderedCoaches.join(","),
        widget.token,
      );

      if (response == null) {
        throw Exception("Failed to update coach order on the backend");
      }

      showSuccessModal(context, '${response.message}', "Success", () {});
      print("Coach order updated successfully.");
    } catch (e) {
      print("Error updating coach order: $e");

      showErrorModal(context, '$e', "Error", () {});
    }
  }

  @override
  void dispose() {
    _chipTextController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(1.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Coaches for Train No. ${widget.trainNo}',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          // Display loading or error states
          if (isLoading)
            Center(child: CircularProgressIndicator())
          else if (_chipList.isEmpty)
            Center(child: Text('No coaches available'))
          else
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              controller: _scrollController,
              child: Row(
                children: _chipList.map((chip) {
                  return LongPressDraggable<ChipModel>(
                    childWhenDragging: Container(),
                    data: chip,
                    feedback: Material(
                      color: Colors.transparent,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              _getCoachIcon(chip.name),
                              SizedBox(height: 4),
                              Text(
                                chip.name,
                                style: TextStyle(color: Colors.black),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    child: DragTarget<ChipModel>(
                      onWillAccept: (data) => data != chip,
                      onAccept: (data) {
                        setState(() {
                          final oldIndex = _chipList.indexOf(data);
                          final newIndex = _chipList.indexOf(chip);
                          _chipList.removeAt(oldIndex);
                          _chipList.insert(newIndex, data);
                        });
                        _updateCoachOrder();
                      },
                      builder: (context, candidateData, rejectedData) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                _getCoachIcon(chip.name),
                                SizedBox(height: 4),
                                Text(
                                  chip.name,
                                  style: TextStyle(color: Colors.black),
                                ),
                                SizedBox(height: 4),
                                GestureDetector(
                                  onTap: () => _deleteChip(chip.id),
                                  child: Icon(Icons.delete,
                                      color: Colors.red, size: 20),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  );
                }).toList(),
              ),
            ),

          SizedBox(height: 20),

          // Add coach input field
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: TextField(
                  controller: _chipTextController,
                  decoration: InputDecoration(
                    labelText: 'Enter new coach',
                    border: OutlineInputBorder(),
                    enabled: !isLoading,
                    helperText: 'Use comma to add multiple coaches',
                  ),
                  onSubmitted: (value) => _addChip(),
                ),
              ),
              SizedBox(width: 10),
              Align(
                alignment: Alignment.center, // Center the IconButton vertically
                child: Material(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(36),
                  child: IconButton(
                    icon: const Icon(Icons.add, color: Colors.white),
                    onPressed: isLoading ? null : _addChip,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
