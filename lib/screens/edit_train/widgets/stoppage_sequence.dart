import 'package:flutter/material.dart';

class ChipModel {
  final String id;
  final String name;

  ChipModel({required this.id, required this.name});
}

class StoppageSequence extends StatefulWidget {
  final List<String> stoppages;
  final TextEditingController stoppageController;
  final Function(List<String>) onStoppagesUpdated;
  final bool enabled;


  const StoppageSequence({
    Key? key,
    required this.stoppages,
    required this.stoppageController,
    required this.onStoppagesUpdated,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<StoppageSequence> createState() => _StoppageSequenceState();
}

class _StoppageSequenceState extends State<StoppageSequence> {
  late List<ChipModel> _chipList;
  ChipModel? _draggedChip;
  late TextEditingController _chipTextController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Convert List<String> to List<ChipModel>
    _chipList = widget.stoppages
        .map((stoppage) =>
            ChipModel(id: DateTime.now().toString(), name: stoppage))
        .toList();

    _chipTextController = widget.stoppageController;

    _chipTextController.addListener(() {
      if (_chipTextController.text.endsWith(' ')) {
        _addChip();
      }
    });
  }

  @override
  void didUpdateWidget(covariant StoppageSequence oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.stoppages != widget.stoppages) {
      setState(() {
        _chipList = widget.stoppages
            .map((stoppage) =>
                ChipModel(id: DateTime.now().toString(), name: stoppage))
            .toList();
      });

      // widget.onStoppagesUpdated(_chipList.map((chip) => chip.name).toList(),);
    }
  }

  void _deleteChip(String id) {
    setState(() {
      _chipList.removeWhere((element) => element.id == id);
    });
    widget.onStoppagesUpdated(
      _chipList.map((chip) => chip.name).toList(),
    );
  }

  void _addChip() {
    if (_chipTextController.text.trim().isNotEmpty) {
      setState(() {
        _chipList.add(ChipModel(
          id: DateTime.now().toString(),
          name: _chipTextController.text
              .trim()
              .toUpperCase(), // Convert to uppercase
        ));
        _chipTextController.clear();
      });

      widget.onStoppagesUpdated(
        _chipList.map((chip) => chip.name).toList(),
      );

      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _chipTextController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(0),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: Row(
              children: _chipList.asMap().entries.map(
                (entry) {
                  final chip = entry.value;
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: LongPressDraggable<ChipModel>(
                      data: chip,
                      feedback: Material(
                        color: Colors.transparent,
                        child: Chip(
                          label: Text(chip.name,
                              style: TextStyle(color: Colors.black)),
                          backgroundColor: Colors.white,
                          deleteIcon: Icon(Icons.delete, color: Colors.blue),
                          onDeleted: () => _deleteChip(chip.id),
                          side: BorderSide(
                              color: Colors.black87,
                              width: 0.5), // Removes the chip border
                        ),
                      ),
                      child: DragTarget<ChipModel>(
                        onWillAccept: (data) => data != chip,
                        onAccept: (data) {
                          setState(() {
                            final oldIndex = _chipList.indexOf(data);
                            final newIndex = _chipList.indexOf(chip);
                            _chipList.removeAt(oldIndex);
                            _chipList.insert(newIndex, data);
                          });
                          widget.onStoppagesUpdated(
                            _chipList.map((chip) => chip.name).toList(),
                          );
                        },
                        builder: (context, candidateData, rejectedData) {
                          return Chip(
                            label: Text(chip.name,
                                style: TextStyle(color: Colors.black)),
                            backgroundColor: Colors.white,
                            deleteIcon: Icon(Icons.delete, color: Colors.blue),
                            onDeleted: () => _deleteChip(chip.id),
                            side: BorderSide(
                                color: Colors.black87,
                                width: 0.5), // Removes the chip border
                          );
                        },
                      ),
                      onDragStarted: () {
                        setState(() {
                          _draggedChip = chip;
                        });
                      },
                      onDragEnd: (details) {
                        setState(() {
                          _draggedChip = null;
                        });
                      },
                    ),
                  );
                },
              ).toList(),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _chipTextController,
                  decoration: InputDecoration(
                      labelText: 'Stoppages in Sequence',
                      filled: true,
                      fillColor: Colors.blue.shade50,
                      contentPadding:
                          const EdgeInsets.symmetric(horizontal: 16.0),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey, width: 1),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.blue, width: 1),
                      ),
                      hintText: 'Type a Station and hit space or Enter'),
                  onSubmitted: (value) => _addChip(),
                ),
              ),
              const SizedBox(width: 10),
              Material(
                color: Colors.blueAccent,
                borderRadius: BorderRadius.circular(24),
                child: IconButton(
                  icon: const Icon(Icons.add, color: Colors.white),
                  onPressed: _addChip,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
