import 'package:flutter/material.dart';

class CoachesInputField extends StatefulWidget {
  final TextEditingController controller;
  final Function(List<String>) onCoachesUpdated;

  const CoachesInputField({
    Key? key,
    required this.controller,
    required this.onCoachesUpdated,
  }) : super(key: key);

  @override
  State<CoachesInputField> createState() => _CoachesInputFieldState();
}

class _CoachesInputFieldState extends State<CoachesInputField> {
  List<String> _coaches = [];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: widget.controller,
          decoration: const InputDecoration(
            labelText: 'Coaches (comma separated)',
            hintText: 'E.g., H, GSL, A, B, M',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(8.0)),
            ),
            helperText: 'Enter coach names separated by commas',
          ),
          onChanged: (value) {
            // Split by comma and trim whitespace
            final updatedCoaches = value
                .split(',')
                .map((coach) => coach.trim().toUpperCase())
                .where((coach) => coach.isNotEmpty)
                .toList();
                
            setState(() {
              _coaches = updatedCoaches;
            });
            
            widget.onCoachesUpdated(updatedCoaches);
          },
        ),
        if (_coaches.isNotEmpty) ...[
          const SizedBox(height: 10),
          Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: _coaches.map((coach) {
              return Chip(
                label: Text(coach),
                backgroundColor: Colors.blue.shade100,
              );
            }).toList(),
          ),
        ],
      ],
    );
  }
}