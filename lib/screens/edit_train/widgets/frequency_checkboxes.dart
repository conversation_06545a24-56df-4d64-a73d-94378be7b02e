import 'package:flutter/material.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class DaysOfWeekDropdown extends StatefulWidget {
  final void Function(List<int>) onSaved;
  final List<int> preselectedDays;

  const DaysOfWeekDropdown(
      {Key? key, required this.onSaved, this.preselectedDays = const []})
      : super(key: key);

  @override
  _DaysOfWeekDropdownState createState() => _DaysOfWeekDropdownState();
}

class _DaysOfWeekDropdownState extends State<DaysOfWeekDropdown> {
  final List<DropdownItem<Day>> dayItems = [];
  final MultiSelectController<Day> controller = MultiSelectController<Day>();
  final List<Day> daysOfWeek = [
    Day(name: 'Monday', id: 1),
    Day(name: 'Tuesday', id: 2),
    Day(name: 'Wednesday', id: 3),
    Day(name: 'Thursday', id: 4),
    Day(name: 'Friday', id: 5),
    Day(name: 'Saturday', id: 6),
    Day(name: 'Sunday', id: 7),
  ];
  bool _isSelectingAll = false;

  @override
  void initState() {
    super.initState();
    _loadDays();
    _preselectDays(widget.preselectedDays);
  }

  @override
  void didUpdateWidget(covariant DaysOfWeekDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.preselectedDays != widget.preselectedDays) {
      _preselectDays(widget.preselectedDays);
    }
  }

  Future<void> _loadDays() async {
    setState(() {
      dayItems
        ..clear()
        ..add(DropdownItem(label: 'Daily', value: Day(name: 'Daily', id: 0)))
        ..addAll(
            daysOfWeek.map((day) => DropdownItem(label: day.name, value: day)));
      controller.setItems(dayItems);
    });
  }

  void _preselectDays(List<int> preselectedDays) {
    // for (var day in daysOfWeek) {
    //   if (preselectedDays.contains(day.id)) {
    //     // controller.selectItem(day);
    //   }
    // }
    controller.clearAll();
    controller.selectWhere((item) => preselectedDays.contains(item.value.id));
  }

  @override
  Widget build(BuildContext context) {
    return MultiDropdown<Day>(
        items: dayItems,
        controller: controller,
        enabled: dayItems.isNotEmpty,
        searchEnabled: false,
        fieldDecoration: FieldDecoration(
          backgroundColor: Colors.blue.shade50 ,
          hintText: 'Select Days',
          hintStyle: const TextStyle(color: Colors.black54),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: Colors.grey),
          ),
        ),
        dropdownDecoration: const DropdownDecoration(
          marginTop: 2,
          maxHeight: 300,
          header: Padding(
            padding: EdgeInsets.all(8),
            child: Text(
              'Select Days of the Week',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        dropdownItemDecoration: DropdownItemDecoration(
          selectedIcon: const Icon(Icons.check_box, color: Colors.green),
          disabledIcon: Icon(Icons.lock, color: Colors.grey.shade300),
        ),
        onSelectionChange: (selectedItems) {
          final selectedIds = selectedItems.map((day) => day.id).toList();


          // If "Daily" (id = 0) is selected
          if (selectedIds.contains(0) && !_isSelectingAll) {
            
            _isSelectingAll = true;
            setState(() {
              controller.selectAll();
            });
            return;
            
          }
          // If "Daily" is deselected explicitly, clear all selections
          else if (_isSelectingAll && !selectedIds.contains(0)) {
            _isSelectingAll = false;
            setState(() {
              controller.clearAll();
            });
            return;
          }
          // If any day is deselected while "Daily" is selected, deselect "Daily"
          
          else if (_isSelectingAll &&
              selectedItems.length <= daysOfWeek.length) {
            _isSelectingAll = false;
            setState(() {
              controller.clearAll();
              controller.selectWhere((item) =>
                  selectedIds.contains(item.value.id) && item.value.id != 0);
            });
            return;
          }

          // Save selection excluding "Daily" (id = 0) if items are selected
          if (selectedItems.isNotEmpty) {
            widget.onSaved(selectedIds.where((id) => id != 0).toList());
          }
        }
      );
  }
}

class Day {
  final String name;
  final int id;

  Day({required this.name, required this.id});

  @override
  String toString() {
    return 'Day(name: $name, id: $id)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Day && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
