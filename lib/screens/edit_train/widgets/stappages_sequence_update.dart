import 'package:flutter/material.dart';

class ChipModel {
  final String id;
  final String name;

  ChipModel({required this.id, required this.name});
}

class StappagesSequenceUpdate extends StatefulWidget {
  final List<String> stoppages;
  final List<String> attendanceStoppages;
  final TextEditingController stoppageController;
  final void Function(String stoppageName, String action) onStoppagesUpdated;

  const StappagesSequenceUpdate({
    Key? key,
    required this.stoppages,
    required this.attendanceStoppages,
    required this.stoppageController,
    required this.onStoppagesUpdated,
  }) : super(key: key);

  @override
  _StappagesSequenceUpdateState createState() =>
      _StappagesSequenceUpdateState();
}

class _StappagesSequenceUpdateState extends State<StappagesSequenceUpdate> {
  late List<ChipModel> _inactiveChips;
  late List<ChipModel> _activeChips;
  ChipModel? _draggedChip;

  @override
  void initState() {
    super.initState();
    _inactiveChips = widget.stoppages
        .map((s) => ChipModel(id: DateTime.now().toIso8601String(), name: s))
        .toList();
    _activeChips = widget.attendanceStoppages
        .map((s) => ChipModel(id: DateTime.now().toIso8601String(), name: s))
        .toList();
  }

  @override
  void didUpdateWidget(covariant StappagesSequenceUpdate oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.stoppages != widget.stoppages) {
      setState(() {
        _inactiveChips = widget.stoppages
            .map(
                (s) => ChipModel(id: DateTime.now().toIso8601String(), name: s))
            .toList();
      });
    }
    if (oldWidget.attendanceStoppages != widget.attendanceStoppages) {
      setState(() {
        _activeChips = widget.attendanceStoppages
            .map(
                (s) => ChipModel(id: DateTime.now().toIso8601String(), name: s))
            .toList();
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onChipAction(String name, String action) {
    widget.onStoppagesUpdated(name, action);
  }

  @override
  Widget build(BuildContext context) {
    final maxSectionHeight = MediaQuery.of(context).size.height * 0.3;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(left: 8.0, top: 8.0),
          child: Text(
            'Active Stoppages:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16.0,
              color: Colors.black,
            ),
          ),
        ),
        const SizedBox(height: 8),
        ConstrainedBox(
          constraints: BoxConstraints(maxHeight: maxSectionHeight),
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            child: Wrap(
              spacing: 4,
              runSpacing: 4,
              children: _activeChips.map((chip) {
                return _buildDraggableChip(
                  chip: chip,
                  onDelete: () => _onChipAction(chip.name, 'remove'),
                  deleteIcon: Icons.delete,
                  onReorder: (data, target) {
                    setState(() {
                      final oldIndex = _activeChips.indexOf(data);
                      final newIndex = _activeChips.indexOf(target);
                      _activeChips.removeAt(oldIndex);
                      _activeChips.insert(newIndex, data);
                    });
                  },
                );
              }).toList(),
            ),
          ),
        ),
        const Divider(),
        const Padding(
          padding: EdgeInsets.only(left: 8.0, top: 8.0),
          child: Text(
            'In-Active Stoppages:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16.0,
              color: Colors.black,
            ),
          ),
        ),
        const SizedBox(height: 8),
        ConstrainedBox(
          constraints: BoxConstraints(maxHeight: maxSectionHeight),
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
            child: Wrap(
              spacing: 4,
              runSpacing: 4,
              children: _inactiveChips.map((chip) {
                return _buildDraggableChip(
                  chip: chip,
                  onDelete: () => _onChipAction(chip.name, 'add'),
                  deleteIcon: Icons.add,
                  onReorder: (data, target) {
                    setState(() {
                      final oldIndex = _inactiveChips.indexOf(data);
                      final newIndex = _inactiveChips.indexOf(target);
                      _inactiveChips.removeAt(oldIndex);
                      _inactiveChips.insert(newIndex, data);
                    });
                  },
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDraggableChip({
    required ChipModel chip,
    required VoidCallback onDelete,
    required IconData deleteIcon,
    required void Function(ChipModel data, ChipModel target) onReorder,
  }) {
    return Padding(
      padding: const EdgeInsets.all(2),
      child: LongPressDraggable<ChipModel>(
        data: chip,
        feedback: Material(
          color: Colors.transparent,
          child: Chip(
            label: Text(chip.name, style: const TextStyle(color: Colors.black)),
            backgroundColor: Colors.white,
            deleteIcon: Icon(deleteIcon, color: Colors.blue),
            onDeleted: onDelete,
            side: const BorderSide(color: Colors.black87, width: 0.5),
          ),
        ),
        child: DragTarget<ChipModel>(
          onWillAcceptWithDetails: (data) => data != chip,
          onAcceptWithDetails: (data) => onReorder(data as ChipModel, chip),
          builder: (context, candidateData, rejectedData) {
            return Chip(
              label:
                  Text(chip.name, style: const TextStyle(color: Colors.black)),
              backgroundColor: Colors.white,
              deleteIcon: Icon(deleteIcon, color: Colors.blue),
              onDeleted: onDelete,
              side: const BorderSide(color: Colors.black87, width: 0.5),
            );
          },
        ),
        onDragStarted: () => setState(() => _draggedChip = chip),
        onDragEnd: (_) => setState(() => _draggedChip = null),
      ),
    );
  }
}
