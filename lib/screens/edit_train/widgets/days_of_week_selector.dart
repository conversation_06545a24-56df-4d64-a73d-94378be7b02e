import 'package:flutter/material.dart';

class DaysOfWeekSelector extends StatefulWidget {
 
  final Function(List<int>) onSelectionChanged;
  
  /// Initially selected days (1-7 where 1=Monday, 7=Sunday)
  final List<int> selectedDays;
  const DaysOfWeekSelector({
    Key? key, 
    required this.onSelectionChanged,
    required this.selectedDays,
  }) : super(key: key);

  @override
  State<DaysOfWeekSelector> createState() => _DaysOfWeekSelectorState();
}

class _DaysOfWeekSelectorState extends State<DaysOfWeekSelector> {
  
  late List<int> _selectedDays;
  // Day names in order (Monday-Sunday)
  final List<String> _dayNames = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
  ];

  @override
  void initState() {
    super.initState();
    // Create a copy of the list to avoid modifying the original
    _selectedDays = List<int>.from(widget.selectedDays);
  }

  @override
  void didUpdateWidget(DaysOfWeekSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Check if we need to update our internal state when widget props change
    if (!_listsAreEqual(oldWidget.selectedDays, widget.selectedDays)) {
      setState(() {
        _selectedDays = List<int>.from(widget.selectedDays);
      });
    }
  }

  bool _listsAreEqual(List<int> list1, List<int> list2) {
    if (list1.length != list2.length) return false;
    
    for (int i = 0; i < list1.length; i++) {
      if (!list2.contains(list1[i])) return false;
    }
    
    return true;
  }

  void _toggleDay(int dayIndex) {
    setState(() {
      if (_selectedDays.contains(dayIndex)) {
        _selectedDays.remove(dayIndex);
      } else {
        _selectedDays.add(dayIndex);
      }
      // Sort the list for consistent ordering
      _selectedDays.sort();
      
      // Notify parent of the change
      widget.onSelectionChanged(_selectedDays);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.only(bottom: 8.0),
          child: Text(
            'Select Days Of Week',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        FormField<List<int>>(
          initialValue: _selectedDays,
          builder: (FormFieldState<List<int>> state) {
            return Wrap(
              spacing: 8.0,
              runSpacing: 8.0,
              children: List.generate(_dayNames.length, (index) {
                // Days are 1-indexed in your API
                final dayIndex = index + 1;
                final isSelected = _selectedDays.contains(dayIndex);
                
                return ChoiceChip(
                  label: Text(_dayNames[index]),
                  selected: isSelected,
                  onSelected: (_) => _toggleDay(dayIndex),
                  backgroundColor: Colors.grey[200],
                  selectedColor: Colors.blue[100],
                );
              }),
            );
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please select at least one day';
            }
            return null;
          },
        ),
      ],
    );
  }
}