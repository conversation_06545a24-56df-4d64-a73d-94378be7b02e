import 'package:flutter/material.dart';

class ChipModel {
  final String id;
  final String name;
  ChipModel({required this.id, required this.name});
}

class StoppagesSequenceInput extends StatefulWidget {
  final List<String> stoppages;
  final TextEditingController stoppageController;
  final Function(List<String>) onStoppagesUpdated;

  const StoppagesSequenceInput({
    Key? key,
    required this.stoppages,
    required this.stoppageController,
    required this.onStoppagesUpdated,
  }) : super(key: key);

  @override
  State<StoppagesSequenceInput> createState() => _StoppagesSequenceInputState();
}

class _StoppagesSequenceInputState extends State<StoppagesSequenceInput> {
  late List<ChipModel> _chipList;
  ChipModel? _draggedChip;
  late TextEditingController _chipTextController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // Convert List<String> to List<ChipModel>
    _chipList = widget.stoppages
        .map((stoppage) =>
            ChipModel(id: DateTime.now().toString(), name: stoppage))
        .toList();

    _chipTextController = widget.stoppageController;

    _chipTextController.addListener(() {
      if (_chipTextController.text.endsWith(' ')) {
        _addChip();
      }
    });
  }

  @override
  void didUpdateWidget(covariant StoppagesSequenceInput oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.stoppages != widget.stoppages) {
      setState(() {
        _chipList = widget.stoppages
            .map((stoppage) =>
                ChipModel(id: DateTime.now().toString(), name: stoppage))
            .toList();
      });
    }
  }

  void _deleteChip(String id) {
    setState(() {
      _chipList.removeWhere((element) => element.id == id);
    });
    widget.onStoppagesUpdated(
      _chipList.map((chip) => chip.name).toList(),
    );
  }

  void _addChip() {
    if (_chipTextController.text.trim().isNotEmpty) {
      setState(() {
        _chipList.add(ChipModel(
          id: DateTime.now().toString(),
          name: _chipTextController.text.trim().toUpperCase(),
        ));
        _chipTextController.clear();
      });

      widget.onStoppagesUpdated(
        _chipList.map((chip) => chip.name).toList(),
      );

      Future.delayed(const Duration(milliseconds: 100), () {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
          width: double.infinity,
          // decoration: BoxDecoration(
          //   color: Colors.grey.shade100,
          //   borderRadius: BorderRadius.circular(8.0),
          //   border: Border.all(color: Colors.grey.shade300),
          // ),
          child: Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: _chipList.asMap().entries.map(
              (entry) {
                final chip = entry.value;
                return LongPressDraggable<ChipModel>(
                  data: chip,
                  feedback: Material(
                    color: Colors.transparent,
                    child: Chip(
                      label: Text(chip.name,
                          style: const TextStyle(color: Colors.black)),
                      backgroundColor: Colors.white,
                      deleteIcon: const Icon(Icons.delete, color: Colors.blue),
                      onDeleted: () => _deleteChip(chip.id),
                      side: const BorderSide(color: Colors.black87, width: 0.5),
                    ),
                  ),
                  child: DragTarget<ChipModel>(
                    onWillAccept: (data) => data != chip,
                    onAccept: (data) {
                      setState(() {
                        final oldIndex = _chipList.indexOf(data);
                        final newIndex = _chipList.indexOf(chip);
                        _chipList.removeAt(oldIndex);
                        _chipList.insert(newIndex, data);
                      });
                      widget.onStoppagesUpdated(
                        _chipList.map((chip) => chip.name).toList(),
                      );
                    },
                    builder: (context, candidateData, rejectedData) {
                      return Chip(
                        label: Text(chip.name,
                            style: const TextStyle(color: Colors.black)),
                        backgroundColor: Colors.white,
                        deleteIcon:
                            const Icon(Icons.delete, color: Colors.blue),
                        onDeleted: () => _deleteChip(chip.id),
                        side:
                            const BorderSide(color: Colors.black87, width: 0.5),
                      );
                    },
                  ),
                  onDragStarted: () {
                    setState(() {
                      _draggedChip = chip;
                    });
                  },
                  onDragEnd: (details) {
                    setState(() {
                      _draggedChip = null;
                    });
                  },
                );
              },
            ).toList(),
          ),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _chipTextController,
                decoration: InputDecoration(
                  labelText: 'Add Stoppage',
                  hintText: 'Type a station name and press Enter or Space',
                  filled: true,
                  fillColor: Colors.blue.shade50,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.grey, width: 1),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Colors.blue, width: 1),
                  ),
                ),
                onSubmitted: (value) => _addChip(),
              ),
            ),
            const SizedBox(width: 10),
            Material(
              color: Colors.blueAccent,
              borderRadius: BorderRadius.circular(24),
              child: IconButton(
                icon: const Icon(Icons.add, color: Colors.white),
                onPressed: _addChip,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
