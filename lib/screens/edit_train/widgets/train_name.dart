
import 'package:flutter/material.dart';

class TrainNameField extends StatelessWidget {
  final TextEditingController controller;
  final bool enabled;
  const TrainNameField({Key? key, required this.controller,this.enabled = true,}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: 'Train Name',
          filled: true,
          fillColor: Colors.blue.shade50,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.blue, width: 1),
          ),
        ),
        textCapitalization: TextCapitalization.characters,
      ),
    );
  }
}
