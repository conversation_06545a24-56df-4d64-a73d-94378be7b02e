import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';

class CustomDropdownField extends StatelessWidget {
  final String? value;
  final List<String> items;
  final Function(String?) onChanged;
  final String labelText;
  final double right;
  final double left;
  final double top;
  final double bottom;
  final bool loading;
  final bool enabled;

  const CustomDropdownField({
    Key? key,
    required this.value,
    required this.items,
    required this.onChanged,
    required this.labelText,
    required this.right,
    required this.left,
    required this.top,
    required this.bottom,
    this.loading = false,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        right: right,
        left: left,
        top: top,
        bottom: bottom,
      ),
      child: DropdownSearch<String>(
        selectedItem: value,
        items: items,
        enabled: enabled,
        dropdownDecoratorProps: DropDownDecoratorProps(
          dropdownSearchDecoration: InputDecoration(
            labelText: labelText,
            border: const OutlineInputBorder(),
            enabled: enabled,
          ),
        ),
        dropdownButtonProps: DropdownButtonProps(
          icon: loading
              ? const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
              : const Icon(Icons.arrow_drop_down),
        ),
        onChanged: enabled ? onChanged : null,
        popupProps: const PopupProps.menu(
          showSearchBox: true,
          searchFieldProps: TextFieldProps(
            decoration: InputDecoration(
              hintText: "Search",
              border: OutlineInputBorder(),
            ),
          ),
        ),
      ),
    );
  }
}