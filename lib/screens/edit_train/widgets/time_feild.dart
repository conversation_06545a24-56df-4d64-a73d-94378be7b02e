import 'package:flutter/material.dart';

class TimeField extends StatelessWidget {
  final TextEditingController controller;
  final String labelText;
  final Function onTap;

  final bool enabled;


  const TimeField({
    Key? key,
    required this.controller,
    required this.labelText,
    required this.onTap,
  
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.only(right: 8.0),
        child: TextFormField(
          controller: controller,
          readOnly: true,
          decoration: InputDecoration(
            labelText: labelText,
            filled: true,
            fillColor: Colors.blue.shade50,
            contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue, width: 1),
            ),
          ),
          onTap: () => onTap(),
        ),
      ),
    );
  }
}
