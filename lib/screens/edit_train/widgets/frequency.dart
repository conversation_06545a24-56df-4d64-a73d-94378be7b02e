import 'package:flutter/material.dart';

class FrequencyField extends StatelessWidget {
  final List<int> frequency; // Store the frequency as a list of numbers
  final Function(int) onChanged;

  // Mapping day names to their corresponding number (1 = Monday, 7 = Sunday)
  static const Map<int, String> dayMapping = {
    1: 'Monday',
    2: 'Tuesday',
    3: 'Wednesday',
    4: 'Thursday',
    5: 'Friday',
    6: 'Saturday',
    7: 'Sunday',
  };

  static const Map<String, int> reverseDayMapping = {
    'Monday': 1,
    'Tuesday': 2,
    'Wednesday': 3,
    'Thursday': 4,
    'Friday': 5,
    'Saturday': 6,
    'Sunday': 7,
  };

  const FrequencyField({
    Key? key,
    required this.frequency,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Frequency',
              filled: true,
              fillColor: Colors.blue.shade50,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16.0),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey, width: 1),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.blue, width: 1),
              ),
            ),
            items: dayMapping.values
                .map((day) => DropdownMenuItem<String>(
                      value: day,
                      child: Text(day),
                    ))
                .toList(),
            onChanged: (value) {
              if (value != null && !frequency.contains(reverseDayMapping[value]!)) {
                onChanged(reverseDayMapping[value]!); // Pass the corresponding number
              }
            },
          ),
        ),
        Wrap(
          children: frequency.map((dayNumber) {
            final dayName = dayMapping[dayNumber]!; // Get the day name from the number
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
              child: Chip(
                label: Text(dayName, style: TextStyle(color: Colors.white)),
                backgroundColor: Colors.blue,
                side: BorderSide.none,
                deleteIcon: Icon(Icons.delete, color: Colors.white),
                onDeleted: () {
                  onChanged(dayNumber); // Remove day from frequency (pass the day number)
                },
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}
