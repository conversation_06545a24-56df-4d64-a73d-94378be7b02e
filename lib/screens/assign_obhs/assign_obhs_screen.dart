import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/status.dart';
import 'package:railops/screens/assign_obhs/widgets/assign_obhs_filters.dart';
import 'package:railops/screens/assign_obhs/widgets/assign_obhs_table.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/assign_ehk_ca_services/job_chart_status_services.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/assign_ehk_ca_types/job_chart_status_types.dart';
import 'package:railops/widgets/index.dart';

class AssignObhsScreen extends StatefulWidget {
  const AssignObhsScreen({Key? key}) : super(key: key);

  @override
  _AssignObhsScreenState createState() => _AssignObhsScreenState();
}

class _AssignObhsScreenState extends State<AssignObhsScreen> {
  bool displaySidebar = true;
  String? train;
  List<String> coaches = [];
  List<String> ehkUsers = [];
  List<String> obhsUsers = [];
  List<String> caUsers = [];
  Map<String, String>cashCarry1={};
  Map<String, String>cashCarry2={};
  String? date;
  bool showLoader = false;
  bool msgModalFlag = false;
  String message = '';
  String modalType = '';
  Map<String, dynamic>? upTrainData;
  Map<String, dynamic>? downTrainData;
  bool hasAccess = false;
  String loaderText = 'Loading...'; 
  List<String> selectedEhk1 = [];
  List<String> selectedEhk2 = [];
  Map<String, List<String>> selectedUsers1 = {};
  Map<String, List<String>> selectedUsers2 = {};

  Map<String, List<String>> upPendingDeletions = {};
  Map<String, List<String>> downPendingDeletions = {};

  final GlobalKey<State<AssignObhsTable>> upTableKey =
      GlobalKey<State<AssignObhsTable>>();
  final GlobalKey<State<AssignObhsTable>> downTableKey =
      GlobalKey<State<AssignObhsTable>>();

  void toggleSideBar() {
    setState(() {
      displaySidebar = !displaySidebar;
    });
  }

  void onSubmit(String trainNumber, String selectedDate) {
    setState(() {
      train = trainNumber;
      date = selectedDate;
      showLoader = true;
      loaderText = 'Loading train data...';
    });

    fetchSpecificUsers("EHK", trainNumber).then((_) {
      return fetchTrainsCoachWise(trainNumber, selectedDate);
    }).then((_) {
      setState(() {
        showLoader = false;
      });
      clearAllTablePendingDeletions();
    }).catchError((error) {
      setState(() {
        showLoader = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading data: ${error.toString()}')),
      );
    });
  }

  void clearSeletections() {
    setState(() {
      selectedEhk1 = [];
      selectedEhk2 = [];
      selectedUsers1 = {};
      selectedUsers2 = {};
      upPendingDeletions = {};
      downPendingDeletions = {};
    });
  }

  void clearSeletectionsUp() {
    setState(() {
      selectedEhk1 = [];
      selectedUsers1 = {};
      upPendingDeletions = {};
    });
  }

  void clearSeletectionsDown() {
    setState(() {
      selectedEhk2 = [];
      selectedUsers2 = {};
      downPendingDeletions = {};
    });
  }

  
  Future<void> fetchCoaches(String? trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading coaches...';
      });
      
      try {
        final response = await AdminAssignService.fetchCoaches(trainNumber);
        setState(() {
          coaches = response;
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading coaches: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> fetchTrainsCoachWise(
      String trainNumber, String selectedDate) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    const forUserType = "OBHS";
    if (token.isNotEmpty) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading train data...';
      });
      
      try {
        final response = await AdminAssignService.fetchTrainsCoachWise(
            trainNumber, selectedDate, forUserType, token);
        setState(() {
          upTrainData = response['out'];
          downTrainData = response['in'];
          hasAccess = response['has_access'];
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading train data: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> fetchSpecificUsers(String userType, String trainNumber) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    if (token.isNotEmpty) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading users...';
      });
      
      try {
        final response =
            await AdminAssignService.fetchSpecificUsersDivision(userType, token, trainNumber);

        setState(() {
          if (userType == "EHK") {
            ehkUsers = response.map<String>((ehk) => ehk["username"]).toList();
          } else if (userType == "OBHS") {
            obhsUsers =
                response.map<String>((obhs) => obhs["username"]).toSet().toList();
          }
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading ${userType} users: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  // Future<void> addTrainsCoachWise(
  //     Map<String, List<String>> data,
  //     Map<String, List<String>> dataEhk,
  //     String trainNumber,
  //     String dateParam) async {
  //   final userModel = Provider.of<UserModel>(context, listen: false);
  //   final token = userModel.token;
  //   const forUserType = "OBHS";
  //   setState(() => showLoader = true);

  //   final response = await AdminAssignService.addTrainsCoachWise(
  //       data, dataEhk, trainNumber, dateParam, forUserType, token);
  //   setState(() {
  //     message = response["message"];
  //     msgModalFlag = true;
  //     showLoader = false;
  //   });

  //   showModal(message);
  //   onSubmit(train!, date!);
  // }

  Future<void> addTrainsCoachWise(
      Map<String, dynamic> data,
      Map<String, dynamic> dataEhk,
      String trainNumber,
      String dateParam) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    const forUserType = "OBHS";
    setState(() => showLoader = true);

    for (var entry in upPendingDeletions.entries) {
      final keyParts =
          entry.key.split('-'); // Split key into username and userType
      final username = keyParts[0];
      final userType = keyParts[1];

      await removeTrainDetailsOriginDate(
        username,
        entry.value,
        upTrainData!['train_no'],
        upTrainData!['date'],
        userType,
      );
    }

    for (var entry in downPendingDeletions.entries) {
      final keyParts =
          entry.key.split('-'); // Split key into username and userType
      final username = keyParts[0];
      final userType = keyParts[1];

      await removeTrainDetailsOriginDate(
        username,
        entry.value,
        downTrainData!['train_no'].toString(),
        downTrainData!['date'],
        userType,
      );
    }

    final response = await AdminAssignService.addTrainsCoachWise(
        data, dataEhk, trainNumber, dateParam, forUserType, token);
    setState(() {
      message = response["message"];
      msgModalFlag = true;
      showLoader = false;
    });

    showModal(message);
    onSubmit(train!, date!);
  }

  Future<void> removeTrainDetailsOriginDate(
      String username,
      List<String> coachNumbers,
      String trainNumber,
      String dateParam,
      String forUserType) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    setState(() => showLoader = true);
    await AdminAssignService.removeTrainDetailsOriginDate(
        username, coachNumbers, trainNumber, dateParam, forUserType, token);
    setState(() => showLoader = false);
    onSubmit(train!, date!);
  }

  Future<String> fetchLastJourneyDate(String username) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    final response =
        await AdminAssignService.fetchLastJourneyDate(username, token);
    return response;
  }

  void clearAllTablePendingDeletions() {
    // Access the public method through the widget instance
    (upTableKey.currentWidget as AssignObhsTable?)?.clearPendingDeletions();
    (downTableKey.currentWidget as AssignObhsTable?)?.clearPendingDeletions();
  }

  @override
  void initState() {
    super.initState();
    fetchCoaches(train);
    // fetchSpecificUsers("EHK");
    // fetchSpecificUsers("OBHS");
  }

  void _handleUpchange(String type, dynamic value,cashCarrybyUser) {
    
    if (type == 'ehk-user') {
      if (downTrainData == null || downTrainData!['ehk_dict'] == null) {
        downTrainData!['ehk_dict'] = {};
      }
      bool hasRightCellValueEhk = downTrainData!['ehk_dict'].isNotEmpty &&
          downTrainData!['ehk_dict']
              .entries!
              .any((entry) => entry.value == downTrainData!['date']);

      setState(() {
        selectedEhk1 = List<String>.from(value);

        if (!hasRightCellValueEhk) {
          selectedEhk2 = List<String>.from(value);
        }
      });
    } else if (type.startsWith('obhs-user-')) {
      // print("In OBHS Block");
      // print('Type is : $type and Values are : $value');
      final coach = type.replaceFirst('obhs-user-', '');
      setState(() {
        selectedUsers1[coach] = List<String>.from(value);
        selectedUsers2[coach] = List<String>.from(value);
        cashCarry1[coach] = cashCarrybyUser[coach] ?? "0";
        cashCarry2[coach] = cashCarrybyUser[coach] ?? "0";
        // bool hasRightCellValue = downTrainData!['coach_wise_dict']
        //         .containsKey(coach) &&
        //     downTrainData!['coach_wise_dict'][coach]!
        //         .any((user) => user['origin_date'] == downTrainData!['date']);

        // if (!hasRightCellValue) {
        //   selectedUsers2[coach] = List<String>.from(value);
        // }
      });
    }
  }

  void _handleDownchange(String type, dynamic value,cashCarrybyUser) {
  if (type == 'ehk-user') {
    // Check if up table's EHK has existing entries for the current date
    
    if (upTrainData == null || upTrainData!['ehk_dict'] == null) {
      upTrainData!['ehk_dict'] = {};
    }
    bool hasRightCellValueEhkUp = upTrainData!['ehk_dict'].isNotEmpty &&
        upTrainData!['ehk_dict'].entries.any(
            (entry) => entry.value['origin_date'] == upTrainData!['date']);

    setState(() {
      selectedEhk2 = List<String>.from(value);
      // Propagate to up table if no existing EHK
      if (!hasRightCellValueEhkUp) {
        selectedEhk1 = List<String>.from(value);
      }
    });
  } 
  else if (type.startsWith('obhs-user-')) {
    final coach = type.replaceFirst('obhs-user-', '');
    // Check if up table's coach has existing OBHS entries
    bool hasRightCellValueUp=true;
    if (upTrainData!['coach_wise_dict'] != null) {
       hasRightCellValueUp = upTrainData!['coach_wise_dict'].containsKey(coach) &&
          upTrainData!['coach_wise_dict'][coach]!.any(
              (user) => user['origin_date'] == upTrainData!['date']);
    }
    setState(() {
      selectedUsers2[coach] = List<String>.from(value);
      cashCarry2[coach] = cashCarrybyUser[coach] ?? "0";
      // Propagate to up table if no existing OBHS for the coach
      if (!hasRightCellValueUp) {
        selectedUsers1[coach] = List<String>.from(value);
      }
    });
  }
}

  Future<void> _reloadPage() async {
    try {
      if (train != null) {
        await fetchTrainsCoachWise(train!, date!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Data refreshed successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Refresh failed: ${e.toString()}')),
      );
    } finally {
      await fetchTrainsCoachWise(train!, date!);
    }
  }

  void handlePendingDeletionsChanged(
    Map<String, List<String>> deletions, bool isUp) {
  print('screen deletions: ${deletions.keys}');
  setState(() {
    if (isUp) {
      upPendingDeletions = deletions;
      if (deletions.isNotEmpty && upTrainData != null && upTrainData!['ehk_dict'] != null) {
        final String firstKey = deletions.keys.first;
        if (firstKey.contains('-EHK')) {
          final String username = firstKey.split('-EHK')[0];
          upTrainData!['ehk_dict'].forEach((key, value) {
            print('up train data: $key $username ${key == username}');
            if(key == username) {
              upTrainData!['ehk_dict'] = <String, dynamic>{};
            }
          });
        }
      }
    } else {
      downPendingDeletions = deletions;
      
      if (deletions.isNotEmpty && downTrainData != null && downTrainData!['ehk_dict'] != null) {
        final String firstKey = deletions.keys.first;
        if (firstKey.contains('-EHK')) {
          final String username = firstKey.split('-EHK')[0];
          downTrainData!['ehk_dict'].forEach((key, value) {
            if(key == username) {
              downTrainData!['ehk_dict'] = <String, dynamic>{};
            }
          });
        }
      }
    }
  });
}

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  Future<void> submitJobChartStatus(
      trainNumber, originDate, statusFor, selectedStatus, token) async {
    setState(() => showLoader = true);

    setState(() {
      showLoader = true;
      loaderText = 'Updating job chart status...';
    });

    final request = JobChartStatusRequest(
      trainNumber: trainNumber,
      date: originDate,
      statusFor: statusFor,
      status: selectedStatus,
      token: token,
    );

    try {
      await JobChartStatusService.addJobChartStatus(request);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Job Chart Status Added')));
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Error: $e')));
    } finally {
      setState(() => showLoader = false);
    }
    fetchTrainsCoachWise(train!, date!);
  }

  @override
  Widget build(BuildContext context) {
    return  
      Scaffold(
        appBar: const CustomAppBar(title: 'Assign Multiple Users'),
        drawer: const CustomDrawer(),
        body: Stack(
          children: [
            RefreshIndicator(
            onRefresh: _reloadPage,
            child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: Container(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context)
                    .size
                    .height, // Ensures it fills screen height
              ),
              child: Stack(
                children: [
            Padding(
              padding: const EdgeInsets.all(1),
                child: Column(
                  children: [
                    const SizedBox(height: 10),
                    const Text(
                      'Assign OBHS',
                      style: TextStyle(fontSize: 18.0),
                      textAlign: TextAlign.center,
                    ),
                    AssignObhsFilters(
                      onSubmit: (trainNumber, selectedDate) {
                        onSubmit(trainNumber!, selectedDate!);
                        clearSeletections();
                      },
                    ),
                    // const SizedBox(height: 5),
                    if (upTrainData != null && upTrainData!['coaches'] != null)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              minimumSize: const Size(150, 40),
                              side: const BorderSide(
                                  color: Colors.black87, width: 0.5),
                            ),
                            onPressed: () async {
                              _ImageUpload(
                                  context,
                                  JobChartImageUploadPage(
                                      trainNumber: upTrainData!['train_no']!,
                                      journeyDate: upTrainData!['date']!,
                                      uploadedFor: 'obhs',
                                      status: upTrainData!['status']));
                            },
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  "Upload Job Chart Image",
                                  style: TextStyle(
                                    fontSize: 15.0,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(width: 5.0),
                                if (upTrainData!["image_uploaded"] == true)
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.image,
                                        color: Colors.blue,
                                        size: 18.0,
                                      ),
                                      Icon(
                                        Icons.check,
                                        color: Colors.blue,
                                        size: 18.0,
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    const SizedBox(height: 10),
                    if (upTrainData != null && upTrainData!['coaches'] != null)
                      AssignObhsTable(
                          key: upTableKey,
                          coaches: List<String>.from(upTrainData!['coaches']),
                          ehkUsers: [...ehkUsers, ...obhsUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          obhsUsers: [...ehkUsers, ...obhsUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          coachWiseDict: upTrainData!['coach_wise_dict']
                              .cast<String, dynamic>(),
                          ehkDict: upTrainData!['ehk_dict'],
                          date: upTrainData!['date'],
                          train: upTrainData!['train_no'],
                          down: false,
                          hasAccess:
                              hasAccess && upTrainData!['status'] == "pending",
                          onSubmit: addTrainsCoachWise,
                          selectedUsers: selectedUsers1,
                          setSelectedUsers: (value) =>
                              setState(() => selectedUsers1 = value),
                          selectedEhk: selectedEhk1,
                          setSelectedEhk: (value) =>
                              setState(() => selectedEhk1 = value),
                          fetchLastJourneyDate: fetchLastJourneyDate,
                          removeTrainDetailsOriginDate:
                              removeTrainDetailsOriginDate,
                          onPendingDeletionsChanged: (deletions) =>
                              handlePendingDeletionsChanged(deletions, true),
                          handleUpchange: _handleUpchange,
                          updateBothTablesVisible: (upTrainData != null &&
                              upTrainData!['coaches'] != null &&
                              downTrainData != null &&
                              downTrainData!['coaches'] != null &&
                              hasAccess),
                          clearSeletection: clearSeletectionsUp,
                          status: upTrainData!['status']),

                    if (upTrainData != null && upTrainData!['coaches'] != null)
                      JobChartStatusWidget(
                          trainNumber: upTrainData!['train_no'],
                          originDate: upTrainData!['date'],
                          statusFor: "obhs",
                          defaultStatus: upTrainData!['status'],
                          submitJobChartStatus: submitJobChartStatus),

                    if (downTrainData != null &&
                        downTrainData!['coaches'] != null)
                      AssignObhsTable(
                          key: downTableKey,
                          coaches: List<String>.from(downTrainData!['coaches']),
                          ehkUsers: [...ehkUsers, ...obhsUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          obhsUsers: [...ehkUsers, ...obhsUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          coachWiseDict: downTrainData!['coach_wise_dict'],
                          ehkDict: downTrainData!['ehk_dict'],
                          date: downTrainData!['date'],
                          train: downTrainData!['train_no'].toString(),
                          down: true,
                          hasAccess: hasAccess &&
                              downTrainData!['status'] == "pending",
                          onSubmit: addTrainsCoachWise,
                          selectedUsers: selectedUsers2,
                          setSelectedUsers: (value) =>
                              setState(() => selectedUsers2 = value),
                          selectedEhk: selectedEhk2,
                          setSelectedEhk: (value) =>
                              setState(() => selectedEhk2 = value),
                          fetchLastJourneyDate: fetchLastJourneyDate,
                          removeTrainDetailsOriginDate:
                              removeTrainDetailsOriginDate,
                          onPendingDeletionsChanged: (deletions) =>
                              handlePendingDeletionsChanged(deletions, false),
                          // handleUpchange: (String type, dynamic value) {
                          //   if (type == 'ehk-user') {
                          //     setState(() {
                          //       selectedEhk2 = List<String>.from(value);
                          //     });
                          //   } else if (type.startsWith('obhs-user-')) {
                          //     final coach = type.replaceFirst('obhs-user-', '');
                          //     setState(() {
                          //       selectedUsers2[coach] =
                          //           List<String>.from(value);
                          //     });
                          //   }
                          // },
                          handleUpchange:_handleDownchange,
                          updateBothTablesVisible: (upTrainData != null &&
                              upTrainData!['coaches'] != null &&
                              downTrainData != null &&
                              downTrainData!['coaches'] != null &&
                              hasAccess),
                          clearSeletection: clearSeletectionsDown,
                          status: downTrainData!['status']),

                    if (downTrainData != null &&
                        downTrainData!['coaches'] != null)
                      JobChartStatusWidget(
                          trainNumber: downTrainData!['train_no'].toString(),
                          originDate: downTrainData!['date'],
                          statusFor: "obhs",
                          defaultStatus: downTrainData!['status'],
                          submitJobChartStatus: submitJobChartStatus),

                    if (upTrainData != null &&
                        upTrainData!['coaches'] != null &&
                        downTrainData != null &&
                        downTrainData!['coaches'] != null &&
                        hasAccess)
                      Center(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: ElevatedButton(
                            onPressed: () async {
                              try {
                                await submitTwoForms();
                              } catch (e) {
                                print("Error submitting forms: $e");
                              } finally {}
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.black87,
                              backgroundColor: Colors.white,
                              side:
                                  BorderSide(color: Colors.black87, width: 0.5),
                            ),
                            child: const Text(
                              'Update Both Trains',
                              style: TextStyle(
                                fontSize: 16.0,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ])))),
          
          if (showLoader)
            Positioned.fill(
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                color: Colors.black.withOpacity(0.5),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children:  [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          loaderText,
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    ]));
  }

  Future<void> submitTwoForms() async {
    setState(() {
      showLoader = true;
      loaderText = 'Update Job Chart...';
    });
    String combinedMessage = '';

    try {
      for (var entry in upPendingDeletions.entries) {
        final keyParts =
            entry.key.split('-');
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          upTrainData!['train_no'],
          upTrainData!['date'],
          userType,
        );
      }

      for (var entry in downPendingDeletions.entries) {
        final keyParts =
            entry.key.split('-'); // Split key into username and userType
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          downTrainData!['train_no'].toString(),
          downTrainData!['date'],
          userType,
        );
      }

      setState(() {
        upPendingDeletions = {};
        downPendingDeletions = {};
      });

      if ((upTrainData != null && upTrainData!['coaches'] != null) &&
          (downTrainData != null && downTrainData!['coaches'] != null)) {
        final data1 = transformData(
            selectedUsers1, List<String>.from(upTrainData!['coaches']),cashCarry1);
        final Map<String, dynamic> dataEhk1 = {};
        final upAmountData = (upTableKey.currentState as AssignObhsTableState).amountInHand;

        for (final ehk in selectedEhk1) {
          dataEhk1[ehk] = {
            "values":List<String>.from(upTrainData!['coaches']),
            "cash_carry": upAmountData[ehk] ?? 0.0,
        };
        }
        
        upTrainData!['ehk_dict'].forEach((key, value) {
          if (value['origin_date'] == upTrainData!['date']) {
            dataEhk1[key] = {
              "values": List<String>.from(upTrainData!['coaches']),
              "cash_carry": upAmountData[key] ?? 0.0,
            };
          }
        });


        final data2 = transformData(
            selectedUsers2, List<String>.from(downTrainData!['coaches']),cashCarry2);
        final Map<String, dynamic> dataEhk2 = {};
        final downAmountData = (downTableKey.currentState as AssignObhsTableState).amountInHand;

        for (final ehk in selectedEhk2) {
          dataEhk2[ehk] = {
            "values":List<String>.from(downTrainData!['coaches']),
            "cash_carry": 0.0,
          };
        }

        downTrainData!['ehk_dict'].forEach((key, value) {
          if (value['origin_date'] == downTrainData!['date']) {
            dataEhk2[key] = {
              "values": List<String>.from(downTrainData!['coaches']),
              "cash_carry": downAmountData[key] ?? 0.0,
            };
          }
        });
        final response = await AdminAssignService.addBothTrainsCoachWise(
          data1,
          dataEhk1,
          upTrainData!['train_no'].toString(),
          upTrainData!['date'],
          data2,
          dataEhk2,
          downTrainData!['train_no'].toString(),
          downTrainData!['date'],
          "OBHS",
          Provider.of<UserModel>(context, listen: false).token,
        );

        combinedMessage += "${response["message"]}";
      } else {
        if (upTrainData != null && upTrainData!['coaches'] != null) {
          final data1 = transformData(
              selectedUsers1, List<String>.from(upTrainData!['coaches']),cashCarry1);
          final Map<String, List<String>> dataEhk1 = {};

          for (final ehk in selectedEhk1) {
            dataEhk1[ehk] = List<String>.from(upTrainData!['coaches']);
          }

          final response1 = await AdminAssignService.addTrainsCoachWise(
            data1,
            dataEhk1,
            upTrainData!['train_no'],
            upTrainData!['date'],
            "OBHS",
            Provider.of<UserModel>(context, listen: false).token,
          );

          combinedMessage +=
              "${upTrainData!['train_no']}: " + response1["message"];
        }

        if (downTrainData != null && downTrainData!['coaches'] != null) {
          final data2 = transformData(
              selectedUsers2, List<String>.from(downTrainData!['coaches']),cashCarry2);
          final Map<String, List<String>> dataEhk2 = {};

          for (final ehk in selectedEhk2) {
            dataEhk2[ehk] = List<String>.from(downTrainData!['coaches']);
          }

          final response2 = await AdminAssignService.addTrainsCoachWise(
            data2,
            dataEhk2,
            downTrainData!['train_no'].toString(),
            downTrainData!['date'],
            "OBHS",
            Provider.of<UserModel>(context, listen: false).token,
          );

          if (combinedMessage.isNotEmpty) {
            combinedMessage += '\n\n';
          }
          combinedMessage +=
              "${downTrainData!['train_no']}: " + response2["message"];
        }
      }
      // Show combined message
      setState(() {
        message = combinedMessage;
        msgModalFlag = true;
      });

      showModal(message);
      clearSeletections();

    } catch (e) {
      setState(() {
        message = 'An error occurred while updating the trains: $e';
        msgModalFlag = true;
      });
      showModal(message);
      print("Error submitting forms: $e");
    } finally {
      setState(() {
        showLoader = false;
        loaderText = '';
      });

      // Refresh data
      if (train != null && date != null) {
        onSubmit(train!, date!);
      }
    }
  }

  Map<String, dynamic> transformData(
    Map<String, List<String>> data,
    coaches,
    Map<String, String> cashCarry) {
  print("In Transform Data: $data");
  final userToCoaches = <String, dynamic>{};

  data.forEach((coach, users) {
    
    for (var user in users) {
      if (!userToCoaches.containsKey(user)) {
        userToCoaches[user] = {}; // ✅ Initialize map

        if (cashCarry != null && cashCarry.containsKey(user)) {
          userToCoaches[user]["cash_carry"] = cashCarry[user];
          userToCoaches[user]["values"] = coaches;
        }
        else{
          userToCoaches[user]["cash_carry"] = 0;
          userToCoaches[user]["values"] = coaches;
        }
      } 
    }
  });

  return userToCoaches;
}


  void showModal(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Message'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void _ImageUpload(BuildContext context, Widget page) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => page),
    );

    if (train != null && date != null) {
      onSubmit(train!, date!);
    }
  }
}