import 'package:flutter/material.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:railops/types/profile_types/add_trains_response.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/widgets/bottom_modal_widget.dart';
import 'package:railops/widgets/call_widget.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:intl/intl.dart';
class AssignObhsTable extends StatefulWidget {
  final List<String> coaches;
  final List<String> obhsUsers;
  final List<String> ehkUsers;
  final Map<String, dynamic> coachWiseDict;
  final Map<String, dynamic> ehkDict;
  final String date;
  final String train;
  final bool down;
  final bool hasAccess;
  final bool updateBothTablesVisible;
  final Map<String, List<String>> selectedUsers;
  final List<String> selectedEhk;
  final Function(Map<String, dynamic>, Map<String, dynamic>, String, String)
      onSubmit;
  final Function(String) fetchLastJourneyDate;
  final Function(String, List<String>, String, String, String)
      removeTrainDetailsOriginDate;
  final Function(String, List<String>,Map<String, String>) handleUpchange;
  final Function() clearSeletection;
  final Function(Map<String, List<String>>) onPendingDeletionsChanged;
  void clearPendingDeletions() {
    // Find the current state using GlobalKey
    final state = key as GlobalKey<State<AssignObhsTable>>;
    (state.currentState as dynamic)?.clearTablePendingDeletions();
  }

  final String status;

  const AssignObhsTable(
      {required this.coaches,
      required this.obhsUsers,
      required this.ehkUsers,
      required this.coachWiseDict,
      required this.ehkDict,
      required this.date,
      required this.train,
      required this.down,
      required this.hasAccess,
      required this.onSubmit,
      required this.fetchLastJourneyDate,
      required this.updateBothTablesVisible,
      required this.removeTrainDetailsOriginDate,
      required this.handleUpchange,
      Key? key,
      required this.selectedUsers,
      required this.selectedEhk,
      required void Function(dynamic value) setSelectedUsers,
      required void Function(dynamic value) setSelectedEhk,
      required this.clearSeletection,
      required this.onPendingDeletionsChanged,
      required this.status})
      : super(key: key);

  @override
  AssignObhsTableState createState() => AssignObhsTableState();
}

class UserCash {
  final String username;
  final String cash;

  UserCash(this.username, this.cash);
}

class AssignObhsTableState extends State<AssignObhsTable> {
  Map<String, List<String>> selectedUsers = {};
  List<UserCash>? distinctUsernames;
  List<String>? updatedOBHSUsers;
  List<String> selectedEhk = [];
  Map<String, String> lastJourneyDetails = {};
  bool showMsgPopup = false;
  String message = '';
  String modalType = '';
  final Map<String, String> _amountInHand = {};
  Map<String, String> get amountInHand => _amountInHand;

  

  Map<String, List<String>> pendingDeletions = {};

  bool get showCol {
    bool ehkHasValue = widget.ehkDict.entries
        .any((entry) => entry.value['origin_date'] == widget.date);
    return !ehkHasValue;
  }

  void clearTablePendingDeletions() {
    setState(() {
      pendingDeletions = {};
      widget.onPendingDeletionsChanged(pendingDeletions);
    });
  }

  @override
  void initState() {
    super.initState();
    updatedOBHSUsers = widget.obhsUsers;
    distinctUsernames = widget.coachWiseDict.values
        .expand((coachList) => coachList)
        .map((entry) => UserCash(
              entry["user"] ?? "",
              entry["cash_carry"].toString(),
            ))
        .where(
      (() {
        final seen = <String>{};
        return (userCash) => seen.add(userCash.username);
      })(),
    ).toList();
    // updatedOBHSUsers!.removeWhere((user) => distinctUsernames!.contains(user));
    _initializeSelectedUsers();
    List<String> selectedUsernames =
        selectedUsers.values.expand((userList) => userList).toList();
    List<String> usernamesToRemove = [
      ...distinctUsernames?.map((e) => e.username) ?? [],
      ...selectedUsernames
    ];
    updatedOBHSUsers?.removeWhere((user) => usernamesToRemove.contains(user));
    updatedOBHSUsers
        ?.sort((a, b) => a.toUpperCase().compareTo(b.toUpperCase()));
    distinctUsernames?.sort(
      (a, b) => a.username.toUpperCase().compareTo(b.username.toUpperCase()),
    );
  }

  @override
  void didUpdateWidget(AssignObhsTable oldWidget) {
    super.didUpdateWidget(oldWidget);
    updatedOBHSUsers = widget.obhsUsers;
    distinctUsernames = widget.coachWiseDict.values
        .expand((coachList) => coachList)
        .map((entry) => UserCash(
              entry["user"] ?? "",
              entry["cash_carry"].toString(),
            ))
        .where(
      (() {
        final seen = <String>{};
        return (userCash) => seen.add(userCash.username);
      })(),
    ).toList();
    // updatedOBHSUsers!.removeWhere((user) => distinctUsernames!.contains(user));
    _initializeSelectedUsers();

    List<String> selectedUsernames =
        selectedUsers.values.expand((userList) => userList).toList();
    List<String> usernamesToRemove = [
      ...distinctUsernames?.map((e) => e.username) ?? [],
      ...selectedUsernames
    ];
    updatedOBHSUsers?.removeWhere((user) => usernamesToRemove.contains(user));
    updatedOBHSUsers
        ?.sort((a, b) => a.toUpperCase().compareTo(b.toUpperCase()));
    distinctUsernames?.sort(
      (a, b) => a.username.toUpperCase().compareTo(b.username.toUpperCase()),
    );
  }

  void _initializeSelectedUsers() {
    setState(() {
      selectedUsers = widget.selectedUsers;
      selectedEhk = widget.selectedEhk;
      for (var entry in selectedUsers.entries) {
        List<String> usernames = entry.value;
        for (var username in usernames) {
          _fetchLastJourneyByUserName(username);
        }
      }

      for (var entry in widget.ehkDict.entries) {
        if (entry.value['origin_date'] == widget.date &&
            !_amountInHand.containsKey(entry.key)) {
          _amountInHand[entry.key] = entry.value['cash_carry'].toString() ?? "0";
        }
      }

      for (var entry in widget.coachWiseDict.entries) {
        for (var user in entry.value) {
          if (user['user'] != null && user['cash_carry'] != null) {
            _amountInHand.putIfAbsent(user['user'], () => user['cash_carry'].toString());
          }
        }
      }
       
    });
  }

  String _extractNumber(String input) {
    if (input.contains('_')) {
      return input.split('_').last; // Extract the number after the '_'
    }
    return input; // Return the input if no '_' is found
  }

  void _launchPhoneDialer(String phoneNumber) async {
    final countryCode = '+91';
    final formattedNumber = '$countryCode$phoneNumber';
    final url = 'tel:$formattedNumber';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not open the dialer';
    }
  }

  bool _isMarkedForDeletion(
      String username, String forUserType, List<String> coaches) {
    final key = '$username-$forUserType';
    return pendingDeletions.containsKey(key) &&
        coaches
            .every((coach) => pendingDeletions[key]?.contains(coach) ?? false);
  }

  void _unmarkForDeletion(
      String username, String forUserType, List<String> coaches) {
    setState(() {
      final key = '$username-$forUserType';
      if (pendingDeletions.containsKey(key)) {
        final existingCoaches = pendingDeletions[key] ?? [];
        final updatedCoaches =
            existingCoaches.where((c) => !coaches.contains(c)).toList();
        if (updatedCoaches.isEmpty) {
          pendingDeletions.remove(key);
        } else {
          pendingDeletions[key] = updatedCoaches;
        }
        for (var coach in coaches) {
          selectedUsers.remove(coach);
        }
        widget.onPendingDeletionsChanged(pendingDeletions);
      }
    });
  }

  void openColorinfoDialog(
      Map<String, dynamic> user, String username, String lastLoginDate) {
    String message;

    String lastLoginData =
        "Last logged out at: ${user["last_logout_date_time"] ?? "N/A"}\n"
        "Last login at: ${user["last_login_date_time"] ?? "N/A"}\n"
        "Last user Location fetched at: ${user["last_user_location"] ?? "N/A"}\n"
        "Last location fetched on :${user["last_user_location_time"] ?? "N/A"} \n"
        "Last Location Fetched for train: ${user["last_location_fetched_for_train"] ?? "N/A"}";

    if (!user['isEmail']) {
      message = "Has registered with only phone number";
    } else if (user['isEmail'] && user['location_fetched_within_hr']) {
      message = "Has email id and phone number both\nlocation fetched an hour.";
    } else if (user['isEmail'] && user['isLoginBeforeTrainDate']) {
      message =
          "Has email id and phone number both\nRecently logged in after getting allocated for this journey.";
    } else if (user['isEmail'] && user['isLoginBefore']) {
      message =
          "Ha email id and phone number both\nAnd logged in any time before journey.";
    } else {
      message = "Entered his email id and phone number both.";
    }
    final msg = 'User : $username\n$message\n$lastLoginData';
    bottomModalWidget(context, msg);
  }

  void _markForDeletion(
      String username, String forUserType, List<String> coaches) {
    // print('coaches are: $coaches');
    print('deleted $username');
    setState(() {
      final key = '$username-$forUserType';
      if (!pendingDeletions.containsKey(key)) {
        pendingDeletions[key] = coaches;
      } else {
        final existingCoaches = pendingDeletions[key] ?? [];
        final updatedCoaches = {...existingCoaches, ...coaches}.toList();
        pendingDeletions[key] = updatedCoaches;
      }
      widget.onPendingDeletionsChanged(pendingDeletions);
    });
  }

  Future<void> _fetchLastJourneyByUserName(String value) async {
    if (!lastJourneyDetails.containsKey(value)) {
      try {
        final result = await widget.fetchLastJourneyDate(value);
        setState(() {
          lastJourneyDetails[value] = result;
        });
      } catch (error) {
        print('Error fetching journey date for $value: $error');
      }
    }
  }

  Color getUserColor(Map<String, dynamic> user, isMarkedForDeletion) {
    // print( user['location_fetched_within_hr']);
    if (isMarkedForDeletion) {
      return Colors.grey.shade300;
    }
    if (!user['isEmail']) {
      return Colors.red.shade100;
    } else if (user['isEmail'] && user['location_fetched_within_hr']) {
      return Colors.green.shade900;
    } else if (user['isEmail'] && user['isLoginBeforeTrainDate']) {
      return Colors.green;
    } else if (user['isEmail'] && user['isLoginBefore']) {
      return Colors.green.shade100;
    } else {
      return Colors.yellow.shade200;
    }
  }

  Future<void> _handleSelectChangeEhk(List<String> values,Map<String,String>cashCarry) async {
    for (var value in values) {
      _fetchLastJourneyByUserName(value);
    }

    // print(values);
    setState(() {
      selectedEhk = values;
    });

    widget.handleUpchange('ehk-user', values,cashCarry);
  }

  Future<void> _handleSelectChange(String coach, List<String> values,final Map<String, String>cashCarry) async {
    for (var value in values) {
      _fetchLastJourneyByUserName(value);
      // updatedOBHSUsers!.removeWhere((user) => user == value);
    }
    setState(() {
      selectedUsers[coach] = values;
    });
    widget.handleUpchange('obhs-user-$coach', values, cashCarry);
  }

  void _handleSubmit() {
    final data = _transformData(selectedUsers);
    final Map<String, dynamic> dataEhk = {};

    // Handle EHK users - make sure we process all selected EHK users
    if (selectedEhk.isNotEmpty) {
      for (var ehk in selectedEhk) {
        dataEhk[ehk] = {
          "values": widget.coaches.toList(),
          "cash_carry": _amountInHand[ehk].toString() ?? "0",
        };
      }
    }

    // Also include any existing EHK users from the ehkDict
    widget.ehkDict.entries.forEach((entry) {
      if (entry.value['origin_date'] == widget.date) {
        // Only include if not marked for deletion
        final isMarkedForDeletion =
            _isMarkedForDeletion(entry.key, "EHK", widget.coaches);
        if (!isMarkedForDeletion && !dataEhk.containsKey(entry.key)) {
          dataEhk[entry.key] = {
            "values": widget.coaches.toList(),
            "cash_carry": _amountInHand[entry.key].toString() ?? "0",
          };
        }
      }
    });

    widget.onSubmit(data, dataEhk, widget.train, widget.date);
    widget.clearSeletection();
  }

  void _showAmountUpdateDialog(String userId, String currentAmount,String userType) {
    final TextEditingController amountController = TextEditingController(
      text: currentAmount.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Amount for $userId'),
        content: TextField(
          controller: amountController,
          keyboardType: TextInputType.numberWithOptions(decimal: true),
          decoration: const InputDecoration(
            labelText: 'Amount in Hand (₹)',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final newAmount = amountController.text ?? "0";
              setState(() {
                _amountInHand[userId] = newAmount.toString();
                _amountInHand[userId] = newAmount.toString();
                print('Updated amount for $userId: $newAmount');
                if(userType == "OBHS"){
                 widget.handleUpchange(
                    'obhs-user-$userId', [userId], _amountInHand);
                }
                else{
                  widget.handleUpchange(
                    'ehk-user-$userId', [userId], _amountInHand);
                }
                
              });
              Navigator.pop(context);
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic> _transformData(Map<String, List<String>> data) {
    final userToCoaches = <String, dynamic>{};
    data.forEach((coach, users) {

      for (var user in users) {
        if (!userToCoaches.containsKey(user)) {
          userToCoaches[user] = {
            "values": [],
            "cash_carry": "0",
          };
        }
        for (var c in widget.coaches) {
          // userToCoaches[user]!.add(c);
          userToCoaches[user]["values"]!.add(c);
          userToCoaches[user]["cash_carry"] =
              _amountInHand[user].toString() ?? "0";
        }
      }
    });
    return userToCoaches;
  }

  @override
  Widget build(BuildContext context) {
    return widget.coaches.isNotEmpty
        ? SingleChildScrollView(
            // scrollDirection: Axis.horizontal,
            child: Padding(
              padding:
                  const EdgeInsets.all(8.0), // Adding margin around the content
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                        bottom: 8.0), // Adding margin below the text
                    child: Text(
                      'Train Number: ${widget.train} - ${widget.down ? "in" : "out"}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontSize: 16), // Decreasing font size slightly
                    ),
                  ),
                  widget.status == "completed"
                      ? Container(
                          padding: const EdgeInsets.all(8),
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          decoration: BoxDecoration(
                            color: const Color.fromARGB(255, 245, 190, 187),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            "This train is marked as completed, cannot be updated",
                            style: TextStyle(
                              color: Color.fromARGB(255, 237, 39, 39),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
                  SingleChildScrollView(
                    scrollDirection:
                        Axis.horizontal, // Enable horizontal scrolling
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(
                        minWidth: 270, // Set the minimum width
                      ),
                      child: DataTable(
                        border: TableBorder.all(
                          color: Colors.grey,
                          width: 1.0,
                          style: BorderStyle.solid,
                        ),
                        columns: [
                          const DataColumn(label: Text('')),
                          if (widget.hasAccess && showCol)
                            const DataColumn(label: Text('OBHS')),
                          DataColumn(
                            label: Column(
                              children: [
                                Text('Assigned'),
                                Text(
                                  '${DateFormat('dd-MMM-yyyy').format(DateTime.parse(widget.date))}'
                                  ' (${DateFormat('EEEE').format(DateTime.parse(widget.date))})',
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                          ),
                          DataColumn(label: Text('Amount')),
                        ],
                        rows: [
                          _buildEhkRow(),
                        ],
                        headingRowColor:
                            MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                            return Colors
                                .blue.shade100; // Header background color
                          },
                        ),
                        dataRowColor: MaterialStateProperty.resolveWith<Color>(
                          (Set<MaterialState> states) {
                            return states.contains(MaterialState.selected)
                                ? Colors.blue.shade50
                                : Colors.white; // Row background color
                          },
                        ),
                        columnSpacing: 20.0,
                        horizontalMargin: 10.0,
                        dividerThickness: 1.0,
                      ),
                    ),
                  ),
                  ConstrainedBox(
                    constraints: const BoxConstraints(
                      minWidth: 350, // Set the minimum width
                    ),
                    child: Wrap(
                      spacing: 3.0, // Horizontal spacing between cards
                      runSpacing: 16.0, // Vertical spacing between rows
                      children: List.generate(
                        10,
                        (index) {
                          if (index < distinctUsernames!.length &&
                              distinctUsernames![index] != null) {
                            return SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 12,
                              child: _buildCoachCard(
                                  distinctUsernames![index].username,
                                  index,
                                  distinctUsernames![index].cash),
                            );
                          } else {
                            return SizedBox(
                              width: MediaQuery.of(context).size.width / 2 - 12,
                              child: _buildCoachCard("NA", index, "NA"),
                            );
                          }
                        },
                      ),
                    ),
                  ),

                  if (widget.hasAccess && !widget.updateBothTablesVisible)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: ElevatedButton(
                        onPressed: _handleSubmit,
                        style: ElevatedButton.styleFrom(
                          foregroundColor: Colors.black,
                          backgroundColor: Colors.white,
                          side:
                              const BorderSide(color: Colors.black, width: 0.5),
                        ),
                        child: const Text(
                          'Update',
                          style: TextStyle(fontSize: 16.0),
                        ),
                      ),
                    ),

                  // if (widget.hasAccess)
                  // Padding(
                  //   padding: const EdgeInsets.only(top: 8.0),
                  //   child: ElevatedButton(
                  //     onPressed: _handleSubmit,
                  //     style: ElevatedButton.styleFrom(
                  //       foregroundColor: Colors.white,
                  //       backgroundColor: Colors.blue,
                  //     ),
                  //     child: const Text(
                  //       'Update',
                  //       style: TextStyle(fontSize: 16.0),
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ),
          )
        : const SizedBox();
  }

  DataRow _buildEhkRow() {
    bool hasRightCellValue = widget.ehkDict.isNotEmpty &&
        widget.ehkDict.entries!
            .any((entry) => entry.value['origin_date'] == widget.date);

    final ehkUsername = widget.ehkDict.entries.firstWhere(
      (entry) => entry.value['origin_date'] == widget.date,
      orElse: () => const MapEntry('', null),
    );

    if (ehkUsername.key.isNotEmpty) {
      hasRightCellValue = hasRightCellValue &&
          !_isMarkedForDeletion(ehkUsername!.key, "EHK", widget.coaches);
    }

    return DataRow(cells: [
      const DataCell(
          Text('EHK', style: TextStyle(fontSize: 11))), // Reduced font size
      if (widget.hasAccess && showCol)
        DataCell(SizedBox(
          width: 150,
          child: Row(
            children: [
              ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 130,
                  minHeight: 40,
                ),
                child: DropdownSearch<String>(
                  enabled: !hasRightCellValue,
                  items: widget.ehkUsers.toList(),
                  onChanged: hasRightCellValue
                      ? null
                      : (value) {
                          if (value != null) _handleSelectChangeEhk([value],_amountInHand);
                        },
                  dropdownDecoratorProps: const DropDownDecoratorProps(
                    dropdownSearchDecoration: InputDecoration(
                      labelText: 'Select User',
                      labelStyle: TextStyle(
                        fontSize: 11,
                      ),
                      border: InputBorder.none,
                    ),
                    baseStyle: TextStyle(
                      fontSize: 11,
                    ),
                  ),
                  popupProps: PopupProps.menu(
                    showSearchBox: true,
                    constraints: const BoxConstraints(
                      maxHeight: 250,
                    ),
                    itemBuilder: (context, item, isSelected) {
                      return ListTile(
                        title: Text(item, style: const TextStyle(fontSize: 11)),
                        selected: isSelected,
                      );
                    },
                  ),
                  selectedItem: selectedEhk.isEmpty ? null : selectedEhk.first,
                ),
              ),
              ...selectedEhk.map((username) {
                final lastJourney = lastJourneyDetails[username];
                return lastJourney != null
                    ? Row(
                        children: [
                          Tooltip(
                            message: lastJourney,
                            triggerMode: TooltipTriggerMode.tap,
                            child: GestureDetector(
                              onTap: () {
                                String extractedNumber =
                                    _extractNumber(username);
                                _launchPhoneDialer(extractedNumber);
                                // print('Icon clicked! $phoneNumber');
                              },
                              child: const Icon(Icons.info, size: 16),
                            ),
                          ),
                        ],
                      )
                    : const SizedBox.shrink();
              }).toList(),
            ],
          ),
        )),
      DataCell(
        widget.ehkDict.isNotEmpty
            ? Column(
                children: widget.ehkDict.entries
                    .where((entry) => entry.value['origin_date'] == widget.date)
                    .map<Widget>((entry) {
                  final isMarkedForDeletion =
                      _isMarkedForDeletion(entry.key, "EHK", widget.coaches);

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 0.0, horizontal: 12.0),
                    decoration: BoxDecoration(
                        color: getUserColor(entry.value, isMarkedForDeletion)),
                    child: Row(
                      children: [
                        IconButton(
                            onPressed: () {
                              showCallModal(
                                context,
                                entry.key.toString(),
                              );
                            },
                            icon: const Icon(
                              Icons.call,
                              color: Colors.blue,
                            )),
                        GestureDetector(
                          onLongPress: () {
                            showCallModal(
                              context,
                              entry.key.toString(),
                            );
                          },
                          child: Text(
                            entry.key,
                            style: TextStyle(
                              fontSize: 11,
                              decoration: isMarkedForDeletion
                                  ? TextDecoration.lineThrough
                                  : TextDecoration.none,
                              color: isMarkedForDeletion
                                  ? Colors.grey
                                  : Colors.black,
                            ),
                          ),
                        ),
                        const Spacer(),
                        if (widget.hasAccess)
                          IconButton(
                            icon: Icon(
                              isMarkedForDeletion ? Icons.undo : Icons.delete,
                              color: isMarkedForDeletion
                                  ? Colors.blue
                                  : Colors.red,
                            ),
                            onPressed: () {
                              if (isMarkedForDeletion) {
                                _unmarkForDeletion(
                                    entry.key, "EHK", widget.coaches);
                              } else {
                                _markForDeletion(
                                    entry.key, "EHK", widget.coaches);
                              }
                            },
                          ),
                        IconButton(
                            onPressed: () {
                              openColorinfoDialog(entry.value, entry.key,
                                  entry.value['last_login_date_time'] ?? 'N/A');
                            },
                            icon: const Icon(Icons.info))
                      ],
                    ),
                  );
                }).toList(),
              )
            : const SizedBox.shrink(),
      ),
      DataCell(
        widget.ehkDict.isNotEmpty
            ? Column(
                children: widget.ehkDict.entries
                    .where((entry) => entry.value['origin_date'] == widget.date)
                    .map<Widget>((entry) {
                  final isMarkedForDeletion =
                      _isMarkedForDeletion(entry.key, "EHK", widget.coaches);
                  final String userId = entry.key;
                  final String amount = _amountInHand[userId] ?? "0";
                  // final double amount = entry.value['amount'] ?? 0.0;
                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 0.0, horizontal: 12.0),
                    decoration: BoxDecoration(
                        color: getUserColor(entry.value, isMarkedForDeletion)),
                    child: Row(
                      children: [
                        Text(
                          '₹${amount!='null' ? amount : "0"}',
                          style: TextStyle(
                            fontSize: 11,
                            decoration: isMarkedForDeletion
                                ? TextDecoration.lineThrough
                                : TextDecoration.none,
                            color: isMarkedForDeletion
                                ? Colors.grey
                                : Colors.black,
                          ),
                        ),
                        const Spacer(),
                        if (widget.hasAccess)
                          IconButton(
                            icon: const Icon(
                              Icons.edit,
                              color: Colors.blue,
                              size: 16,
                            ),
                            onPressed: () {
                              _showAmountUpdateDialog(userId, amount,"EHK");
                            },
                          ),
                      ],
                    ),
                  );
                }).toList(),
              )
            : const SizedBox.shrink(),
      ),
    ]);
  }

  Card _buildCoachCard(String username, int coachID, String cashCarry) {
    // Determine if there is a value in the right DataCell based on the conditions provided
    // bool hasRightCellValue = widget.coachWiseDict.containsKey(coach) &&
    //     widget.coachWiseDict[coach]!.any((user) => user['origin_date'] == widget.date);
    final isMarkedForDeletion =
        _isMarkedForDeletion(username, "OBHS", widget.coaches);
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6.0, horizontal: 12.0),
      elevation: 4.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Coach Title
            Text(
              (coachID + 1).toString(),
              // coachID.toString(),
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
            const Divider(thickness: 1.0),
            const Align(
              alignment: AlignmentDirectional.center,
              child: const Icon(
                Icons.person,
                color: Colors.blue,
                size: 90.0,
              ),
            ),
            if (username == "NA")
              Row(
                children: [
                  // DropdownSearch for Selecting Users
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(
                            height: 14), // Adds spacing above the dropdown
                        ConstrainedBox(
                          constraints: const BoxConstraints(
                            maxWidth: 140,
                            minHeight: 50,
                          ),
                          child: DropdownSearch<String>(
                              enabled: widget.hasAccess,
                              items: updatedOBHSUsers!.toList()
                                ..sort(
                                  (a, b) => a
                                      .toLowerCase()
                                      .compareTo(b.toLowerCase()),
                                ),
                              onChanged: (value) {
                                if (value != null) {
                                  _handleSelectChange(
                                      coachID.toString(), [value],_amountInHand);
                                }
                                _showAmountUpdateDialog(value!, _amountInHand[username] ?? "0","OBHS");
                              },
                              dropdownDecoratorProps:
                                  const DropDownDecoratorProps(
                                dropdownSearchDecoration: InputDecoration(
                                  labelText: 'Select User',
                                  labelStyle: TextStyle(fontSize: 12),
                                  border: OutlineInputBorder(),
                                ),
                              ),
                              popupProps: PopupProps.menu(
                                showSearchBox: true,
                                constraints:
                                    const BoxConstraints(maxHeight: 250),
                                itemBuilder: (context, item, isSelected) {
                                  return ListTile(
                                    title: Text(item,
                                        style: const TextStyle(fontSize: 12)),
                                    selected: isSelected,
                                  );
                                },
                              ),
                              selectedItem: selectedUsers.entries.any((entry) =>
                                      entry.key == coachID.toString())
                                  ? selectedUsers.entries
                                      .firstWhere((entry) =>
                                          entry.key == coachID.toString())
                                      .value
                                      .first
                                  : null),
                        ),
                        const SizedBox(
                            height: 8.0,
                        ),
                        // TextFormField(
                        //     enabled: widget.hasAccess,
                        //     initialValue: (_amountInHand[username] ?? 0.0).toString(),
                        //     decoration: const InputDecoration(
                        //       labelText: 'Amount in Hand (₹)',
                        //       border: OutlineInputBorder(),
                        //     ),
                        //     keyboardType:
                        //         TextInputType.numberWithOptions(decimal: true),
                        //     onChanged: (value) {
                        //       final amount = double.tryParse(value) ?? 0.0;
                        //       setState(() {
                        //         _amountInHand[username] = amount;
                        //         amountInHand[username] = amount;
                        //       });
                        //     },
                        //     onTap: () {
                        //       _showAmountUpdateDialog(username, _amountInHand[username] ?? 0.0);
                        //     },
                        //   )
                      ],
                    ),
                  ),

                  const SizedBox(
                    width: 8.0,
                  ),
                  // Tooltip and Icon for Contact Information
                  // ...(selectedUsers[coach] ?? []).map((username) {
                  //   final lastJourney = lastJourneyDetails[username];
                  //   return lastJourney != null
                  //       ? Tooltip(
                  //           message: lastJourney,
                  //           triggerMode: TooltipTriggerMode.tap,
                  //           child: IconButton(
                  //             icon: const Icon(Icons.info, size: 16, color: Colors.blue),
                  //             onPressed: () {
                  //               String extractedNumber = _extractNumber(username);
                  //               _launchPhoneDialer(extractedNumber);
                  //             },
                  //           ),
                  //         )
                  //       : const SizedBox.shrink();
                  // }).toList(),
                ],
              ),

            // User List with Additional Actions
            // if (widget.coachWiseDict.containsKey(coach))
            if (username != "NA")
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(bottom: 6.0),
                      padding: const EdgeInsets.symmetric(
                          vertical: 6.0, horizontal: 10.0),
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: Colors.grey.shade300), // Optional border
                        borderRadius: BorderRadius.circular(6.0),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // User Name with Long Press Gesture
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                showCallModal(
                                  context,
                                  username,
                                );
                              },
                              child: Text(
                                style: TextStyle(
                                  fontSize: 12,
                                  decoration: isMarkedForDeletion
                                      ? TextDecoration.lineThrough
                                      : TextDecoration.none,
                                  color: isMarkedForDeletion
                                      ? Colors.grey
                                      : Colors.black,
                                ),
                                '${username}', // Display the single username
                                // style: const TextStyle(fontSize: 12),
                                softWrap:
                                    true, // Allows text to wrap to the next line
                                maxLines: null,
                              ),
                            ),
                          ),
                          // Delete Button
                          if (widget.hasAccess)
                            // IconButton(
                            //   icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                            //   onPressed: () {
                            //     widget.removeTrainDetailsOriginDate(
                            //       username, // Pass the single username
                            //       widget.coaches,
                            //       widget.train,
                            //       widget.date,
                            //       "OBHS"
                            //     );
                            //   },
                            //   tooltip: 'Remove User',
                            // ),
                            IconButton(
                              icon: Icon(
                                isMarkedForDeletion ? Icons.undo : Icons.delete,
                                color: isMarkedForDeletion
                                    ? Colors.blue
                                    : Colors.red,
                              ),
                              onPressed: () {
                                if (isMarkedForDeletion) {
                                  _unmarkForDeletion(
                                      username, "OBHS", widget.coaches);
                                } else {
                                  _markForDeletion(
                                      username, "OBHS", widget.coaches);
                                }
                              },
                            ),
                        ],
                      ),
                    ),
                    // TextFormField(
                    //   enabled: widget.hasAccess,
                    //   initialValue: (_amountInHand[username] ?? 0.0).toString(),
                    //   decoration: const InputDecoration(
                    //     labelText: 'Amount in Hand (₹)',
                    //     border: OutlineInputBorder(),
                    //   ),
                    //   keyboardType:
                    //       TextInputType.numberWithOptions(decimal: true),
                    //   onChanged: (value) {
                    //     final amount = double.tryParse(value) ?? 0.0;
                    //     setState(() {
                    //       _amountInHand[username] = amount;
                    //       amountInHand[username] = amount;
                    //     });
                    //   },
                    //   onTap: () {
                    //     _showAmountUpdateDialog(username, _amountInHand[username] ?? 0.0);
                    //   },
                    // )
                    Row(
                      children: [
                        Text(
                          '₹${_amountInHand[username] ?? "0"}',
                          style: TextStyle(
                            fontSize: 11,
                            decoration: isMarkedForDeletion
                                ? TextDecoration.lineThrough
                                : TextDecoration.none,
                            color: isMarkedForDeletion
                                ? Colors.grey
                                : Colors.black,
                          ),
                        ),
                        const Spacer(),
                        if (widget.hasAccess)
                          IconButton(
                            icon: const Icon(
                              Icons.edit,
                              color: Colors.blue,
                              size: 16,
                            ),
                            onPressed: () {
                              _showAmountUpdateDialog(username, _amountInHand[username] ?? "0","OBHS");
                            },
                          ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
