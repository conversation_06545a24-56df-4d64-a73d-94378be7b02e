import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/widgets/notification_tray_widget.dart';
import 'package:railops/models/notification_tray_model.dart';

/// Full-screen notification tray displaying train location-based notifications
/// Shows station-coach-count data in table format with filtering and actions
class NotificationTrayScreen extends StatefulWidget {
  const NotificationTrayScreen({super.key});

  @override
  State<NotificationTrayScreen> createState() => _NotificationTrayScreenState();
}

class _NotificationTrayScreenState extends State<NotificationTrayScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedStation = 'All';
  String _selectedCoach = 'All';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize provider if not already done
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationTrayProvider>(context, listen: false)
          .initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Train Notifications'),
        backgroundColor: Colors.blue.shade50,
        actions: [
          Consumer<NotificationTrayProvider>(
            builder: (context, provider, child) {
              return IconButton(
                icon: Badge(
                  isLabelVisible: provider.unreadCount > 0,
                  label: Text(provider.unreadCount.toString()),
                  child: const Icon(Icons.notifications),
                ),
                onPressed: () => _showNotificationActions(context, provider),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All', icon: Icon(Icons.list)),
            Tab(text: 'Unread', icon: Icon(Icons.mark_email_unread)),
            Tab(text: 'Summary', icon: Icon(Icons.analytics)),
          ],
        ),
      ),
      body: Consumer<NotificationTrayProvider>(
        builder: (context, provider, child) {
          return RefreshIndicator(
            onRefresh: () => provider.refresh(),
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllNotificationsTab(provider),
                _buildUnreadNotificationsTab(provider),
                _buildSummaryTab(provider),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showTestDialog(context),
        tooltip: 'Test Notifications',
        child: const Icon(Icons.add_alert),
      ),
    );
  }

  Widget _buildAllNotificationsTab(NotificationTrayProvider provider) {
    final filteredItems = _getFilteredItems(provider.items);

    if (filteredItems.isEmpty) {
      return _buildEmptyState('No notifications match your filters');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return _buildNotificationCard(item, provider);
      },
    );
  }

  Widget _buildUnreadNotificationsTab(NotificationTrayProvider provider) {
    final filteredItems = _getFilteredItems(provider.unreadItems);

    if (filteredItems.isEmpty) {
      return _buildEmptyState('No unread notifications');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        return _buildNotificationCard(item, provider);
      },
    );
  }

  Widget _buildSummaryTab(NotificationTrayProvider provider) {
    final summary = provider.summary;
    final itemsByStation = provider.itemsByStation;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(summary),
          const SizedBox(height: 24),
          if (itemsByStation.isNotEmpty) ...[
            Text(
              'Station Breakdown',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            ...itemsByStation.entries
                .map((entry) => _buildStationSummaryCard(entry.value)),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCards(NotificationTraySummary summary) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Onboarding',
                summary.totalOnboardingCount.toString(),
                Colors.green,
                Icons.login,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Total Off-boarding',
                summary.totalOffboardingCount.toString(),
                Colors.orange,
                Icons.logout,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Vacant',
                summary.totalVacantCount.toString(),
                Colors.grey,
                Icons.event_seat,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Unread',
                summary.totalUnreadCount.toString(),
                Colors.blue,
                Icons.mark_email_unread,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
      String title, String value, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStationSummaryCard(StationNotificationGroup group) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  group.stationCode,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (group.hasUnreadItems)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${group.unreadCount} unread',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                _buildCountBadge(
                  'Onboarding',
                  group.totalOnboardingCount,
                  Colors.green,
                ),
                const SizedBox(width: 12),
                _buildCountBadge(
                  'Off-boarding',
                  group.totalOffboardingCount,
                  Colors.orange,
                ),
                const SizedBox(width: 12),
                _buildCountBadge(
                  'Vacant',
                  group.totalVacantCount,
                  Colors.grey,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Coaches: ${group.coachNumbers.join(', ')}',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCountBadge(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildNotificationCard(
      NotificationTrayItem item, NotificationTrayProvider provider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: item.isRead ? 1 : 3,
      child: InkWell(
        onTap: () {
          if (!item.isRead) {
            provider.markAsRead(item.id);
          }
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${item.stationCode} - ${item.coachNumber}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight:
                          item.isRead ? FontWeight.normal : FontWeight.bold,
                    ),
                  ),
                  if (!item.isRead)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.blue,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildCountChip(
                      item.onboardingCount, Colors.green, 'Onboarding'),
                  const SizedBox(width: 8),
                  _buildCountChip(
                      item.offboardingCount, Colors.orange, 'Off-boarding'),
                  const SizedBox(width: 8),
                  _buildCountChip(item.vacantCount, Colors.grey, 'Vacant'),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Train: ${item.trainNumber} • ${item.date}',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCountChip(int count, Color color, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<NotificationTrayItem> _getFilteredItems(
      List<NotificationTrayItem> items) {
    return items.where((item) {
      if (_selectedStation != 'All' && item.stationCode != _selectedStation) {
        return false;
      }
      if (_selectedCoach != 'All' && item.coachNumber != _selectedCoach) {
        return false;
      }
      return true;
    }).toList();
  }

  void _showFilterDialog(BuildContext context) {
    final provider =
        Provider.of<NotificationTrayProvider>(context, listen: false);
    final stations = ['All', ...provider.summary.activeStations];
    final coaches = ['All', ...provider.summary.activeCoaches];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Notifications'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: _selectedStation,
              decoration: const InputDecoration(labelText: 'Station'),
              items: stations
                  .map((station) => DropdownMenuItem(
                        value: station,
                        child: Text(station),
                      ))
                  .toList(),
              onChanged: (value) => setState(() => _selectedStation = value!),
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedCoach,
              decoration: const InputDecoration(labelText: 'Coach'),
              items: coaches
                  .map((coach) => DropdownMenuItem(
                        value: coach,
                        child: Text(coach),
                      ))
                  .toList(),
              onChanged: (value) => setState(() => _selectedCoach = value!),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedStation = 'All';
                _selectedCoach = 'All';
              });
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showNotificationActions(
      BuildContext context, NotificationTrayProvider provider) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.done_all),
              title: const Text('Mark All as Read'),
              onTap: () {
                provider.markAllAsRead();
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('Refresh'),
              onTap: () {
                provider.refresh();
                Navigator.of(context).pop();
              },
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear All'),
              onTap: () {
                Navigator.of(context).pop();
                _showClearConfirmation(context, provider);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearConfirmation(
      BuildContext context, NotificationTrayProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text(
            'Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              provider.clearAll();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  void _showTestDialog(BuildContext context) {
    // This would integrate with the existing notification testing system
    Navigator.of(context).pushNamed('/notification-testing');
  }
}
