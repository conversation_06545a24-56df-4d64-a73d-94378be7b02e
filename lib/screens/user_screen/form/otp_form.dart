import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/otp_services/index.dart';

class OtpForm extends StatefulWidget {
  const OtpForm({super.key});

  @override
  _OtpForm createState() => _OtpForm();
}

class _OtpForm extends State<OtpForm> {
  String otp = '';
  String? mobileNumber;
  bool _isLoadingConfirm = false;
  bool _isLoadingResend = false;
  bool _showModal = false;
  Timer? _timer;
  int _start = 60; // Timer for 1 minute (60 seconds)
  String? value;
  String? type;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    setState(() {
      _isLoadingResend = true;
      _start = 60;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_start == 0) {
        setState(() {
          _isLoadingResend = false;
        });
        timer.cancel();
      } else {
        setState(() {
          _start--;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final args = ModalRoute.of(context)?.settings.arguments as String?;
    if (args != null && mobileNumber == null) {
      setState(() {
        mobileNumber = args;
      });
    }
  }

  void _resendOtp() async {
    _startTimer();
    type = 'phone_number';
    value = mobileNumber;
    try {
      final sendOtpResponse = await SignUpOtp.sendOtp(value!, type!);
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('OTP resent successfully')));
    } catch (e) {
      _showErrorModal(context, 'Failed to resend OTP: $e');
    }
  }

  void _showErrorModal(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(errorMessage),
          actions: <Widget>[
            TextButton(
              child: const Text('Close'),
              onPressed: () {
                setState(() {
                  _showModal = false;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _submitOtp() async {
    setState(() {
      _isLoadingConfirm = true;
    });
    try {
      final loginResponse = await AuthService.loginByMobile(otp, mobileNumber!);
      Provider.of<AuthModel>(context, listen: false).login(loginResponse!);
      Provider.of<UserModel>(context, listen: false).updateUserDetails(
        userName: loginResponse.userName,
        mobileNumber: loginResponse.mobileNumber,
        stationCode: loginResponse.stationCode,
        stationName: loginResponse.stationName,
        token: loginResponse.token,
        userType: loginResponse.userType,
        refreshToken: loginResponse.refreshToken,
      );
      Navigator.pushReplacementNamed(context, Routes.attendance);
    } catch (e) {
      if (e is StateError && e.toString().contains('mounted')) {
        print('Widget disposed before operation completes');
      } else {
        print('Login Error: $e');
        _showErrorModal(context, '$e');
      }
    } finally {
      setState(() {
        _isLoadingConfirm = false;
      });
    }
  }

  void _handleOtpChange(String newOtp) {
    setState(() {
      otp = newOtp;
    });
    if (otp.length == 6 && !_isLoadingConfirm) {
      _submitOtp();
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.8; // Adjusted for column layout

    return Form(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          OtpData(onOtpChanged: _handleOtpChange),
          Padding(
            padding: const EdgeInsets.only(top: 32.0),
            child: Column(
              children: [
                const SizedBox(height: 20), // Spacing between buttons
                OutlinedButton.icon(
                  style: OutlinedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF1E88E5),
                    side: const BorderSide(color: Color(0xFF1E88E5)),
                    fixedSize: Size(buttonWidth, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _isLoadingResend ? null : _resendOtp,
                  icon: const Icon(Icons.refresh, color: Color(0xFF1E88E5)),
                  label: _isLoadingResend
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const SizedBox(width: 10),
                            Text(
                              'Resend ($_start)',
                              style: const TextStyle(
                                color: Color(
                                    0xFF1E88E5), // More visible text color
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        )
                      : const Text(
                          'Resend',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF1E88E5), // More visible text color
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
