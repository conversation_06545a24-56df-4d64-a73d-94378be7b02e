import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/services/otp_services/index.dart';

class LoginMobileScreen extends StatefulWidget {
  const LoginMobileScreen({super.key});

  @override
  _LoginMobileScreen createState() => _LoginMobileScreen();
}

class _LoginMobileScreen extends State<LoginMobileScreen> {
  final TextEditingController _mobileController = TextEditingController();
  late String _mobileNumber;
  String? value;
  String? type;
  bool _isLoading = false;
  bool _showErrorModal = false;
  String _errorMessage = '';
  @override
  void initState() {
    super.initState();
    _mobileController.addListener(() {
      if (_mobileController.text.length == 10 && !_isLoading) {
        _mobileNumber = _mobileController.text;
        _sendOtp();
      }
    });
  }

  @override
  void dispose() {
    _mobileController.dispose();
    super.dispose();
  }

  void _sendOtp() async {
    setState(() {
      _isLoading = true;
    });

    type = 'phone_number';
    value = _mobileNumber;

    try {
      final sendOtpResponse = await SignUpOtp.sendOtp(value!, type!);
      Navigator.pushReplacementNamed(context, Routes.otpEnter,
          arguments: _mobileNumber);
    } catch (e) {
      if (e is StateError && e.toString().contains('mounted')) {
        print('Widget disposed before operation completes');
      } else {
        print('Send Otp Failed: $e');
        _showErrorModalDialog(context, '$e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorModalDialog(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(errorMessage),
          actions: <Widget>[
            TextButton(
              child: const Text('Close'),
              onPressed: () {
                setState(() {
                  _showErrorModal = false;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.8;

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Mobile Login',
          style: TextStyle(color: Colors.black), // Changed text color to black
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pushNamed(context, Routes.login);
          },
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Align(
            alignment: Alignment.center,
            child: Column(
              children: [
                const Text(
                  "Login",
                  style: TextStyle(
                    fontSize:
                        28, // Increased font size for a more prominent header/ Added bold to make it stand out
                    color: Colors.black87, // Use your primary color
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Please enter your phone number',
                  style: TextStyle(
                    fontSize: 16, // Standardized font size
                    color: Colors.black54, // Subtle grey color for the subtitle
                  ),
                ),
                const SizedBox(height: 16),
                // MobileField(onSaved: (value) => _mobileNumber = value),
                TextFormField(
                  controller: _mobileController,
                  keyboardType: TextInputType.phone,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(10),
                  ],
                  decoration: const InputDecoration(
                    labelText: 'Mobile Number',
                    hintText: 'Enter your 10-digit mobile number only',
                    hintStyle: TextStyle(fontSize: 12, color: Colors.grey),
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 32),
                if (_isLoading)
                  const Center(
                    child: CircularProgressIndicator(
                      color: Colors.blue,
                    ),
                  ),
                // Padding(
                //   padding: const EdgeInsets.symmetric(horizontal: 16.0),
                //   child: SizedBox(
                //     width: buttonWidth,
                //     child: ElevatedButton(
                //       style: ElevatedButton.styleFrom(
                //         backgroundColor: Colors.blue,
                //         foregroundColor: Colors.white,
                //         fixedSize: Size(buttonWidth, 50),
                //         shape: RoundedRectangleBorder(
                //           borderRadius: BorderRadius.circular(12), // Increased border radius for a softer look
                //         ),
                //       ),
                //       onPressed: _sendOtp,
                //       child: _isLoading
                //           ? const CircularProgressIndicator(color: Colors.white,)
                //           : const Text(
                //               'GENERATE OTP',
                //               style: TextStyle(
                //                 fontSize: 18, // Slightly increased font size for button text
                //                 fontWeight: FontWeight.bold, // Bold text for emphasis
                //               ),
                //             ),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
