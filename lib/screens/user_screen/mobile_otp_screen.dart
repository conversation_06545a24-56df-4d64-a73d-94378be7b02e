import 'package:flutter/material.dart';
import 'package:railops/screens/user_screen/form/otp_form.dart';

class MobileOtpScreen extends StatelessWidget {
  const MobileOtpScreen({Key? key});

  @override
  Widget build(BuildContext context) {
    final String mobileNumner =
        ModalRoute.of(context)?.settings.arguments as String;
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Enter OTP',
          style: TextStyle(color: Colors.black),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/mobile-login');
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Please enter the OTP sent to your mobile number: $mobileNumner ',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const OtpForm(),
          ],
        ),
      ),
    );
  }
}
