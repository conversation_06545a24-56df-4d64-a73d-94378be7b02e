import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';
import "dart:io" as io;
import "package:universal_html/html.dart" as html;
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart';
import 'package:http/http.dart' as http;

class ImageDetailPage extends StatelessWidget {
  final ImageResponse imageResponse;

  const ImageDetailPage({super.key, required this.imageResponse});
  
  Future<void> _downloadImage(BuildContext context) async {
    final imageUrl = imageResponse.imageUrl;
    print("Image reposne : ${imageResponse.createdBy}");
    if (imageUrl == null || imageUrl.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No image URL provided')),
      );
      return;
    }

    try {
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        final imageBytes = response.bodyBytes;

        if (kIsWeb) {
          final blob = html.Blob([imageBytes]);
          final url = html.Url.createObjectUrlFromBlob(blob);
          final anchor = html.AnchorElement(href: url)
            ..setAttribute("download", "image.jpg")
            ..click();
          html.Url.revokeObjectUrl(url);
        } else {
          final directory = await getApplicationDocumentsDirectory();
          final filePath = '${directory.path}/image.jpg';
          final file = io.File(filePath);
          await file.writeAsBytes(imageBytes);

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Image downloaded successfully!')),
          );
        }
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to download image')),
        );
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to download image: $error')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime? createdAt = imageResponse.createdAt != null
        ? DateTime.parse(imageResponse.createdAt!)
        : null;
    String formattedDate = createdAt != null
        ? DateFormat('dd-MM-yyyy').format(createdAt)
        : 'Unknown';
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Detail'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: InteractiveViewer(
                  panEnabled: true,
                  minScale: 0.5,
                  maxScale: 4.0,
                  child: Image.network(
                    imageResponse.imageUrl ?? '',
                    width: double.infinity,
                    fit: BoxFit.contain,
                    errorBuilder: (BuildContext context, Object exception,
                        StackTrace? stackTrace) {
                      return Container(
                        color: Colors.grey,
                        child: const Icon(Icons.error, color: Colors.red, size: 100),
                      );
                    },
                  ),
                ),
              ),
            const SizedBox(height: 16),
            Text(
              'Train: ${imageResponse.train}',
              style: const TextStyle(fontSize: 16),
            ),
            if( imageResponse.coach != null)
              Text(
                'Coach: ${imageResponse.coach}',
                style: const TextStyle(fontSize: 16),
              ),
            if( imageResponse.coach == null)
              Text(
                'Coach No: ${imageResponse.coachNumber}',
                style: const TextStyle(fontSize: 16),
              ),
            if( imageResponse.issue != null)
              Text(
                'Issue: ${imageResponse.issue}',
                style: const TextStyle(fontSize: 16),
              ),
            Text(
              'Latitude: ${imageResponse.latitude}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Longitude: ${imageResponse.longitude}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Uploaded by: ${imageResponse.updatedBy .isEmpty ? 'Unknown' : imageResponse.updatedBy}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Uploaded at: $formattedDate',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _downloadImage(context),
              child: const Text('Download Image'),
            ),
          ],
        ),
      ),
    );
  }
}
