import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';
import 'package:video_player/video_player.dart';

class ImgVideoDetailPage extends StatefulWidget {
  final ImageResponse imgVideoResponse;
  final String? url;
  final bool? isImage;
  final String trainNumber;

  const ImgVideoDetailPage({
    super.key,
    required this.imgVideoResponse,
    required this.url,
    required this.trainNumber,
    required this.isImage,
  });

  @override
  State<ImgVideoDetailPage> createState() => _ImgVideoDetailPageState();
}

class _ImgVideoDetailPageState extends State<ImgVideoDetailPage> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    if (!widget.isImage!) {
      _initializeVideo();
    }
  }

  Future<void> _initializeVideo() async {
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.url!))
      ..addListener(() {
        if (_controller!.value.isPlaying != _isPlaying) {
          setState(() => _isPlaying = _controller!.value.isPlaying);
        }
      })
      ..initialize().then((_) {
        if (mounted) {
          setState(() => _isInitialized = true);
        }
      });
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      if (_controller!.value.isPlaying) {
        _controller!.pause();
      } else {
        _controller!.play();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    DateTime? createdAt = widget.imgVideoResponse.createdAt != null
        ? DateTime.parse(widget.imgVideoResponse.createdAt!)
        : null;
    String formattedDate = createdAt != null
        ? DateFormat('dd-MM-yyyy').format(createdAt)
        : 'Unknown';

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isImage! ? 'Image Detail' : 'Video Detail'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: widget.isImage!
                  ? Image.network(
                      widget.url!,
                      width: double.infinity,
                      fit: BoxFit.contain,
                      errorBuilder: (BuildContext context, Object exception,
                          StackTrace? stackTrace) {
                        return Container(
                          color: Colors.grey,
                          child: const Icon(Icons.error,
                              color: Colors.red, size: 100),
                        );
                      },
                    )
                  : AspectRatio(
                      aspectRatio: _isInitialized
                          ? _controller!.value.aspectRatio
                          : 16 / 9,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          if (_isInitialized)
                            VideoPlayer(_controller!)
                          else
                            const Center(child: CircularProgressIndicator()),
                          Positioned.fill(
                            child: IconButton(
                              iconSize: 64,
                              icon: Icon(
                                _isPlaying ? Icons.pause : Icons.play_arrow,
                                color: Colors.white.withOpacity(0.7),
                              ),
                              onPressed: _togglePlayPause,
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
            const SizedBox(height: 16),
            // Rest of your metadata fields
            Text(
              'Train: ${widget.trainNumber}',
              style: const TextStyle(fontSize: 16),
            ),
            if (widget.imgVideoResponse.coach != null)
              Text(
                'Coach: ${widget.imgVideoResponse.coach}',
                style: const TextStyle(fontSize: 16),
              ),
            if (widget.imgVideoResponse.coach == null)
              Text(
                'Coach No: ${widget.imgVideoResponse.coachNumber}',
                style: const TextStyle(fontSize: 16),
              ),
            if (widget.imgVideoResponse.issue != null)
              Text(
                'Issue: ${widget.imgVideoResponse.issue}',
                style: const TextStyle(fontSize: 16),
              ),
            Text(
              'Latitude: ${widget.imgVideoResponse.latitude}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Longitude: ${widget.imgVideoResponse.longitude}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Uploaded by: ${widget.imgVideoResponse.createdBy.isEmpty ? 'Unknown' : widget.imgVideoResponse.createdBy}',
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              'Uploaded at: $formattedDate',
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}