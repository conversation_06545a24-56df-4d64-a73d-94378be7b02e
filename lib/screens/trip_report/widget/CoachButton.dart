import 'package:flutter/material.dart';

class CoachButton extends StatelessWidget {
  final String coach;
  final List<int> issues;
  final VoidCallback onTap;
  final Map<int, String> problemOptions;
  final bool isLast;

  const CoachButton({
    Key? key,
    required this.coach,
    required this.issues,
    required this.onTap,
    required this.problemOptions,
    this.isLast = false,
  }) : super(key: key);

  IconData getIconForProblem(int problemId) {
    switch (problemId) {
      case 1:
        return Icons.door_front_door;
      case 2:
        return Icons.wash;
      case 3:
        return Icons.lock_outline;
      case 4:
        return Icons.plumbing_sharp;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    List<int> displayedIssues = issues.take(2).toList();

    return Container(
      // margin: const EdgeInsets.only(bottom: 5),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: onTap,
                child: ElevatedButton(
                  onPressed: onTap,
                  style: ElevatedButton.styleFrom(
                    backgroundColor:Colors.white,
                    fixedSize: const Size(100, 40), // Adjusted width
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: BorderSide(
                        color: issues.isEmpty ? Colors.grey : Colors.yellow,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Upload Icon
                      // Icon(
                      //   Icons.upload_file,
                      //   size: 20,
                      //   color: issues.isEmpty ? Colors.blue : Colors.red,
                      // ),
                      // const SizedBox(width: 5), // Space between elements

                      // Coach Number
                      Text(
                        coach,
                        style: TextStyle(
                        
                          fontSize: 18,
                          color: issues.isEmpty
                              ? Colors.black
                              : Color.fromARGB(255, 77, 6, 144),
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      // Issue Icons
                      if (issues.isNotEmpty)
                        Row(
                          children: [
                            for (var issueId in displayedIssues)
                              Padding(
                                padding: const EdgeInsets.only(right: 1),
                                child: Icon(
                                  getIconForProblem(issueId),
                                  size: 16,
                                  color: Colors.lightBlue,
                                ),
                              ),
                            if (issues.length > 2)
                              const Text(
                                "...",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.lightBlue,
                                ),
                              ),
                          ],
                        ),
                    ],
                    
                  ),
                  
                ),
              ),
              if (!isLast) const Icon(Icons.keyboard_tab, size: 20),
            ],
          ),
        ],
      ),
    );
  }
}
