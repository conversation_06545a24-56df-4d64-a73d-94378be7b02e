import 'dart:io';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart';
import 'package:railops/screens/attendance/uploaded_image.dart';
import 'package:railops/screens/trip_report/widget/TripReportFilePreview.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/assign_ehk_ca_services/jobchart_image_upload.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/services/upload_services/upload_services.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:railops/types/trip_report_type/trip_report_type.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/widgets/error_modal.dart';

class CoachIssueStatus extends StatefulWidget {
  final String trainNumber;
  final String journeyDate;
  final String coach;

  const CoachIssueStatus({
    Key? key,
    required this.trainNumber,
    required this.journeyDate,
    required this.coach,
  }) : super(key: key);

  @override
  _CoachIssueStatusState createState() => _CoachIssueStatusState();
}

class _CoachIssueStatusState extends State<CoachIssueStatus> {
  bool _isFileLoading = false;
  String? token;
  List<TripReportType> uploadedFiles = [];
  String? selectedProblemId;
  late List<String> caUsers = [];
  late List<String> obhsUsers = [];
  late List<String> ehkUsers = [];
  late List<String> users = [];

  @override
  void initState() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _fetchUploadedFiles();
    fetchSpecificUsers("OBHS");
    fetchSpecificUsers("coach attendent");
    fetchSpecificUsers("EHK");
    super.initState();
  }

  Future<List<JobchartType>?> _fetchUploadedFiles(
      {String? selectedUser}) async {
    try {
      setState(() {
        _isFileLoading = true;
      });

      final response = await TripReportServices.fetchTripReportImages(
        trainNumber: widget.trainNumber,
        date: widget.journeyDate,
        token: token!,
        coach: widget.coach,
      );

      if (response != null) {
        setState(() {
          uploadedFiles = response!;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No images available')),
        );
      }

      setState(() {
        _isFileLoading = false;
      });
    } catch (e) {
      setState(() {
        _isFileLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error fetching images: $e')),
      );
    }
  }

  Future<void> fetchSpecificUsers(String userType) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    if (token.isNotEmpty) {
      final response =
          await AdminAssignService.fetchSpecificUsers(userType, token);

      setState(() {
        if (userType == "EHK") {
          ehkUsers = response.map<String>((ehk) => ehk["username"]).toList();
        } else if (userType == "coach attendent") {
          caUsers = response.map<String>((ca) => ca["username"]).toList();
        } else if (userType == "OBHS") {
          obhsUsers = response.map<String>((ca) => ca["username"]).toList();
        }
      });
      users = [...obhsUsers!, ...caUsers!, ...ehkUsers!].toSet().toList()
        ..sort(
          (a, b) => a.toLowerCase().compareTo(b.toLowerCase()),
        );
    }
  }

  Future<void> _handleRefresh() async {
    _fetchUploadedFiles();
    setState(() {
      uploadedFiles = [];
    });

    await Future.delayed(const Duration(seconds: 2));
  }

  bool _isVideo(XFile file) {
    final extension = file.name.split('.').last.toLowerCase();
    return ['mp4', 'mov', 'avi', 'mkv'].contains(extension);
  }

  @override
  Widget build(BuildContext context) {
    double availableSpace = MediaQuery.of(context).size.width * 0.8;
    return Scaffold(
        appBar: const CustomAppBar(title: "Isssue Status"),
        drawer: const CustomDrawer(),
        body: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Center(
                  child: Text('Coach Issue Status',
                      style: TextStyle(
                        fontSize: 22,
                      )),
                ),
                SizedBox(height: 10),
                // Train Details Card
                Card(
                  elevation: 5,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween, 
                          children: [
                            Expanded(child: _buildDetailRow('Train Number: ', widget.trainNumber)),
                            SizedBox(width: 2),
                            Expanded(child: _buildDetailRow('Coach: ', widget.coach),),
                          ],
                        ),   
                        _buildDetailRow('Journey Date: ', widget.journeyDate)
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 10),

                Container(
                  width: availableSpace,
                  margin: const EdgeInsets.all(20),
                  child: Card(
                    margin: EdgeInsets.zero,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const Text(
                            'Uploaded Issues Status',
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              fontSize: 18,
                            ),
                          ),
                          Column(
                            children: [
                              if (_isFileLoading)
                                const CircularProgressIndicator(
                                    color: Colors.blueAccent),
                              if (!_isFileLoading && uploadedFiles.isNotEmpty)
                                ...uploadedFiles
                                    .where((imageData) =>
                                        imageData.imageUrls != "" ||
                                        imageData.videoUrls != "")
                                    .map((imageData) {
                                  return TripReportFilePreview(
                                    imageResponse: imageData,
                                    showStatus: true,
                                    journeyDate: widget.journeyDate,
                                    trainNumber: widget.trainNumber,
                                    coach: widget.coach,
                                    users: users,
                                  );
                                }).toList(),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}

Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
