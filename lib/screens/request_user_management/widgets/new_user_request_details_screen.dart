import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/request_user_management/widgets/new_user_request_list_title.dart';

import 'package:railops/services/authentication_services/index.dart';
import 'package:railops/types/index.dart';
import 'package:railops/widgets/error_modal.dart';

class NewUserRequestDetailsScreen extends StatefulWidget {
  final List<RequestedUserResponse> requests;
  final String title;
  final VoidCallback onRefresh;
  final Color accentColor;

  const NewUserRequestDetailsScreen({
    Key? key,
    required this.requests,
    required this.title,
    required this.onRefresh,
    this.accentColor = Colors.blue,
  }) : super(key: key);

  @override
  _NewUserRequestDetailsScreenState createState() =>
      _NewUserRequestDetailsScreenState();
}

class _NewUserRequestDetailsScreenState
    extends State<NewUserRequestDetailsScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<RequestedUserResponse> filteredRequests = [];
  Set<int> selectedRequestIds = {};
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    filteredRequests = widget.requests;
  }

  void _filterRequests(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredRequests = widget.requests;
      } else {
        filteredRequests = widget.requests
            .where((request) =>
                ('${request.userFName} ${request.userLName}')
                    .toLowerCase()
                    .contains(query.toLowerCase()) ||
                request.userEmail.toLowerCase().contains(query.toLowerCase()) ||
                request.userPhone.toLowerCase().contains(query.toLowerCase()) ||
                request.userDepot.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  void _toggleRequestSelection(RequestedUserResponse request) {
    setState(() {
      if (selectedRequestIds.contains(request.id)) {
        selectedRequestIds.remove(request.id);
      } else {
        selectedRequestIds.add(request.id);
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (selectedRequestIds.length == filteredRequests.length) {
        selectedRequestIds.clear();
      } else {
        selectedRequestIds = filteredRequests.map((r) => r.id).toSet();
      }
    });
  }

  Future<void> _handleBulkAction(String action) async {
    if (_isProcessing) return;

    List<RequestedUserResponse> requestsToProcess = [];

    if (action == 'APPROVE_ALL') {
      requestsToProcess = List.from(filteredRequests);
    } else if (action == 'APPROVE_SELECTED') {
      requestsToProcess = filteredRequests
          .where((request) => selectedRequestIds.contains(request.id))
          .toList();
    }

    if (requestsToProcess.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No requests selected'),
          backgroundColor: Colors.blue,
        ),
      );
      return;
    }

    _showConfirmationDialog(action, requestsToProcess);
  }

  Future<void> _showConfirmationDialog(
      String action, List<RequestedUserResponse> requestsToProcess) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title: Text(
            'Approve Confirmation',
            style: TextStyle(
              color: widget.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Are you sure you want to approve these users:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ...requestsToProcess
                        .map((request) => Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Row(
                                children: [
                                  const Icon(Icons.person_outline,
                                      size: 16, color: Colors.blue),
                                  const SizedBox(width: 8),
                                  Text(
                                    '${request.userFName} ${request.userLName}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                            ))
                        .take(5),
                    if (requestsToProcess.length > 5)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          '...and ${requestsToProcess.length - 5} more',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child:
                  const Text('Cancel', style: TextStyle(color: Colors.black54)),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            ElevatedButton(
              child: const Text('Approve'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                elevation: 0,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
          actionsPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        );
      },
    );

    if (result == true) {
      _processRequests(requestsToProcess);
    }
  }

  Future<void> _processRequests(
      List<RequestedUserResponse> requestsToProcess) async {
    setState(() => _isProcessing = true);

    try {
      final token = Provider.of<UserModel>(context, listen: false).token;
      if (token == null) {
        throw Exception('Authentication token is missing');
      }

      // Process the requests sequentially
      for (var request in requestsToProcess) {
        await RequestedUserService.approveUser(
            'APPROVE', request.id.toString(), token);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('${requestsToProcess.length} users approved successfully'),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(12),
        ),
      );

      // Refresh the list and close the screen
      widget.onRefresh();
      Navigator.of(context).pop();
    } catch (error) {
      showErrorModal(context, '$error'.toString(), "Error", () {});
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  Future<void> _handleSingleUserAction(
      String action, RequestedUserResponse user) async {
    if (_isProcessing) return;

    // Show confirmation dialog for deny action
    if (action == 'DENY') {
      final confirm = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          title:
              const Text('Confirm Denial', style: TextStyle(color: Colors.red)),
          content: Text(
              'Are you sure you want to deny the request from ${user.userFName} ${user.userLName}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                elevation: 0,
              ),
              child: const Text('Deny Request'),
            ),
          ],
          actionsPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      );

      if (confirm != true) return;
    }

    setState(() => _isProcessing = true);

    try {
      final token = Provider.of<UserModel>(context, listen: false).token;
      if (token == null) {
        throw Exception('Authentication token is missing');
      }

      await RequestedUserService.approveUser(action, user.id.toString(), token);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            action == 'APPROVE'
                ? 'User approved successfully'
                : 'User request denied',
          ),
          backgroundColor: action == 'APPROVE' ? Colors.green : Colors.blue,
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          margin: const EdgeInsets.all(12),
        ),
      );

      // Refresh the requests list and update UI
      widget.onRefresh();
      // Remove from filtered list to give immediate UI feedback
      setState(() {
        filteredRequests.removeWhere((request) => request.id == user.id);
        selectedRequestIds.remove(user.id);
      });

      // If all requests are processed, go back
      if (filteredRequests.isEmpty) {
        Navigator.of(context).pop();
      }
    } catch (error) {
      showErrorModal(context, '$error'.toString(), "Error", () {});
    } finally {
      setState(() => _isProcessing = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isAllSelected = filteredRequests.isNotEmpty &&
        selectedRequestIds.length == filteredRequests.length;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        centerTitle: true,
        backgroundColor: widget.accentColor,
        foregroundColor: Colors.white,
        elevation: 2,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(5)),
        ),
      ),
      body: Column(
        children: [
          // Search and filter area
          Container(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
            decoration: BoxDecoration(
              color: widget.accentColor.withOpacity(0.05),
              borderRadius:
                  const BorderRadius.vertical(bottom: Radius.circular(20)),
            ),
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search by name, email, phone or depot',
                    prefixIcon: Icon(Icons.search, color: widget.accentColor),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _filterRequests('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 16,
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide(color: Colors.grey.shade200),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide:
                          BorderSide(color: widget.accentColor, width: 2),
                    ),
                  ),
                  onChanged: _filterRequests,
                ),

                const SizedBox(height: 16),

                // Selection and action area
                Row(
                  children: [
                    // Select all checkbox
                    InkWell(
                      onTap: _selectAll,
                      borderRadius: BorderRadius.circular(8),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(
                                  color: isAllSelected
                                      ? widget.accentColor
                                      : Colors.grey,
                                  width: 2,
                                ),
                                color: isAllSelected
                                    ? widget.accentColor
                                    : Colors.transparent,
                              ),
                              child: isAllSelected
                                  ? const Icon(Icons.check,
                                      size: 16, color: Colors.white)
                                  : null,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              isAllSelected ? 'Deselect All' : 'Select All',
                              style: TextStyle(
                                color: isAllSelected
                                    ? widget.accentColor
                                    : Colors.grey.shade700,
                                fontWeight: isAllSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Show selection count and actions
                    if (selectedRequestIds.isNotEmpty)
                      Text(
                        '${selectedRequestIds.length} selected',
                        style: TextStyle(
                          color: widget.accentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),

          // Action Buttons
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: selectedRequestIds.isNotEmpty
                        ? () => _handleBulkAction('APPROVE_SELECTED')
                        : null,
                    icon: const Icon(Icons.check_circle_outline),
                    label: const Text('Approve Selected'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      disabledBackgroundColor: Colors.grey.shade300,
                      disabledForegroundColor: Colors.grey.shade500,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: filteredRequests.isNotEmpty
                        ? () => _handleBulkAction('APPROVE_ALL')
                        : null,
                    icon: const Icon(Icons.done_all),
                    label: const Text('Approve All'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      disabledBackgroundColor: Colors.grey.shade300,
                      disabledForegroundColor: Colors.grey.shade500,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Request count summary
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Requests: ${filteredRequests.length}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (_searchController.text.isNotEmpty &&
                    filteredRequests.length != widget.requests.length)
                  Text(
                    'Filtered from ${widget.requests.length}',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontStyle: FontStyle.italic,
                      fontSize: 12,
                    ),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // User Request List
          Expanded(
            child: _isProcessing
                ? Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(color: widget.accentColor),
                        const SizedBox(height: 16),
                        const Text('Processing requests...'),
                      ],
                    ),
                  )
                : filteredRequests.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.folder_open,
                                size: 64, color: Colors.grey.shade300),
                            const SizedBox(height: 16),
                            Text(
                              'No requests available',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 16,
                              ),
                            ),
                            if (_searchController.text.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: TextButton.icon(
                                  icon: const Icon(Icons.clear),
                                  label: const Text('Clear search'),
                                  onPressed: () {
                                    _searchController.clear();
                                    _filterRequests('');
                                  },
                                ),
                              ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: filteredRequests.length,
                        padding: const EdgeInsets.only(bottom: 16),
                        itemBuilder: (context, index) {
                          final user = filteredRequests[index];
                          final isSelected =
                              selectedRequestIds.contains(user.id);

                          return NewUserRequestListItem(
                            user: user,
                            isSelected: isSelected,
                            onToggleSelection: _toggleRequestSelection,
                            onApprove: (user) =>
                                _handleSingleUserAction('APPROVE', user),
                            onDeny: (user) =>
                                _handleSingleUserAction('DENY', user),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }
}
