import 'package:flutter/material.dart';
import 'package:railops/screens/request_user_management/widgets/new_user_request_details_screen.dart';
import 'package:railops/screens/request_user_management/widgets/update_request_details_screen.dart';
import 'package:railops/types/user_types/update_request_response.dart';
import 'package:railops/types/user_types/user_request_response.dart';

// Add these functions to the _RequestUserScreenState class

void showNewUserRequestsBottomSheet({
  required BuildContext context,
  required List<RequestedUserResponse> requests,
  required String title,
  required VoidCallback onRefresh,
  required Color accentColor,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: NewUserRequestDetailsScreen(
          requests: requests,
          title: title,
          onRefresh: onRefresh,
          accentColor: accentColor,
        ),
      ),
    ),
  );
}

void showUpdateRequestsBottomSheet({
  required BuildContext context,
  required List<UpdateRequest> requests,
  required String title,
  required VoidCallback onRefresh,
  required Color accentColor,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: UpdateRequestDetailsScreen(
          requests: requests,
          title: title,
          onRefresh: onRefresh,
          accentColor: accentColor,
        ),
      ),
    ),
  );
}