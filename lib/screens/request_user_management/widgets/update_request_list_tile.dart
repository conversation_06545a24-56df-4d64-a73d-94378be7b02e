import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:railops/types/index.dart';

class UpdateRequestListItem extends StatelessWidget {
  final UpdateRequest request;
  final bool isSelected;
  final Function(UpdateRequest) onToggleSelection;
  final Function(UpdateRequest) onApprove;
  final Function(UpdateRequest) onDeny;

  const UpdateRequestListItem({
    Key? key,
    required this.request,
    required this.isSelected,
    required this.onToggleSelection,
    required this.onApprove,
    required this.onDeny,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final current = request.user.currentDetails;
    final requested = request.user.requestedChanges;
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? Colors.blue : Colors.grey.shade200,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => onToggleSelection(request),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Selection indicator
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: isSelected ? Colors.blue : Colors.grey.shade400,
                        width: 2,
                      ),
                      color: isSelected ? Colors.blue : Colors.transparent,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, size: 18, color: Colors.white)
                        : null,
                  ),

                  const SizedBox(width: 12),

                  // User info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${current.fName ?? ""} ${current.lName ?? ""}',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(Icons.email_outlined,
                                size: 14, color: Colors.grey.shade600),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                request.user.email,
                                style: TextStyle(
                                  color: Colors.grey.shade700,
                                  fontSize: 14,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        if (request.user.phone.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2),
                            child: Row(
                              children: [
                                Icon(Icons.phone_outlined,
                                    size: 14, color: Colors.grey.shade600),
                                const SizedBox(width: 4),
                                Text(
                                  request.user.phone,
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        if (current.depo != null && current.depo!.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 2),
                            child: Row(
                              children: [
                                Icon(Icons.business_outlined,
                                    size: 14, color: Colors.grey.shade600),
                                const SizedBox(width: 4),
                                Text(
                                  current.depo!,
                                  style: TextStyle(
                                    color: Colors.grey.shade700,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            if (current.userType != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Role: ${current.userType ?? "Unknown"}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade700,
                                  ),
                                ),
                              ),
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.amber.shade50,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'Profile Update',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.deepPurpleAccent,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Update details
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Changed Fields:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: _buildOnlyChangedFields(),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Date info
              Row(
                children: [
                  Icon(Icons.calendar_today_outlined,
                      size: 14, color: Colors.grey.shade500),
                  const SizedBox(width: 4),
                  Text(
                    'Requested: ${DateFormat('MMM dd, yyyy').format(DateTime.parse(request.createdAt))}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton(
                    onPressed: () => onDeny(request),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    child: const Text('Deny'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () => onApprove(request),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                    ),
                    child: const Text('Approve'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildOnlyChangedFields() {
    final List<Widget> changedFields = [];
    final current = request.user.currentDetails;
    final requested = request.user.requestedChanges;

    // Helper function to safely compare nullable strings
    bool isStringDifferent(String? str1, String? str2) {
      // If both are null, they're equal
      if (str1 == null && str2 == null) return false;
      // If only one is null, they're different
      if (str1 == null || str2 == null) return true;
      // Compare non-null strings
      return str1 != str2;
    }

    // Helper function to check if a field has changed
    bool hasFieldChanged(String? requestedValue, String? currentValue) {
      // Empty string in requested_changes means field wasn't changed
      if (requestedValue == "") return false;
      // Field value is provided and different from current
      return requestedValue != null &&
          isStringDifferent(requestedValue, currentValue);
    }

    // Check each field independently (no else if)
    if (hasFieldChanged(requested.email, current.email)) {
      changedFields.add(_buildUpdateDetail('Email', requested.email!));
    }

    if (hasFieldChanged(requested.phone, current.phone)) {
      changedFields.add(_buildUpdateDetail('Phone', requested.phone!));
    }

    if (hasFieldChanged(requested.depo, current.depo)) {
      changedFields.add(_buildUpdateDetail('Depot', requested.depo!));
    }

    if (hasFieldChanged(requested.fName, current.fName)) {
      changedFields.add(_buildUpdateDetail('First Name', requested.fName!));
    }

    if (hasFieldChanged(requested.lName, current.lName)) {
      changedFields.add(_buildUpdateDetail('Last Name', requested.lName!));
    }

    if (hasFieldChanged(requested.whatsappNumber, current.whatsappNumber)) {
      changedFields
          .add(_buildUpdateDetail('WhatsApp', requested.whatsappNumber!));
    }

    if (hasFieldChanged(requested.zone, current.zone)) {
      changedFields.add(_buildUpdateDetail('Zone', requested.zone!));
    }

    if (hasFieldChanged(requested.division, current.division)) {
      changedFields.add(_buildUpdateDetail('Division', requested.division!));
    }

    if (hasFieldChanged(requested.userType, current.userType)) {
      changedFields.add(_buildUpdateDetail('Role', requested.userType!));
    }

    if (hasFieldChanged(requested.empNumber, current.empNumber)) {
      changedFields.add(_buildUpdateDetail('EmpNumber', requested.empNumber!));
    }

    // Special case for enabled (boolean)
    if (requested.enabled != current.enabled) {
      changedFields.add(_buildUpdateDetail(
          'Status', requested.enabled ? 'Enabled' : 'Disabled'));
    }

    // If no fields were changed, show a message
    if (changedFields.isEmpty) {
      changedFields.add(Container(
        padding: const EdgeInsets.all(8),
        child: Text(
          'No fields changed',
          style: TextStyle(
            fontStyle: FontStyle.italic,
            color: Colors.grey.shade600,
          ),
        ),
      ));
    }

    return changedFields;
  }

  Widget _buildUpdateDetail(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
