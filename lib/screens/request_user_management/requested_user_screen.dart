import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/request_user_management/widgets/request_details_button.dart';
import 'package:railops/screens/request_user_management/widgets/request_pannel.dart';
import 'package:railops/services/authentication_services/Request_update_service.dart';
import 'package:railops/services/authentication_services/index.dart';
import 'package:railops/types/index.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class RequestUserScreen extends StatefulWidget {
  const RequestUserScreen({Key? key}) : super(key: key);

  @override
  _RequestUserScreenState createState() => _RequestUserScreenState();
}

class _RequestUserScreenState extends State<RequestUserScreen> {
  String? token;
  List<RequestedUserResponse> newUserRequests = [];
  List<UpdateRequest> updateRequests = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchRequests();
    });
  }

  Future<void> _fetchRequests() async {
    if (!mounted) return;

    setState(() => _isLoading = true);
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      token = userModel.token;

      // Fetch new user registration requests
      final allNewUserRequests =
          await RequestedUserService.showRequests(token!).timeout(
        const Duration(seconds: 20),
        onTimeout: () => throw TimeoutException('Request timed out'),
      );

      try {
        // Add specific error handling for update requests
        final updateRequestsResponse =
            await UpdateUserService.getUpdateRequests(token!).timeout(
          const Duration(seconds: 20),
          onTimeout: () => throw TimeoutException('Request timed out'),
        );

        setState(() {
          updateRequests = updateRequestsResponse.updateRequests;
        });
      } catch (updateError) {
        _showErrorModal(
            context, 'Failed to fetch update requests: $updateError');
        // Still update the new user requests even if update requests fail
        setState(() {
          newUserRequests = allNewUserRequests;
        });
        return;
      }

      setState(() {
        newUserRequests = allNewUserRequests;
      });
    } catch (e) {
      _showErrorModal(context, 'An unexpected error occurred: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showErrorModal(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error', style: TextStyle(color: Colors.red)),
          content: Text(errorMessage),
          actions: <Widget>[
            TextButton(
              child: const Text('Retry'),
              onPressed: () {
                Navigator.of(context).pop();
                _fetchRequests();
              },
            ),
            TextButton(
              child: const Text('Close'),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Requested Users'),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _fetchRequests,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 24),
                  // Request summary
                  Text(
                    'Request Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[800],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // New User Requests Panel
                  RequestPanel(
                    title: 'New User Requests',
                    count: newUserRequests.length,
                    onTap: () => showNewUserRequestsBottomSheet(
                      context: context,
                      requests: newUserRequests,
                      title: 'New User Requests',
                      onRefresh: _fetchRequests,
                      accentColor: Colors.blue,
                    ),
                    accentColor: Colors.blue,
                  ),

                  const SizedBox(height: 16),

                  // Update Requests Panel
                  RequestPanel(
                    title: 'Update Requests',
                    count: updateRequests.length,
                    onTap: () => showUpdateRequestsBottomSheet(
                      context: context,
                      requests: updateRequests,
                      title: 'Update Requests',
                      onRefresh: _fetchRequests,
                      accentColor: Colors.blue,
                    ),
                    accentColor: Colors.blue,
                  ),

                  // Loading or No Requests Indicator
                  if (_isLoading)
                    const SizedBox(
                      height: 200,
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    )
                  else if (newUserRequests.isEmpty && updateRequests.isEmpty)
                    SizedBox(
                      height: 300,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.check_circle_outline,
                                size: 64, color: Colors.grey.shade300),
                            const SizedBox(height: 16),
                            const Text(
                              'No Pending Requests',
                              style: TextStyle(
                                fontSize: 18,
                                color: Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'All user requests have been processed',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
