import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/train_services/index.dart';

class AddTrainForm extends StatefulWidget {
  @override
  _AddTrainFormState createState() => _AddTrainFormState();
}

class Coach {
  final String name;
  final int id;

  Coach({required this.name, required this.id});

  @override
  String toString() {
    return 'Coach(name: $name, id: $id)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Coach && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class _AddTrainFormState extends State<AddTrainForm> {
  final _formKey = GlobalKey<FormState>();
  final controller = MultiSelectController<Coach>();

  String? _selectedTrainNumber;
  List<String> _trainNumbers = [];
  List<DropdownItem<Coach>> coachItems = [];
  DateTime? _selectedDate;

  bool _isSelectingAll = false;
  Set<Coach> _previousSelection = {};

  @override
  void initState() {
    super.initState();
    _fetchTrainNumbers();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {});
    } catch (e) {
      print('Error fetching train numbers: $e');
    }
  }

  Future<List<DropdownItem<Coach>>> fetchCoaches(String? trainNumber) async {
    if (trainNumber == null) return [];

    try {
      List<String> coachNames =
          await TrainServiceSignup.getCoaches(trainNumber);

      List<DropdownItem<Coach>> coachItems = [
        DropdownItem<Coach>(
          label: 'Select All',
          value: Coach(name: 'Select All', id: 0),
        ),
        ...coachNames.asMap().entries.map((entry) => DropdownItem<Coach>(
              label: entry.value,
              value: Coach(name: entry.value, id: entry.key + 1),
            ))
      ];

      controller.clearAll();
      controller.setItems(coachItems);
      return coachItems;
    } catch (e) {
      print('Error fetching coaches: $e');
      return [];
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    final coaches = await fetchCoaches(trainNumber);

    setState(() {
      _selectedTrainNumber = trainNumber;
      coachItems = coaches;
    });
  }

  Future<void> printSelectedFilters() async {
    final selectedTrainNumber = _selectedTrainNumber ?? 'No train selected';
    final selectedCoaches =
        controller.selectedItems.map((item) => item.value.name).toList();
    final selectedDate = _selectedDate != null
        ? "${_selectedDate!.year}-${_selectedDate!.month}-${_selectedDate!.day}"
        : 'No date selected';

    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    try {
      await TrainService.addTrainDetails(
        trainNumber: selectedTrainNumber,
        coachNumbers: selectedCoaches,
        originDate: selectedDate,
        token: token,
      );
      print('Train details added successfully');

      Navigator.pushNamed(context, '/add-train-profile');
    } catch (e) {
      print('Error adding train details: $e');

      showErrorDialog(
          context, 'An error occurred while adding train details: $e');
    }
  }

  void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Error'),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _refreshPage() async {
    await _fetchTrainNumbers();
    // You can also clear or fetch coach items if needed
    if (_selectedTrainNumber != null) {
      await fetchCoaches(_selectedTrainNumber);
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshPage,
      child: SingleChildScrollView(
        physics:
            const AlwaysScrollableScrollPhysics(), // Ensure it's always scrollable
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              // Train Number Dropdown with Search
              DropdownSearch<String>(
                items: _trainNumbers,
                onChanged: (value) {
                  _onTrainNumberChanged(value);
                },
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    labelText: 'Select Train Number',
                    border: OutlineInputBorder(),
                  ),
                ),
                popupProps: PopupProps.menu(
                  showSearchBox: true,
                  itemBuilder: (context, item, isSelected) {
                    return ListTile(
                      title: Text(item),
                      selected: isSelected,
                    );
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a train number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // MultiDropdown for Coaches with 'Select All' functionality
              MultiDropdown<Coach>(
                items: coachItems,
                controller: controller,
                enabled: coachItems.isNotEmpty,
                searchEnabled: true,
                chipDecoration: ChipDecoration(
                  backgroundColor: Colors.white,
                  border: Border.all(
                    color: Colors.blue,
                    width: 1.0,
                  ),
                  wrap: false,
                  runSpacing: 4,
                  spacing: 8,
                ),
                fieldDecoration: FieldDecoration(
                  hintText: 'Select Coaches',
                  hintStyle: const TextStyle(color: Colors.black54),
                  showClearIcon: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.blueAccent),
                  ),
                ),
                dropdownDecoration: const DropdownDecoration(
                  marginTop: 2,
                  maxHeight: 300,
                  header: Padding(
                    padding: EdgeInsets.all(8),
                    child: Text(
                      'Select coaches from the list',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                dropdownItemDecoration: DropdownItemDecoration(
                  selectedIcon:
                      const Icon(Icons.check_box, color: Colors.green),
                  disabledIcon: Icon(Icons.lock, color: Colors.grey.shade300),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select at least one coach';
                  }
                  return null;
                },
                onSelectionChange: (selectedItems) {
                  final selectedItemsSet = Set<Coach>.from(selectedItems);
                  final addedItems =
                      selectedItemsSet.difference(_previousSelection);
                  final removedItems =
                      _previousSelection.difference(selectedItemsSet);

                  if (addedItems.contains(DropdownItem<Coach>(
                    label: 'Select All',
                    value: Coach(name: 'Select All', id: 0),
                  ).value)) {
                    if (!_isSelectingAll) {
                      _isSelectingAll = true;
                      setState(() {
                        controller.selectAll();
                      });
                    }
                  } else if (removedItems.contains(DropdownItem<Coach>(
                    label: 'Select All',
                    value: Coach(name: 'Select All', id: 0),
                  ).value)) {
                    if (_isSelectingAll) {
                      _isSelectingAll = false;
                      setState(() {
                        controller.clearAll();
                      });
                    }
                  } else if (_isSelectingAll && removedItems.isNotEmpty) {
                    _isSelectingAll = false;
                    setState(() {
                      controller.clearAll();
                      controller.selectWhere((item) =>
                          selectedItemsSet.contains(item.value) &&
                          item.value.id != 0);
                    });
                  }

                  if (!(selectedItemsSet.length == 1 &&
                      selectedItemsSet.contains(DropdownItem<Coach>(
                        label: 'Select All',
                        value: Coach(name: 'Select All', id: 0),
                      ).value))) {
                    _previousSelection = selectedItemsSet;
                  }
                },
              ),
              const SizedBox(height: 16),
              // Date Picker
              TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Select Date',
                  border: OutlineInputBorder(),
                ),
                readOnly: true,
                onTap: () async {
                  DateTime? selectedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (selectedDate != null) {
                    setState(() {
                      _selectedDate = selectedDate;
                    });
                  }
                },
                controller: TextEditingController(
                  text: _selectedDate != null
                      ? "${_selectedDate!.day}-${_selectedDate!.month}-${_selectedDate!.year}"
                      : '',
                ),
                validator: (value) {
                  if (_selectedDate == null) {
                    return 'Please select a date';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Submit Button
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState?.validate() ?? false) {
                      printSelectedFilters(); // Call the function to print the selected filters
                    }
                  },
                  child: const Text('Add/Update'),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                    textStyle: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
