import 'package:flutter/material.dart';
import 'package:toggle_switch/toggle_switch.dart';

class ToggleSwitchWidget extends StatelessWidget {
  final ValueChanged<int>? onToggleCallback;
  final bool enabled;
  final int initialIndex; // Add initialIndex

  const ToggleSwitchWidget({
    Key? key,
    this.onToggleCallback,
    this.enabled = true,
    this.initialIndex = 1, // Default to 'False' if not specified
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      ignoring: !enabled,
      child: Opacity(
        opacity: enabled ? 1 : 0.5,
        child: ToggleSwitch(
          minWidth: 80.0,
          cornerRadius: 20.0,
          activeBgColors: enabled
              ? [[Colors.green[800]!], [Colors.red[800]!]]
              : [[Colors.green[500]!], [Colors.red[500]!]],
          activeFgColor: Colors.white,
          inactiveBgColor: enabled ? Colors.grey : Colors.grey.shade300,
          inactiveFgColor: Colors.white,
          initialLabelIndex: initialIndex, // Set initial index
          totalSwitches: 2,
          labels: ['True', 'False'],
          radiusStyle: true,
          onToggle: (index) {
            if (enabled && onToggleCallback != null) {
              onToggleCallback!(index!);
            }
          },
        ),
      ),
    );
  }
}