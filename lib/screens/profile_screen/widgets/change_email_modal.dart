import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:railops/models/index.dart'; // Assuming UserModel is defined here
import 'package:railops/services/profile_services/profile_services.dart'; // Assuming ProfileService is defined here

class ChangeEmailModal extends StatefulWidget {
  @override
  _ChangeEmailModalState createState() => _ChangeEmailModalState();
}

class _ChangeEmailModalState extends State<ChangeEmailModal> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _newEmailController;
  late TextEditingController _otpController;

  bool _isOTPSent = false;
  bool _isGeneratingOTP = false;
  bool _isVerifyingOTP = false;
  late Timer? _timer;
  int _timerDuration = 60;
  bool _isResendEnabled = false;

  @override
  void initState() {
    super.initState();
    _newEmailController = TextEditingController();
    _otpController = TextEditingController();
  }

  @override
  void dispose() {
    _newEmailController.dispose();
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _isResendEnabled = false;
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_timerDuration == 0) {
        setState(() {
          _isResendEnabled = true;
          _timer?.cancel();
        });
      } else {
        setState(() {
          _timerDuration--;
        });
      }
    });
  }

  Future<void> _generateOTP(UserModel userModel) async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isGeneratingOTP = true;
        _timerDuration = 60;
      });
      try {
        final token = userModel.token; // Access token from UserModel
        final data = {'email': _newEmailController.text};
        final message = await ProfileService.changeEmail(token, data); // Call the service
        setState(() {
          _isOTPSent = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('OTP sent successfully!')),
        );
        _startTimer();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to send OTP: $e')),
        );
      } finally {
        setState(() {
          _isGeneratingOTP = false;
        });
      }
    }
  }

  Future<void> _verifyOTP(UserModel userModel) async {
    if (_otpController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please enter the OTP')),
      );
      return;
    }
    setState(() {
      _isVerifyingOTP = true;
    });
    try {
      final token = userModel.token; // Access token from UserModel
      final data = {'email': _newEmailController.text, 'otp': _otpController.text};
      final message = await ProfileService.changeEmailOTP(token, data); // Call the service

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Email saved successfully!')),
      );
      Navigator.of(context).pop(); // Close the modal after success
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to verify OTP: $e')),
      );
    } finally {
      setState(() {
        _isVerifyingOTP = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userModel = Provider.of<UserModel>(context, listen: false); // Access UserModel

    return AlertDialog(
      title: Text(
        'Please Enter Your Email',
        style: TextStyle(fontSize: 16), // Adjust the font size here
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _newEmailController,
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a valid email';
                }
                if (!RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
                    .hasMatch(value)) {
                  return 'Invalid email format';
                }
                return null;
              },
            ),
            if (_isOTPSent) ...[
              SizedBox(height: 20),
              TextFormField(
                controller: _otpController,
                decoration: InputDecoration(
                  labelText: 'OTP',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: 20),
              ElevatedButton(
                onPressed: _isVerifyingOTP ? null : () => _verifyOTP(userModel),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: BorderSide(color: Colors.black, width: 0.5),
                  minimumSize: Size(double.infinity, 50),
                ),
                child: _isVerifyingOTP
                    ? CircularProgressIndicator(color: Colors.white)
                    : Text('Verify OTP'),
              ),
            ] else ...[
              SizedBox(height: 20),
              ElevatedButton(
                onPressed: _isGeneratingOTP ? null : () => _generateOTP(userModel),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: BorderSide(color: Colors.black, width: 0.5),
                  minimumSize: Size(double.infinity, 50),
                ),
                child: _isGeneratingOTP
                    ? CircularProgressIndicator(color: Colors.white)
                    : Text('Send OTP'),
              ),
            ],
            if (_isOTPSent) ...[
              SizedBox(height: 10),
              TextButton(
                onPressed: _isResendEnabled ? () => _generateOTP(userModel) : null,
                child: Text(_isResendEnabled
                    ? 'Resend OTP'
                    : 'Resend in $_timerDuration s'),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(); // Close the modal
          },
          child: Text('Cancel'),
        ),
      ],
    );
  }
}
