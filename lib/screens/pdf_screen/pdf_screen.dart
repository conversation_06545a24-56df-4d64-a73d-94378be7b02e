import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/pdf_screen/widgets/date_select.dart';
import 'package:railops/screens/pdf_screen/widgets/pdf_buttons.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/depot_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/division_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/train_number_dropdown.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';

class PdfScreen extends StatefulWidget {
  const PdfScreen({super.key});

  @override
  State<PdfScreen> createState() => _PdfScreenState();
}

class _PdfScreenState extends State<PdfScreen> {
  DateTime _selectedDate = DateTime.now();
  String _division = '', _depot = '', _selectedZones = '';
  List<String> depots = [];
  List<String> trainList = [];
  List<String> _trainNumbers = [];
  List<String> division_codes = [];
  bool showAll = false;
  bool showAllMonthly = false;
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();

  @override
  void initState() {
    super.initState();
     String storedDate = Provider.of<UserModel>(context, listen: false).selectedDate;
     if (storedDate.isNotEmpty) {
       _selectedDate = DateTime.parse(storedDate);
     }
  }


  void _handleDateSelected(DateTime date) {
    setState(() {
      _selectedDate = date;
    });
    Provider.of<UserModel>(context, listen: false).setSelectedDate(
      "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}"
    );
  }

  void _handleZoneSelection(String selectedZones) async {
    setState(() {
      _selectedZones = selectedZones;
    });

    if (_selectedZones.isNotEmpty) {
      try {
        List<ZoneDivision> divisions =
            await TrainServiceSignup.getDivisions(_selectedZones);
        List<String> divisionCodes =
            divisions.map((division) => division.code).toList();
        setState(() {
          division_codes = divisionCodes;
        });
      } catch (e) {
        print("Error fetching divisions: $e");
      }
    }
  }

  Future getDepot() async {
    try {
      if (_division.isEmpty) return;
      final getData = await TrainServiceSignup.getDepot(_division);
      setState(() {
        depots = getData;
      });
    } catch (e) {
      showErrorModal(context, '$e', "Error", () {});
    }
  }

  Future getTrainList() async {
    try {
      final getData = await TrainServiceSignup.getTrainList(_depot);
      setState(() {
        trainList = getData['trains'];
      });
    } catch (e) {
      showErrorModal(context, '$e', "Error", () {});
    }
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(context, const PdfScreen());
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Reports'),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        key: _refreshIndicatorKey,
        onRefresh: _reloadPage,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Card(
                surfaceTintColor: kWhiteColor,
                child:Column(
                children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: DateSelect(
                    onDateSelected: _handleDateSelected,
                    initialDate: _selectedDate,
                  ),
                ),

                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: ZoneDropdown(
                      onSaved: _handleZoneSelection,
                    ),
                  ),

                // Division dropdown
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: DivisionDropdown(
                    divisions: division_codes
                      ..sort(
                          (a, b) => a.toLowerCase().compareTo(b.toLowerCase())),
                    onSaved: (value) {
                      setState(() {
                        _division = value;
                        _depot = '';
                      });
                      getDepot();
                    },
                  ),
                ),
                // Depot dropdown
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: DepotDropdown(
                    depots: depots
                      ..sort(
                        (a, b) => a.toLowerCase().compareTo(b.toLowerCase()),
                      ),
                    onSaved: (value) {
                      setState(() {
                        _depot = value;
                      });
                      getTrainList();
                    },
                  ),
                ),

                // Train number dropdown
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: TrainNumberDropdown(
                    trainList: trainList,
                    onSaved: (value) {
                      setState(() {
                        _trainNumbers = value;
                      });
                    },
                  ),
                ),
              ]),
              ),
              SizedBox(height: 20),
              Card(
                surfaceTintColor: kWhiteColor,
                child: Container(
                  constraints: BoxConstraints(minHeight: showAll ? 300 : 200),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Daily Reports",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        // Replace GridView.count with GridView.builder with adjusted aspect ratio
                        GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio:
                                1, // Adjusted to make cells taller
                          ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(10),
                          itemCount:showAll ? 6 : 2,
                          itemBuilder: (context, index) {
                            // Define all buttons in an array
                            final buttons = [
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Attendance Report",
                                  urlLink: "/pdf/attendance_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Round Trip Report",
                                  urlLink: "/pdf/round_trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.cached,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const[
                                  'assets/images/round_trip/1.png',
                                  'assets/images/round_trip/2.png',
                                  'assets/images/round_trip/3.png',
                                  'assets/images/round_trip/4.png',
                                ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Train Report",
                                  urlLink: "/pdf/all_train_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.directions_transit,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/train_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Detailed Attendance Report",
                                  urlLink:
                                      "/pdf/get_detailed_attendance_by_date/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment_returned_rounded,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const[
                                  'assets/images/attendance/1.png',
                                  'assets/images/attendance/2.png',
                                  'assets/images/attendance/3.png',
                                  'assets/images/attendance/4.png',
                                  'assets/images/attendance/5.png',
                                  'assets/images/attendance/6.png',
                                ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Trip Report",
                                  urlLink: "/pdf/trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.swap_horiz,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/trip_report.png",
                                ),
                              ),
                              
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Detailed Round Trip Attendance Report",
                                  urlLink: "/pdf/detailed_round_trip_report/",
                                  isMail: false,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.library_books,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImageList: const[
                                  'assets/images/round_trip/1.png',
                                  'assets/images/round_trip/2.png',
                                  'assets/images/round_trip/3.png',
                                  'assets/images/round_trip/4.png',
                                ],
                                ),
                              ),
                            ];
                            return buttons[index];
                          },
                        ),
                        if (!showAll)
                          Align(
                            alignment: Alignment.centerRight,
                            child:TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAll = true;
                                });
                              },
                              icon: const Icon(Icons.expand_more),
                              label: const Text("Show More"),
                            ),
                          ), 
                        if (showAll)
                          Align(
                            alignment: Alignment.centerRight,
                            child:TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAll = false;
                                });
                              },
                              icon: const Icon(Icons.expand_less),
                              label: const Text("Show Less"),
                            ),
                          )
                          
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Card(
                surfaceTintColor: kWhiteColor,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Container(
                    constraints: BoxConstraints(minHeight: showAllMonthly ?  200 : 150),
                    child: Column(
                      children: [
                        const Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "Monthly Reports",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        GridView.builder(
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                            childAspectRatio:
                                0.95, // Adjusted to make cells taller
                          ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(10.0),
                          itemCount:showAllMonthly ? 4 : 2,
                          itemBuilder: (context, index) {
                            final buttons = [
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly Attendance Report",
                                  urlLink: "/pdf/get_monthly_attendance_report/",
                                  isMail: false,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.assignment,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly Round Trip Attendance Report",
                                  urlLink: "/pdf/monthly_round_trip_report/",
                                  isMail: false,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  division: _division,
                                  buttonIcon: const Icon(
                                    Icons.cached,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/attendance_report.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly With Mobile",
                                  urlLink: "/pdf/monthly_obhs_report_links/",
                                  isMail: true,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  showPhoneColumn: true,
                                  division: _division,
                                  showDownloadButtonForMonthly: false,
                                  buttonIcon: const Icon(
                                    Icons.smartphone,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/monthly_report_with_mobile.png",
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.all(5.0),
                                child: PdfButtons(
                                  date: _selectedDate
                                      .toIso8601String()
                                      .split('T')
                                      .first,
                                  buttonText: "Monthly Without Mobile",
                                  urlLink: "/pdf/monthly_obhs_report_links/",
                                  isMail: true,
                                  directMail: true,
                                  trainNumbers: _trainNumbers,
                                  showPhoneColumn: false,
                                  division: _division,
                                  showDownloadButtonForMonthly: false,
                                  buttonIcon: const Icon(
                                    Icons.no_cell,
                                    size: 40,
                                    color: kBlueColor,
                                  ),
                                  pdfPreviewImagePath:
                                      "assets/images/monthly_report_without_phone.png",
                                ),
                              ),
                            ];
                            return buttons[index];
                          },

                        ),
                        if (!showAllMonthly)
                          Align(
                            alignment: Alignment.centerRight,
                            child:TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAllMonthly = true;
                                });
                              },
                              icon: const Icon(Icons.expand_more),
                              label: const Text("Show More"),
                            ),
                          ), 
                        if (showAllMonthly)
                          Align(
                            alignment: Alignment.centerRight,
                            child:TextButton.icon(
                              onPressed: () {
                                setState(() {
                                  showAllMonthly = false;
                                });
                              },
                              icon: const Icon(Icons.expand_less),
                              label: const Text("Show Less"),
                            ),
                          )
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}
