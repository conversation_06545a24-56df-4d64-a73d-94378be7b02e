import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/add_user/add_user_from.dart';
import 'package:railops/screens/user_screen/form/signup_form.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class AddUserScreen extends StatefulWidget {
  const AddUserScreen({super.key});

  @override
  _AddUserScreenState createState() => _AddUserScreenState();
}

class _AddUserScreenState extends State<AddUserScreen> {
  @override
  void initState() {
    super.initState();
    _checkAdminAuthentication();
  }

  void _checkAdminAuthentication() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final authModel = Provider.of<AuthModel>(context, listen: false);
        if (!authModel.isAuthenticated) {
          Navigator.pushReplacementNamed(context, Routes.home);
        }
      } catch (e) {
        debugPrint('Admin authentication check failed: $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Add New User"),
      drawer: const CustomDrawer(),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16.0),
                // decoration: const BoxDecoration(
                //   color: Colors.white,
                //   borderRadius: BorderRadius.vertical(
                //     top: Radius.circular(40),
                //   ),
                // ),
                child: SingleChildScrollView(
                  child: Container(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context)
                          .size
                          .height, // Ensures it fills screen height
                    ),
                    child: const Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        AddUserFrom(), // Reusing SignupForm here
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
