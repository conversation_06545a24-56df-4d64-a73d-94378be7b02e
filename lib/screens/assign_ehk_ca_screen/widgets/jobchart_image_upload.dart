import 'dart:io';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart';
import 'package:railops/screens/attendance/uploaded_image.dart';
import 'package:railops/services/assign_ehk_ca_services/jobchart_image_upload.dart';
import 'package:railops/services/upload_services/upload_services.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/widgets/error_modal.dart';

class JobChartImageUploadPage extends StatefulWidget {
  final String trainNumber;
  final String journeyDate;
  final String uploadedFor;
  final String status;

  const JobChartImageUploadPage({
    Key? key,
    required this.trainNumber,
    required this.journeyDate,
    required this.uploadedFor,
    required this.status
  }) : super(key: key);

  @override
  _JobChartImageUploadPageState createState() => _JobChartImageUploadPageState();
}

class _JobChartImageUploadPageState extends State<JobChartImageUploadPage> {
  XFile? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isImageLoading = false;
  String? token;
  List<JobchartType> uploadedImages = [];

  @override
  void initState() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _fetchUploadedImages();
    super.initState();
  }


  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await Geolocator.requestPermission();
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
      }
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      showErrorModal(
          context, 'Please Enable the location service!', "Error", () {});
      await Geolocator.requestPermission();
      return await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.high);
    }
  }

  Future<void> _pickImage() async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo),
              title: const Text('Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8), // Adjust the radius here
        ),
      ),
    );

    if (source != null) {
      final pickedImage = await _imagePicker.pickImage(source: source);
      if (pickedImage != null) {
        setState(() {
          _selectedImage = pickedImage;
        });
      }
    }
  }

  Future<void> _uploadImageAndCreateJobChart() async {
    if (_selectedImage != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return const Dialog(
            child: Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text("Submitting..."),
                ],
              ),
            ),
          );
        },
      );

      String trainNumber = widget.trainNumber;
      String date = widget.journeyDate;

      Position? position = await _getCurrentPosition();

      try {
        final response = await JobchartImageUploadService.createJobChart(
            imageFile: _selectedImage!,
            trainNumber: trainNumber,
            date: date,
            token: token!,
            latitude: position!.latitude.toString(),
            longitude: position!.longitude.toString(),
            uploadedFor: widget.uploadedFor
          );

        if (response != null) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text("Image upload initiated successfully"),
          ));
        } else {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Failed to upload image'),
          ));
        }
        _fetchUploadedImages();
        setState(() {
          _selectedImage = null;
        });
        Navigator.of(context).pop();
      } catch (e) {
        setState(() {
          _selectedImage = null;
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('Error: $e'),
        ));
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        content: Text('Please select an image to upload'),
      ));
    }
  }

  Future<void> _fetchUploadedImages({String? selectedUser}) async {
    
    try {
      setState(() {
        _isImageLoading = true;
      });

      final response = await JobchartImageUploadService.fetchJobChart(
        trainNumber: widget.trainNumber,
        date: widget.journeyDate,
        token: token!,
        uploadedFor: widget.uploadedFor,
      );

      if (response != null) {
        setState(() {
          uploadedImages = response;
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No images available')),
        );
      }

      setState(() {
        _isImageLoading = false;
      });
    } catch (e) {
      setState(() {
        _isImageLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error fetching images: $e')),
      );
    }
  }

  Future<void> _handleRefresh() async {
    _fetchUploadedImages();
    setState(() {
        uploadedImages = [];
        _selectedImage = null;
      });

    await Future.delayed(const Duration(seconds: 2));
  }

  Future<void> _deleteJobChartImage(int id) async {
    try {
      final deleted = await JobchartImageUploadService.deleteJobChart(
        id: id,
        token: token!,
      );
      if (deleted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('JobChart deleted successfully')),
        );
        _fetchUploadedImages(); // Refresh list
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to delete JobChart')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    double availableSpace = MediaQuery.of(context).size.width * 0.8;
    return Scaffold(
        appBar: const CustomAppBar(title: "Upload Image"),
        drawer: const CustomDrawer(),
        body: RefreshIndicator(
          onRefresh: _handleRefresh,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Train Details Card
                Card(
                  elevation: 5,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDetailRow('Train Number:', widget.trainNumber),
                        _buildDetailRow('Journey Date:', widget.journeyDate),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                widget.status == "completed"
                  ? Container(
                      padding: const EdgeInsets.all(8),
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        color: const Color.fromARGB(255, 245, 190, 187),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        "This train is marked as completed, cannot be updated",
                        style: TextStyle(
                          color: Color.fromARGB(255, 237, 39, 39),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),

                if(widget.status == "pending")
                // Image Display Area
                  Container(
                    height: 200,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[200],
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: _selectedImage != null
                          ? kIsWeb
                              ? Image.network(_selectedImage!.path) // For Web
                              : Image.file(
                                  File(_selectedImage!.path)) // For Mobile
                          : Center(
                              child: Text(
                                'No image selected',
                                style: TextStyle(
                                  color: Colors.grey[700],
                                  fontSize: 16,
                                ),
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(height: 20),
                
                if(widget.status == "pending")
                // Image Picker Button
                  ElevatedButton.icon(
                    onPressed: _pickImage,
                    icon: const Icon(Icons.photo_camera, color: Colors.blue,),
                    label: const Text('Pick Image'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      backgroundColor: Colors.white,
                      foregroundColor: Colors.black87,
                      side: const BorderSide(color: Colors.black87, width: 0.5),
                    ),
                  ),
                  const SizedBox(height: 20),
                
                if(widget.status == "pending")
                // Upload Button
                  ElevatedButton.icon(
                    onPressed: _selectedImage != null
                        ? _uploadImageAndCreateJobChart
                        : () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Please select an image to upload'),
                              ),
                            );
                          },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      side: const BorderSide(color: Colors.black87, width: 0.5),
                    ),
                    icon: const Icon(Icons.upload, color: Colors.blue,),
                    label: const Text('Upload Image'),
                  ),

                Container(
                  width: availableSpace,
                  margin: const EdgeInsets.all(20),
                  child: Card(
                    margin: EdgeInsets.zero,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          const Text(
                            'Uploaded Images',
                            style: TextStyle(
                              decoration: TextDecoration.underline,
                              fontSize: 18,
                            ),
                          ),
                          Column(
                            children: [
                              if (_isImageLoading)
                                const CircularProgressIndicator(
                                    color: Colors.blueAccent),
                              if (!_isImageLoading && uploadedImages != null)
                                ...uploadedImages!
                                    .where(
                                        (imageData) => imageData.imageUrl != "")
                                    .map((imageData) {
                                    return JobchartUploadedImage(
                                      imageResponse: imageData,
                                      onDelete: (id) => _deleteJobChartImage(id), // <-- Pass callback here
                                    );
                                }).toList(),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  // Helper function for creating detail rows
  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
