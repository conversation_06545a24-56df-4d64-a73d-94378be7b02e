import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/train_services/train_details_services.dart';

class AssignEhkCaFilters extends StatefulWidget {
  final void Function(String? trainNumber, String? date) onSubmit;

  const AssignEhkCaFilters({Key? key, required this.onSubmit})
      : super(key: key);

  @override
  _AssignEhkCaFiltersState createState() => _AssignEhkCaFiltersState();
}

class _AssignEhkCaFiltersState extends State<AssignEhkCaFilters> {
  List<String> _trainNumbers = [];
  String? _selectedTrainNumber;
  String? _trainName;
  bool loading=false;
  DateTime? _selectedDate=DateTime.now();
  final TextEditingController _trainNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchTrainNumbers();
    _setInitialTrainNoFromUserModel();
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;
    final initialSelectedDate = userModel.selectedDate;
    if(initialSelectedDate.isNotEmpty){
      setState(() {
        _selectedDate = DateTime.parse(initialSelectedDate);
      });
    }
    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  @override
  void dispose() {
    _trainNameController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        loading=true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        loading=false;
      });
    } catch (e) {
      print('Error fetching train numbers: $e');
      setState(() {
        loading=false;
      });
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) {
      final trainName = await fetchTrainName(trainNumber);
      setState(() {
        _selectedTrainNumber = trainNumber;
        _trainName = trainName;
        _trainNameController.text = trainName ?? '';
      });
      Provider.of<UserModel>(context, listen: false).setTrainNo(trainNumber);
    }
  }

  Future<String?> fetchTrainName(String trainNumber) async {
    return await TrainService.getTrainName(trainNumber);
  }

  void _submitForm() {
    if (_selectedTrainNumber != null && _selectedDate != null) {
      final formattedDate =
          "${_selectedDate!.year}-${_selectedDate!.month}-${_selectedDate!.day}";
      widget.onSubmit(_selectedTrainNumber, formattedDate);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select all fields before submitting.')),
      );
    }
  }

  Future<void> _refreshPage() async {
    await _fetchTrainNumbers();
    setState(() {
      _selectedTrainNumber = null;
      _trainName = null;
      _selectedDate = null;
      _trainNameController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshPage,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              children: [
                Flexible(
                  flex: 2,
                  child: DropdownSearch<String>(
                    items: _trainNumbers,
                    selectedItem: _selectedTrainNumber,
                    onChanged: _onTrainNumberChanged,
                    dropdownDecoratorProps: const DropDownDecoratorProps(
                      dropdownSearchDecoration: InputDecoration(
                        labelText: 'Train Number',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                    ),
                    popupProps: PopupProps.menu(
                      showSearchBox: true,
                      searchFieldProps: TextFieldProps(
                        keyboardType: TextInputType.number,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(6),
                        ]
                      ),
                      itemBuilder: (context, item, isSelected) {
                        return ListTile(
                          title:
                              Text(item, style: const TextStyle(fontSize: 14)),
                          selected: isSelected,
                        );
                      },
                    ),
                    dropdownButtonProps: DropdownButtonProps(
                        icon: loading
                            ? const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                              )
                            : const Icon(Icons.arrow_drop_down),
                      ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a train number';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Flexible(
                  flex: 3,
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Train Name',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    readOnly: true,
                    controller: _trainNameController,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Select Date',
                border: OutlineInputBorder(),
              ),
              readOnly: true,
              onTap: () async {
                DateTime? selectedDate = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );
                if (selectedDate != null) {
                  setState(() {
                    _selectedDate = selectedDate;
                  });
                  Provider.of<UserModel>(context, listen: false).setSelectedDate(
                    "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}"
                  );
                }
              },
              controller: TextEditingController(
                text: _selectedDate != null
                    ? "${_selectedDate!.day.toString().padLeft(2, '0')}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.year}"
                    : '',
              ),
              validator: (value) {
                if (_selectedDate == null) {
                  return 'Please select a date';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: ElevatedButton(
                  onPressed: _submitForm,
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.black87,
                    backgroundColor: Colors.white,
                    side: BorderSide(color: Colors.black87, width: 0.5),
                  ),
                  child: const Text(
                    'Submit',
                    style: TextStyle(
                      fontSize: 16.0,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
