import 'package:flutter/material.dart';
import 'package:railops/screens/pages/image_detail_page.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:intl/intl.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';

class JobchartUploadedImage extends StatefulWidget {
  final JobchartType imageResponse;
  final Function(int id)? onDelete; // <-- Added

  const JobchartUploadedImage({
    Key? key,
    required this.imageResponse,
    this.onDelete, // <-- Added
  }) : super(key: key);

  @override
  _JobchartUploadedImageState createState() => _JobchartUploadedImageState();
}

class _JobchartUploadedImageState extends State<JobchartUploadedImage> {
  String? stationCode;
  String? distance;

  @override
  void initState() {
    super.initState();
    _fetchNearestStation();
  }

  Future<void> _fetchNearestStation() async {
    if (widget.imageResponse.latitude == null ||
        widget.imageResponse.longitude == null) {
      return; // Skip if lat/lng is missing
    }
    try {
      final response = await LocationService.getNearestStation(
        widget.imageResponse.latitude.toString(),
        widget.imageResponse.longitude.toString(),
      );
      setState(() {
        stationCode = response['station_code'] ?? 'N/A';
        distance = response['distance_km']?.toStringAsFixed(2) ?? 'N/A';
      });
    } catch (e) {
      setState(() {
        stationCode = 'Error';
        distance = 'Error';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime createdAt = DateTime.parse(widget.imageResponse.createdAt);
    DateTime localTime = createdAt.toLocal();
    String formattedDate =
        DateFormat('yyyy-MM-dd - kk:mm:ss').format(localTime);

    return GestureDetector(
      onTap: () {
        ImageResponse detailPageImageResponse =
            ImageResponse.fromJobchartType(widget.imageResponse);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                ImageDetailPage(imageResponse: detailPageImageResponse),
          ),
        );
      },
      child: Card(
        margin: const EdgeInsets.all(5),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.imageResponse.imageUrl,
                  width: double.infinity,
                  height: 200,
                  fit: BoxFit.cover,
                  errorBuilder: (BuildContext context, Object exception,
                      StackTrace? stackTrace) {
                    return Container(
                      width: double.infinity,
                      height: 200,
                      color: Colors.grey,
                      child: const Icon(Icons.error, color: Colors.red),
                    );
                  },
                ),
              ),
              const SizedBox(height: 8),
              Text('Uploaded at: $formattedDate', style: const TextStyle(fontSize: 14)),
              Text('Uploaded by: ${widget.imageResponse.createdBy}', style: const TextStyle(fontSize: 14)),
              Text('Latitude: ${widget.imageResponse.latitude ?? 'N/A'}', style: const TextStyle(fontSize: 14)),
              Text('Longitude: ${widget.imageResponse.longitude ?? 'N/A'}', style: const TextStyle(fontSize: 14)),
              Text('Station Code: ${stationCode ?? 'Fetching...'}', style: const TextStyle(fontSize: 14)),
              Text('id: ${widget.imageResponse.id}', style: const TextStyle(fontSize: 14)),
              Text('Distance: ${distance ?? 'Fetching...'} km', style: const TextStyle(fontSize: 14)),
              if (widget.imageResponse.coach != null)
                Text('Coach: ${widget.imageResponse.coach}', style: const TextStyle(fontSize: 14)),
              if (widget.imageResponse.issue != null)
                Text('Issue: ${widget.imageResponse.issue}', style: const TextStyle(fontSize: 14)),
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFE5E5), // Light red background
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red), // Red border
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () async {
                              final confirm = await showDialog<bool>(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text("Delete Confirmation"),
                                  content: const Text("Are you sure you want to delete this image?"),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context, false),
                                      child: const Text("Cancel"),
                                    ),
                                    TextButton(
                                      onPressed: () => Navigator.pop(context, true),
                                      child: const Text("Delete"),
                                    ),
                                  ],
                                ),
                              );

                              if (confirm == true && widget.onDelete != null) {
                                widget.onDelete!(widget.imageResponse.id!);
                              }
                            },
                          ),
                          const Text(
                            "Delete",
                            style: TextStyle(color: Colors.red, fontSize: 13),
                          ),
                          const SizedBox(width: 8,)
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
