import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // Import intl for date formatting
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/screens/edit_train/widgets/index.dart';
import 'package:railops/widgets/error_modal.dart';

class ReturnGap extends StatefulWidget {
  final Function(int? gap, String? inOut) onSubmit;
  final ReturnGapData? returnGapData;
  final bool? isFormEnabled;

  ReturnGap({super.key, required this.onSubmit, this.returnGapData, required this.isFormEnabled});

  @override
  _ReturnGapState createState() => _ReturnGapState();
}

class _ReturnGapState extends State<ReturnGap> {
  final _formKey = GlobalKey<FormState>();
  String? _selectedGap;
  String? userType;
  String? _inOut;
  bool? isFormEnabled;

  @override
  void initState() {
    super.initState();
    _selectedGap = widget.returnGapData?.days?.toString();
    _inOut = widget.returnGapData?.inOut?.toString();
    isFormEnabled = widget.isFormEnabled;
    final userModel = Provider.of<UserModel>(context, listen: false);
    userType = userModel.userType;
  }

  @override
  void didUpdateWidget(covariant ReturnGap oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.returnGapData?.days != widget.returnGapData?.days || oldWidget.returnGapData?.inOut != widget.returnGapData?.inOut) {
       setState(() {
        _selectedGap = widget.returnGapData?.days?.toString();
        _inOut = widget.returnGapData?.inOut?.toString();
        isFormEnabled = widget.isFormEnabled;
      });
    }
  }

  String _formatDate(String? date) {
    if (date == null) return "N/A";
    try {
      final parsedDate = DateTime.parse(date).toLocal(); // Convert to local time
      return DateFormat.yMMMd().add_jm().format(parsedDate); // e.g., "Sep 7, 2024 5:08 PM"
    } catch (e) {
      return date; // If parsing fails, return the original date string
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      widget.onSubmit(int.tryParse(_selectedGap ?? "0"), _inOut);
      print(widget.returnGapData?.days ?? "No days data");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 16.0),
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.only(bottom: 16.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              if(! ['EHK', 'coach attendent'].contains(userType))
                Form(
                  key: _formKey,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          value: _selectedGap,
                          items: List.generate(8, (index) {
                            final gap = (index).toString();
                            return DropdownMenuItem(
                              value: gap,
                              child: Text(gap),
                            );
                          }),
                          decoration: InputDecoration(
                            labelText: 'Return Gap (Days)',
                            border: OutlineInputBorder(),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          onChanged: (value) {
                            setState(() {
                              _selectedGap = value;
                            });
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please select a return gap in days';
                            }
                            return null;
                          },
                        ),
                      ),

                      SizedBox(width: 8),

                      Expanded(
                        child: CustomDropdownField(
                          value: _inOut,
                          items: ['in', 'out'],
                          onChanged: (value) => setState(() => _inOut = value),
                          labelText: 'In/Out',
                          right: 4.0,
                          left: 0.0,
                          top: 8.0,
                          bottom: 8.0,
                        ),
                      ),


                      SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: isFormEnabled! ? _submitForm : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade100, 
                          foregroundColor: Colors.black, 
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20), 
                          ),
                          minimumSize: Size(50, 50), 
                          side: BorderSide(color: Colors.black87, width: 0.5),
                        ),
                        child: Text('Save'),
                      ),
                    ],
                  ),
                ),
               const SizedBox(height: 16),
               Align(
                alignment: Alignment.centerRight,
                child:
                InkWell(
                  onTap: () {
                    showErrorModal(context, 
                    "Return gap (Days): ${widget.returnGapData?.days ?? "N/A"}\nUpdated By: ${widget.returnGapData?.updatedBy ?? "N/A"}\nUpdated At: ${_formatDate(widget.returnGapData?.updatedAt)}"
                    , "Details", (){});
                  },
                  child: Icon(Icons.info, color: Colors.grey),
                ),
               ),
            ],
          ),
        ),
      ),
    );
  }
}
