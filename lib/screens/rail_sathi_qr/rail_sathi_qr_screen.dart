import 'package:flutter/material.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:url_launcher/url_launcher.dart';

class RailSathiQrScreen extends StatelessWidget {
  const RailSathiQrScreen({Key? key}) : super(key: key);

  final String qrImagePath = 'assets/images/rail_sathi_qr.png';
  final String linkUrl = 'https://ro.suvidhaen.com/';
  final bool isNetworkImage = false; 

  void _launchURL(BuildContext context) async {
    final Uri url = Uri.parse(linkUrl);
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Could not launch the link')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: 'Rail Sathi'),
      drawer: const CustomDrawer(),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child: Image.asset(qrImagePath, width: 200, height: 200),
          ),
          const SizedBox(height: 40),
          InkWell(
            onTap: () => _launchURL(context),
            child: Text(
              linkUrl,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.blue,
                decoration: TextDecoration.underline,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
