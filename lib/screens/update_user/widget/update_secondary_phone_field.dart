import 'package:flutter/material.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/signup_error.dart';

class UpdateSecondaryPhoneField extends StatefulWidget {
  final Function(String) onSaved;
  final Function(String) onChanged;
  final String initialValue;
  final bool readOnly;
  final bool enabled;
  final String? Function(String?)? validator;

  const UpdateSecondaryPhoneField({
    Key? key,
    required this.onSaved,
    required this.onChanged,
    this.initialValue = '',
    this.readOnly = false,
    this.enabled = true,
    this.validator,
  }) : super(key: key);

  @override
  State<UpdateSecondaryPhoneField> createState() =>
      _UpdateSecondaryPhoneFieldState();
}

class _UpdateSecondaryPhoneFieldState extends State<UpdateSecondaryPhoneField> {
  late TextEditingController _controller;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String? _validateField(String? value) {
    // If the field is empty, it's valid (since it's optional)
    if (value == null || value.trim().isEmpty) {
      return null;
    }
    
    // Only validate if there's a value
    if (value.length != 10) {
      return 'Secondary phone number must be 10 digits';
    }
    if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      return 'Secondary phone number must contain only digits';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      enabled: widget.enabled && !widget.readOnly,
      readOnly: widget.readOnly,
      keyboardType: TextInputType.phone,
      decoration: InputDecoration(
        labelText: 'Secondary Phone Number (Optional)',
        hintText: 'Enter secondary phone number (Optional)',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.blue, width: 2.0),
        ),
        suffixIcon: _controller.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  setState(() {
                    _controller.clear();
                    widget.onChanged('');
                  });
                },
              )
            : null,
      ),
      onSaved: (value) {
        widget.onSaved(value ?? '');
      },
      onChanged: (value) {
        setState(() {
          _errorMessage = null;
        });
        widget.onChanged(value);
      },
      validator: widget.validator ?? _validateField,
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }
} 
