import 'package:flutter/material.dart';


class UpdateMobileNumberField extends StatefulWidget {
  final void Function(String) onSaved;
  final void Function(String) onChanged;
  final String? initialValue;

  const UpdateMobileNumberField({
    super.key, 
    required this.onSaved,
    required this.onChanged,
    this.initialValue
  });

  @override
  _UpdateMobileNumberFieldState createState() => _UpdateMobileNumberFieldState();
}


class _UpdateMobileNumberFieldState extends State<UpdateMobileNumberField> {
  late TextEditingController _controller;
  String? _mobileNumber;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _mobileNumber = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant UpdateMobileNumberField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialValue != oldWidget.initialValue) {
      _controller.text = widget.initialValue ?? '';
      _mobileNumber = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleMobileNumberChange(String value) async {
    if (value.length == 10) {
      setState(() {
        _isLoading = true;
      });

      try {
        widget.onChanged(value);
      } catch (e) {
        print('Error fetching user details: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.centerRight,
      children: [
        TextFormField(
          controller: _controller,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            labelText: 'Mobile Number *',
            hintText: 'Enter mobile number to fetch details',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.blue, width: 2.0),
            ),
            suffixIcon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12.0),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                : (_mobileNumber != null && _mobileNumber!.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _controller.clear();
                            _mobileNumber = null;
                          });
                          widget.onSaved('');
                        },
                      )
                    : null),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your mobile number';
            }
            if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
              return 'Please enter a valid 10-digit mobile number';
            }
            return null;
          },
          onChanged: _handleMobileNumberChange,
          onSaved: (value) {
            if (value != null) {
              widget.onSaved(value);
            }
          },
        ),
      ],
    );
  }
}