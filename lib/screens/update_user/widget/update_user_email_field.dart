import 'package:flutter/material.dart';

class UpdateE<PERSON><PERSON>ield extends StatefulWidget {
  final void Function(String) onSaved;
  final String? initialValue;

  const UpdateEmailField({super.key, required this.onSaved, this.initialValue});

  @override
  _UpdateEmailFieldState createState() => _UpdateEmailFieldState();
}

class _UpdateEmailFieldState extends State<UpdateEmailField> {
  late TextEditingController _controller;
  String? _email;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _email = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant UpdateEmailField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialValue != oldWidget.initialValue) {
      _controller.text = widget.initialValue ?? '';
      _email = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextForm<PERSON>ield(
      controller: _controller,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: 'Email *',
        hintText: 'Enter your email address',
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.grey,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(
            color: Colors.blue,
            width: 2.0,
          ),
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter your email address';
        } else if (!RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .hasMatch(value)) {
          return 'Please enter a valid email address';
        }
        return null;
      },
      onChanged: (String value) {
        setState(() {
          _email = value;
        });
        widget.onSaved(value);
      },
    );
  }
}
