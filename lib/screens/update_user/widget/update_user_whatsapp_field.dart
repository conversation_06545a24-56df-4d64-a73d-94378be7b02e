import 'package:flutter/material.dart';

class UpdateWhatsappNumberField extends StatefulWidget {
  final void Function(String) onSaved;
  final void Function(String)? onChanged;
  final String? initialValue;
  final bool readOnly;
  final bool enabled;

  const UpdateWhatsappNumberField({
    super.key,
    required this.onSaved,
    this.onChanged,
    this.initialValue,
    this.readOnly = false,
    this.enabled = true,
  });

  @override
  _UpdateWhatsappNumberFieldState createState() =>
      _UpdateWhatsappNumberFieldState();
}

class _UpdateWhatsappNumberFieldState extends State<UpdateWhatsappNumberField> {
  late TextEditingController _controller;
  String? _whatsappNumber;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
    _whatsappNumber = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant UpdateWhatsappNumberField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialValue != oldWidget.initialValue) {
      _controller.text = widget.initialValue ?? '';
      _whatsappNumber = widget.initialValue;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleWhatsappNumberChange(String value) async {
    if (widget.readOnly || widget.enabled) return;

    if (value.length == 10) {
      setState(() {
        _isLoading = true;
      });

      try {
        if (widget.onChanged != null) {
          widget.onChanged!(value);
        }
      } catch (e) {
        print('Error handling WhatsApp number change: $e');
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Color borderColor = widget.enabled ? Colors.grey : Colors.grey.shade400;
    return Stack(
      alignment: Alignment.centerRight,
      children: [
        TextFormField(
          readOnly: widget.readOnly,
          enabled: widget.enabled,
          controller: _controller,
          keyboardType: TextInputType.phone,
          decoration: InputDecoration(
            labelText: 'WhatsApp Number *',
            hintText: widget.enabled
                ? 'Enter whatsapp number'
                : 'Same as phone number',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: widget.readOnly ? Colors.grey.shade400 : Colors.grey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: widget.readOnly ? Colors.grey.shade400 : Colors.grey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                  color: widget.readOnly ? Colors.grey.shade400 : Colors.blue,
                  width: 2.0),
            ),
            suffixIcon: widget.enabled && !widget.readOnly
                ? (_isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: Padding(
                          padding: EdgeInsets.all(12.0),
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      )
                    : (_whatsappNumber != null && _whatsappNumber!.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _controller.clear();
                                _whatsappNumber = null;
                              });
                              widget.onSaved('');
                            },
                          )
                        : null))
                : null,
          ),
          validator: (widget.enabled && !widget.readOnly)
              ? (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your WhatsApp number';
                  }
                  if (!RegExp(r'^[0-9]{10}$').hasMatch(value)) {
                    return 'Please enter a valid 10-digit WhatsApp number';
                  }
                  return null;
                }
              : null,
          onChanged: _handleWhatsappNumberChange,
          onSaved: (value) {
            if (value != null) {
              widget.onSaved(value);
            }
          },
        ),
      ],
    );
  }
}
