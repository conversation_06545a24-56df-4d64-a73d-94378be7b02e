import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/update_user/update_user_form.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/user_info_services/user_info_services.dart';
import 'package:railops/types/user_types/user_info.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:railops/widgets/error_modal.dart';

class UpdateUserScreen extends StatefulWidget {
  final String? mobileNumber;

  const UpdateUserScreen({super.key, this.mobileNumber});

  @override
  _UpdateUserScreenState createState() => _UpdateUserScreenState();
}

class _UpdateUserScreenState extends State<UpdateUserScreen> {
  late String _mobileNumber = '';
  bool _isLoading = false;
  Map<String, dynamic>? _userData;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _checkAdminAuthentication();

    if (widget.mobileNumber != null && widget.mobileNumber!.isNotEmpty) {
      _mobileNumber = widget.mobileNumber!;
      _controller.text = _mobileNumber;
      WidgetsBinding.instance.addPostFrameCallback((_) => _searchUser());
    }
  }

  void _checkAdminAuthentication() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final authModel = Provider.of<AuthModel>(context, listen: false);
        if (!authModel.isAuthenticated) {
          Navigator.pushReplacementNamed(context, Routes.home);
        }
      } catch (e) {
        showErrorModal(context, 'Authentication check failed', 'Error', () {});
      }
    });
  }

  void _searchUser() async {
    if (_mobileNumber.isEmpty) {
      showErrorModal(context, 'Enter a valid mobile number', 'Error', () {});
      return;
    }
    setState(() => _isLoading = true);
    try {
      // final authService = AuthService();
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      // Get the full response from the API
      final Map<String, dynamic> userData =
          await AuthService.getUserByMobile(_mobileNumber, token);

      if (userData.isEmpty) {
        showErrorModal(context, 'User data not found', 'Error', () {});
        return;
      }
      String? refreshToken = userData['refresh_token'];
      String? accessToken = userData['access_token'];

      if (accessToken == null) {
        showErrorModal(context, 'Access token not found', 'Error', () {});
        return;
      }

      // Use access token to get user info
      final List<UserInfo> userIdInfo =
          await UserService.getUserInfo(accessToken);
      if (userIdInfo.isNotEmpty) {
        userData['user_id'] = userIdInfo[0].id.toString();
      }

      setState(() {
        _userData = userData;
        // print(" data~ : " + _userData.toString());
        // print(" ~~kk~~ \n" + userIdInfo.toString());
      });
    } catch (error) {
      showErrorModal(context, '$error'.toString(), 'Error', () {});
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(
        context as BuildContext, UpdateUserScreen(mobileNumber: _mobileNumber));
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(title: "Update User"),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _reloadPage,
        child: Container(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
          ),
          child: SafeArea(
            child: Column(
              children: [
                if (widget.mobileNumber == null)
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextField(
                      controller: _controller,
                      onChanged: (value) {
                        _mobileNumber = value;
                        print(_mobileNumber);
                      },
                      onSubmitted: (value) {
                        _searchUser();
                      },
                      decoration: InputDecoration(
                        labelText: 'Enter Mobile Number',
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.search),
                          onPressed: _searchUser,
                        ),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                if (_isLoading)
                  const Padding(
                    padding: EdgeInsets.all(20.0),
                    child: Center(child: CircularProgressIndicator()),
                  )
                else
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16.0),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            if (_userData != null)
                              UpdateUserFrom(userData: _userData!),
                            if (_userData == null &&
                                widget.mobileNumber == null)
                              const Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Text(
                                  'Search for a user to update their details',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.grey),
                                ),
                              ),
                            if (_userData == null &&
                                widget.mobileNumber != null)
                              const Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Text(
                                  'User not found. Please check the mobile number and try again.',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(color: Colors.red),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
