import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/add_user/widget/new_user_email_field.dart';
import 'package:railops/screens/update_user/widget/update_user_email_field.dart';
import 'package:railops/screens/update_user/widget/update_user_mobile_field.dart';
import 'package:railops/screens/update_user/widget/update_user_whatsapp_field.dart';
import 'package:railops/screens/update_user/widget/update_secondary_phone_field.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/signup_error.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart';
import 'package:railops/services/authentication_services/Request_update_service.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/services/user_info_services/user_info_services.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';

class UpdateUserFrom extends StatefulWidget {
  final Map<String, dynamic>? userData;
  const UpdateUserFrom({super.key, required this.userData});

  @override
  State<UpdateUserFrom> createState() => _UpdateUserFromState();
}

class _UpdateUserFromState extends State<UpdateUserFrom> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _empNumberController = TextEditingController();
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _middleNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _whatsappController = TextEditingController();
  final TextEditingController _secondaryPhoneController =
      TextEditingController();

  String? usertype;
  String? userName;
  String? token;
  int? userId;
  // Form field values
  String _role = 'coach attendent';
  String _password = '';
  String _rePassword = '';
  List<String> _selectedZones = [];
  List<String> _division = [];
  List<String> _depot = [];
  List<String> _originalZone = [];
  List<String> _originalDivision = [];
  List<String> _originalDepot = [];

  bool _isEmailFieldVisible = true;
  bool _isLoading = false;
  bool _sameAsPhone = false;
  bool _isDataLoaded = false;
  bool _isWhatsAppButtonEnabled = false;

  List<String> depots = [];
  List<String> trainList = [];
  List<String> EmpNumberList = [];
  List<String> _trainNumber = [];
  List<String> _coachNumber = [];
  List<String> division_codes = [];
  List<String> all_zones = [];

  String? currentUserType;

  bool get _isPassenger => _role.toLowerCase() == 'passenger';

  // Define a consistent theme
  final _primaryColor = Colors.blue;
  final _secondaryColor = Colors.green.shade600;
  final _borderRadius = 10.0;
  final _fieldSpacing = 16.0;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    setState(() {
      currentUserType = userModel.userType;
    });
    if (widget.userData != null) {
      _loadInitialUserData(widget.userData!);
    }
  }

  @override
  Future<void> didChangeDependencies() async {
    super.didChangeDependencies();
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      token = userModel.token;
      usertype = userModel.userType;
      userName = userModel.userName;
    } catch (e) {
      print("Error accessing user model: $e");
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _empNumberController.dispose();
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _mobileController.dispose();
    _whatsappController.dispose();
    _secondaryPhoneController.dispose();
    super.dispose();
  }

  void _loadInitialUserData(Map<String, dynamic> data) {
    setState(() {
      userId = int.tryParse(data['user_id']?.toString() ?? '');
      _isLoading = true; // Show loading while fetching user data
    });

    String? whatsappNumber;
    String? secondaryPhoneNumber;

    // Check for secondary phone directly in the userData first
    data.forEach((key, value) {
      if (key.toLowerCase() == 'whatsapp_number') {
        whatsappNumber = value?.toString();
      }
      if (key.toLowerCase() == 'secondary_phone') {
        secondaryPhoneNumber = value?.toString();
      }
    });

    String? mobileNumber = data['phone']?.toString();
    String email = data['email'] ?? '';

    _firstNameController.text = data['first_name'] ?? '';
    _middleNameController.text = data['middle_name'] ?? '';
    _lastNameController.text = data['last_name'] ?? '';
    _emailController.text = email;
    _mobileController.text = mobileNumber ?? '';
    _empNumberController.text = data['emp_number'] ?? '';

    // Set secondary phone directly if available in userData
    if (secondaryPhoneNumber != null && secondaryPhoneNumber!.isNotEmpty) {
      _secondaryPhoneController.text = secondaryPhoneNumber!;
    }

    // Get user info to extract secondary phone from API if not directly available
    if (data['access_token'] != null &&
        (secondaryPhoneNumber == null || secondaryPhoneNumber!.isEmpty)) {
      UserService.getUserInfo(data['access_token']).then((userInfoList) {
        if (userInfoList.isNotEmpty) {
          setState(() {
            _secondaryPhoneController.text = userInfoList[0].secondaryPhone;
          });
        }
      }).catchError((error) {
        print("Error fetching user info: $error");
      }).whenComplete(() {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    } else {
      // If we don't need to fetch from API
      setState(() {
        _isLoading = false;
      });
    }

    setState(() {
      // More precise WhatsApp number handling
      if (whatsappNumber == null || whatsappNumber!.isEmpty) {
        _sameAsPhone = true;
        _whatsappController.text = mobileNumber ?? '';
      } else {
        _sameAsPhone = whatsappNumber == mobileNumber;
        _whatsappController.text = whatsappNumber!;
      }

      _isWhatsAppButtonEnabled = true;

      _division =
          data['division'] != null && data['division'].toString().isNotEmpty
              ? [data['division'].toString()]
              : [];
      _depot = data['depot'] != null && data['depot'].toString().isNotEmpty
          ? [data['depot'].toString()]
          : [];

      _selectedZones =
          data['zone'] != null && data['zone'].toString().isNotEmpty
              ? [data['zone'].toString()]
              : [];

      _originalDivision = List.from(_division);
      _originalDepot = List.from(_depot);
      _originalZone = List.from(_selectedZones);

      _role = data['user_type'] ?? _role;
      _isDataLoaded = true;
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadDependentDataSafely();
      }
    });
  }

  Future<void> _loadDependentDataSafely() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      if (_selectedZones.isNotEmpty) {
        await _loadDivisionsForZones();
      }

      if (_division.isNotEmpty) {
        await _loadDepotsForDivision();
      }

      if (mounted) {
        setState(() {
          _isDataLoaded = true;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        showErrorModal(context, "Error loading data: $e", "Error", () {});
      }
    }
  }

  Future<void> _loadDivisionsForZones() async {
    if (_selectedZones.isEmpty) return;
    try {
      List<ZoneDivision> divisions =
          await TrainServiceSignup.getDivisions(_selectedZones.join(','));

      setState(() {
        division_codes = divisions.map((division) => division.code).toList();
      });
    } catch (e) {
      print("Error loading divisions: $e");
    }
  }

  Future<void> _loadDepotsForDivision() async {
    if (_division.isEmpty) return;
    if (_division.join(',') == "" || _division.join(',') == " ") return;
    try {
      final getData = await TrainServiceSignup.getDepot(_division.join(','));
      setState(() {
        depots = getData;
      });
    } catch (e) {
      showErrorModal(context, 'Error loading depots: $e', "Error", () {});
    }
  }

  void _handleZoneSelection(String selectedZones) async {
    setState(() {
      _selectedZones = selectedZones.split(',');
    });

    if (selectedZones.isEmpty) {
      setState(() {
        _selectedZones = [];
        division_codes = [];
        _division = [];
        depots = [];
        _depot = [];
      });
      return;
    }

    if (_selectedZones.isNotEmpty) {
      try {
        List<ZoneDivision> divisions =
            await TrainServiceSignup.getDivisions(_selectedZones.join(','));

        List<String> divisionCodes =
            divisions.map((division) => division.code).toList();

        setState(() {
          division_codes = divisionCodes;
        });
      } catch (e) {
        print("Error fetching divisions: $e");
      }
    }
  }

  Future getDepot() async {
    if (_division.isEmpty) return;
    if (_division.join(',') == "" || _division.join(',') == " ") return;
    try {
      final getData = await TrainServiceSignup.getDepot(_division.join(','));
      setState(() {
        depots = getData;
      });
    } catch (e) {
      setState(() {});
      if (e is StateError && e.toString().contains('mounted')) {
        print('Widget disposed before operation completes');
      } else {
        print('Send Otp Failed : $e');
      }
    }
  }

  Future getTrainList() async {
    // if(_depot.isEmpty) return;
    // try {
    //   final getData = await TrainServiceSignup.getTrainList(_depot!.join(','));
    //   setState(() {
    //     trainList = getData['trains'];
    //     EmpNumberList = getData['emp_numbers'];
    //   });
    // } catch (e) {
    //   showErrorModal(context, '$e', "Error", () {});
    //   // setState(() {});
    //   if (e is StateError && e.toString().contains('mounted')) {
    //     print('Widget disposed before operation completes');
    //   } else {
    //     print('Send Otp Failed : $e');
    //   }
    // }
  }

  Future<void> _submitUpdateRequest() async {
    // First validate the form
    if (!_formKey.currentState!.validate()) {
      // If validation fails, don't proceed
      return;
    }

    // Save the form data
    _formKey.currentState!.save();

    // Check other required conditions
    if (_role != 'passenger') {
      if (_depot.isEmpty || _depot.every((element) => element.trim().isEmpty)) {
        showErrorModal(
            context, 'Please select at least one depot', "Error", () {});
        return;
      }
      if (userId == null) {
        showErrorModal(context, 'User ID is missing', "Error", () {});
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final Map<String, dynamic> updateData = {
        'user_id': userId,
        'phone': _mobileController.text,
        'f_name': _firstNameController.text,
        'm_name': _middleNameController.text,
        'l_name': _lastNameController.text,
        'email': _emailController.text,
        'user_type': _role,
        'division': _division.join(','),
        'depo': _depot.join(','),
        'zone': _selectedZones.join(','),
        'whatsapp_number': _whatsappController.text,
        'emp_number': _empNumberController.text,
      };

      // Always include secondary_phone in the update request
      updateData['secondary_phone'] = _secondaryPhoneController.text;

      final response = await UpdateUserService.submitUpdateRequest(
        token!,
        updateData,
      );
      Navigator.of(context).pop();
      showSuccessModal(
        context,
        '${response['message']}',
        "Success",
        () => Navigator.pushNamed(context, Routes.userInfo),
      );
    } catch (e) {
      if (e is SignupDetailsException) {
        showErrorDialog(context, e.details);
      } else {
        showErrorModal(context, '${e}', "Error", () {});
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Create styled input decoration
  InputDecoration _getInputDecoration(String label) {
    return InputDecoration(
      labelText: label,
      labelStyle: TextStyle(color: _primaryColor),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: Colors.grey.shade300),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: _primaryColor, width: 2),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(_borderRadius),
        borderSide: BorderSide(color: Colors.grey.shade400),
      ),
      filled: true,
      fillColor: Colors.grey.shade50,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
    );
  }

  Widget _buildEmailField() {
    return UpdateEmailField(
      initialValue: _emailController.text,
      onSaved: (value) {
        _emailController.text = value;
      },
    );
  }

  Widget _buildStaffOnlyFields() {
    if (_isPassenger) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Text(
              'Staff Information',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: _primaryColor,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: ZoneDropdown(
                  onSaved: _handleZoneSelection,
                  initialValue: _originalZone.join(''),
                ),
              ),
              const SizedBox(width: 5),
              Flexible(
                child: DivisionDropdown(
                  divisions: division_codes,
                  initialValue: _originalDivision.join(''),
                  onSaved: (value) {
                    setState(() {
                      _division = value.split(',');
                      _depot = [];
                    });
                    getDepot();
                  },
                ),
              ),
              const SizedBox(width: 5),
              Flexible(
                child: DepotDropdown(
                  depots: depots,
                  initialValue: _originalDepot.join(''),
                  onSaved: (value) {
                    setState(() {
                      _depot = value.split(',');
                    });
                    getTrainList();
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: _fieldSpacing),
          EmpNumberField(
            initialValue: _empNumberController.text,
            onSaved: (value) => _empNumberController.text = value,
            userType: _role,
            empNumberList:
                EmpNumberList.isEmpty && _empNumberController.text.isNotEmpty
                    ? [_empNumberController.text]
                    : EmpNumberList,
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: _primaryColor,
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: _primaryColor),
          SizedBox(height: 16),
          Text(
            'Loading user data...',
            style: TextStyle(color: _primaryColor, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  void _handlePhoneNumberChange(String value) {
    setState(() {
      _isWhatsAppButtonEnabled = true;
      if (_sameAsPhone) {
        _whatsappController.text = value;
      }
    });
  }

  Widget _buildWhatsAppFields() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(_borderRadius),
        border: Border.all(color: Colors.grey.shade300),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          OutlinedButton(
            onPressed: _toggleWhatsAppSameAsPhone,
            style: OutlinedButton.styleFrom(
              foregroundColor: _sameAsPhone ? Colors.grey : Colors.blue[700],
              backgroundColor: _sameAsPhone
                  ? Colors.grey.withOpacity(0.1)
                  : Colors.blue.withOpacity(0.1),
              side: BorderSide(
                color: _sameAsPhone ? Colors.grey : Colors.blue[700]!,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'WhatsApp number same as phone number (${_sameAsPhone ? 'Yes' : 'No'})',
              style: TextStyle(
                fontSize: 14.0,
                color: _sameAsPhone ? Colors.grey : Colors.blue[700],
              ),
            ),
          ),
          SizedBox(height: _fieldSpacing),
          UpdateWhatsappNumberField(
            initialValue: _whatsappController.text,
            enabled: true, // Always enabled
            onSaved: (value) {
              setState(() {
                _whatsappController.text = value;
                _sameAsPhone = false;
              });
            },
            onChanged: (value) {
              setState(() {
                _whatsappController.text = value;
                _sameAsPhone = false;
              });
            },
          ),
        ],
      ),
    );
  }

  void _toggleWhatsAppSameAsPhone() {
    setState(() {
      _sameAsPhone = !_sameAsPhone;

      if (_sameAsPhone) {
        // Set WhatsApp number to mobile number
        _whatsappController.text = _mobileController.text;
        _isWhatsAppButtonEnabled = false;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.9;

    if (_isLoading && !_isDataLoaded) {
      return _buildLoadingIndicator();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(_borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  'Update User Profile',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: _primaryColor,
                  ),
                ),
              ),
              const Divider(height: 32),
              _buildSectionTitle('Personal Information'),
              FirstNameField(
                initialValue: _firstNameController.text,
                onSaved: (value) => _firstNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              MiddleNameField(
                initialValue: _middleNameController.text,
                onSaved: (value) => _middleNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              LastNameField(
                initialValue: _lastNameController.text,
                onSaved: (value) => _lastNameController.text = value,
              ),
              SizedBox(height: _fieldSpacing),
              _buildSectionTitle('Contact Information'),
              _buildEmailField(),
              SizedBox(height: _fieldSpacing),
              UpdateMobileNumberField(
                initialValue: _mobileController.text,
                onSaved: (value) => _mobileController.text = value,
                onChanged: (value) {
                  // Update whatsapp number if "same as phone" is selected
                  if (_sameAsPhone && mounted) {
                    _whatsappController.text = value;
                  }
                  // Call the phone number change handler
                  _handlePhoneNumberChange(value);
                },
              ),
              SizedBox(height: _fieldSpacing),
              // Secondary Phone Field is now always visible and given equal importance
              UpdateSecondaryPhoneField(
                initialValue: _secondaryPhoneController.text,
                onSaved: (value) => _secondaryPhoneController.text = value,
                onChanged: (value) {
                  setState(() {
                    _secondaryPhoneController.text = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return null; // Allow empty value since it's optional
                  }
                  if (value.length != 10) {
                    return 'Secondary phone number must be 10 digits';
                  }
                  if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                    return 'Secondary phone number must contain only digits';
                  }
                  return null;
                },
              ),
              SizedBox(height: _fieldSpacing),
              _buildWhatsAppFields(),
              SizedBox(height: _fieldSpacing * 1.5),
              _buildSectionTitle('Role Information'),
              RolesDropdown(
                initialValue: _role,
                onSaved: (value) {
                  if (mounted) {
                    setState(() {
                      _role = value;
                      // Clear staff-only fields when switching to passenger role
                      if (_isPassenger) {
                        _selectedZones = [];
                        _division = [];
                        _depot = [];
                        _empNumberController.text = '';
                      }
                    });
                  }
                },
              ),
              SizedBox(height: _fieldSpacing),
              _buildStaffOnlyFields(),
              SizedBox(height: _fieldSpacing * 1.5),
              Container(
                alignment: Alignment.center,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _secondaryColor,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    fixedSize: Size(buttonWidth, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  onPressed: _submitUpdateRequest,
                  child: _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : Text(
                          currentUserType == 'railway admin'
                              ? 'Update User'
                              : 'Request For Update User',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              SizedBox(height: _fieldSpacing),
            ],
          ),
        ),
      ),
    );
  }
}
