import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:railops/models/notification_tray_model.dart';

/// Provider for managing notification tray state
/// Handles CRUD operations for notification tray items with persistence
class NotificationTrayProvider extends ChangeNotifier {
  static const String _storageKey = 'notification_tray_items';
  static const String _lastUpdateKey = 'notification_tray_last_update';

  List<NotificationTrayItem> _items = [];
  bool _isLoading = false;
  DateTime? _lastUpdateTime;

  // Getters
  List<NotificationTrayItem> get items => List.unmodifiable(_items);
  bool get isLoading => _isLoading;
  DateTime? get lastUpdateTime => _lastUpdateTime;

  /// Get unread items only
  List<NotificationTrayItem> get unreadItems =>
      _items.where((item) => !item.isRead).toList();

  /// Get total unread count
  int get unreadCount => unreadItems.length;

  /// Get items grouped by station
  Map<String, StationNotificationGroup> get itemsByStation {
    final Map<String, List<NotificationTrayItem>> grouped = {};

    for (final item in _items) {
      grouped[item.stationCode] = grouped[item.stationCode] ?? [];
      grouped[item.stationCode]!.add(item);
    }

    final Map<String, StationNotificationGroup> result = {};
    for (final entry in grouped.entries) {
      final items = entry.value;
      items.sort((a, b) => b.timestamp.compareTo(a.timestamp)); // Latest first

      result[entry.key] = StationNotificationGroup(
        stationCode: entry.key,
        items: items,
        latestTimestamp: items.first.timestamp,
      );
    }

    return result;
  }

  /// Get summary data for the notification tray
  NotificationTraySummary get summary {
    if (_items.isEmpty) {
      return NotificationTraySummary.empty();
    }

    final totalOnboarding =
        _items.fold(0, (sum, item) => sum + item.onboardingCount);
    final totalOffboarding =
        _items.fold(0, (sum, item) => sum + item.offboardingCount);
    final totalVacant = _items.fold(0, (sum, item) => sum + item.vacantCount);

    final activeStations =
        _items.map((item) => item.stationCode).toSet().toList();
    final activeCoaches =
        _items.map((item) => item.coachNumber).toSet().toList();

    return NotificationTraySummary(
      totalUnreadCount: unreadCount,
      totalOnboardingCount: totalOnboarding,
      totalOffboardingCount: totalOffboarding,
      totalVacantCount: totalVacant,
      activeStations: activeStations,
      activeCoaches: activeCoaches,
      lastUpdateTime: _lastUpdateTime,
    );
  }

  /// Initialize provider and load stored data
  Future<void> initialize() async {
    await loadFromStorage();
  }

  /// Add new notification tray items
  Future<void> addItems(List<NotificationTrayItem> newItems) async {
    if (newItems.isEmpty) return;

    _setLoading(true);

    try {
      // Filter out duplicates based on ID and station-coach-timestamp combination
      final existingIds = _items.map((item) => item.id).toSet();
      final existingKeys = _items
          .map((item) =>
              '${item.stationCode}_${item.coachNumber}_${item.timestamp.millisecondsSinceEpoch}')
          .toSet();

      // Also check for duplicates within the new items list
      final seenIds = <String>{};
      final seenKeys = <String>{};

      final uniqueNewItems = <NotificationTrayItem>[];

      for (final item in newItems) {
        final itemId = item.id;
        final itemKey =
            '${item.stationCode}_${item.coachNumber}_${item.timestamp.millisecondsSinceEpoch}';

        if (!existingIds.contains(itemId) &&
            !existingKeys.contains(itemKey) &&
            !seenIds.contains(itemId) &&
            !seenKeys.contains(itemKey)) {
          uniqueNewItems.add(item);
          seenIds.add(itemId);
          seenKeys.add(itemKey);
        }
      }

      if (uniqueNewItems.isNotEmpty) {
        _items.addAll(uniqueNewItems);
        _sortItems();
        _lastUpdateTime = DateTime.now();

        await _saveToStorage();

        if (kDebugMode) {
          print(
              'NotificationTrayProvider: Added ${uniqueNewItems.length} new items');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error adding items: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Mark a specific item as read
  Future<void> markAsRead(String itemId) async {
    final index = _items.indexWhere((item) => item.id == itemId);
    if (index != -1 && !_items[index].isRead) {
      _items[index] = _items[index].copyWith(isRead: true);
      await _saveToStorage();
      notifyListeners();

      if (kDebugMode) {
        print('NotificationTrayProvider: Marked item $itemId as read');
      }
    }
  }

  /// Mark all items as read
  Future<void> markAllAsRead() async {
    if (unreadCount == 0) return;

    _setLoading(true);

    try {
      for (int i = 0; i < _items.length; i++) {
        if (!_items[i].isRead) {
          _items[i] = _items[i].copyWith(isRead: true);
        }
      }

      await _saveToStorage();

      if (kDebugMode) {
        print('NotificationTrayProvider: Marked all items as read');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error marking all as read: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Mark all items for a specific station as read
  Future<void> markStationAsRead(String stationCode) async {
    final stationItems = _items
        .where((item) => item.stationCode == stationCode && !item.isRead)
        .toList();

    if (stationItems.isEmpty) return;

    _setLoading(true);

    try {
      for (int i = 0; i < _items.length; i++) {
        if (_items[i].stationCode == stationCode && !_items[i].isRead) {
          _items[i] = _items[i].copyWith(isRead: true);
        }
      }

      await _saveToStorage();

      if (kDebugMode) {
        print('NotificationTrayProvider: Marked station $stationCode as read');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error marking station as read: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Clear all notification tray items
  Future<void> clearAll() async {
    _setLoading(true);

    try {
      _items.clear();
      _lastUpdateTime = null;
      await _saveToStorage();

      if (kDebugMode) {
        print('NotificationTrayProvider: Cleared all items');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error clearing items: $e');
      }
    } finally {
      _setLoading(false);
    }
  }

  /// Remove items older than specified duration
  Future<void> removeOldItems(
      {Duration maxAge = const Duration(days: 7)}) async {
    final cutoffTime = DateTime.now().subtract(maxAge);
    final initialCount = _items.length;

    _items.removeWhere((item) => item.timestamp.isBefore(cutoffTime));

    if (_items.length != initialCount) {
      await _saveToStorage();
      notifyListeners();

      if (kDebugMode) {
        print(
            'NotificationTrayProvider: Removed ${initialCount - _items.length} old items');
      }
    }
  }

  /// Load items from persistent storage
  Future<void> loadFromStorage() async {
    _setLoading(true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson = prefs.getString(_storageKey);
      final lastUpdateTimestamp = prefs.getInt(_lastUpdateKey);

      if (itemsJson != null) {
        final List<dynamic> itemsList = json.decode(itemsJson);
        _items = itemsList
            .map((json) => NotificationTrayItem.fromJson(json))
            .toList();
        _sortItems();
      }

      if (lastUpdateTimestamp != null) {
        _lastUpdateTime =
            DateTime.fromMillisecondsSinceEpoch(lastUpdateTimestamp);
      }

      if (kDebugMode) {
        print(
            'NotificationTrayProvider: Loaded ${_items.length} items from storage');
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error loading from storage: $e');
      }
      _items = [];
      _lastUpdateTime = null;
    } finally {
      _setLoading(false);
    }
  }

  /// Save items to persistent storage
  Future<void> _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final itemsJson =
          json.encode(_items.map((item) => item.toJson()).toList());

      await prefs.setString(_storageKey, itemsJson);

      if (_lastUpdateTime != null) {
        await prefs.setInt(
            _lastUpdateKey, _lastUpdateTime!.millisecondsSinceEpoch);
      }
    } catch (e) {
      if (kDebugMode) {
        print('NotificationTrayProvider: Error saving to storage: $e');
      }
    }
  }

  /// Sort items by timestamp (newest first)
  void _sortItems() {
    _items.sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  /// Set loading state and notify listeners
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Get items for a specific train and date
  List<NotificationTrayItem> getItemsForTrain(String trainNumber, String date) {
    return _items
        .where((item) => item.trainNumber == trainNumber && item.date == date)
        .toList();
  }

  /// Get items for a specific station
  List<NotificationTrayItem> getItemsForStation(String stationCode) {
    return _items.where((item) => item.stationCode == stationCode).toList();
  }

  /// Get items for a specific coach
  List<NotificationTrayItem> getItemsForCoach(String coachNumber) {
    return _items.where((item) => item.coachNumber == coachNumber).toList();
  }

  /// Refresh data (useful for pull-to-refresh)
  Future<void> refresh() async {
    await loadFromStorage();
    await removeOldItems(); // Clean up old items during refresh
  }
}
