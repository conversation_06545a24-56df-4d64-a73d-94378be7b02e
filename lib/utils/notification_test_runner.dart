import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';

/// Test runner utility for verifying Phase 1 and Phase 2 notification functionality
/// This can be called from your app to quickly test all notification features
class NotificationTestRunner {
  static bool _isRunning = false;

  /// Run all notification tests in sequence
  static Future<Map<String, bool>> runAllTests() async {
    if (_isRunning) {
      if (kDebugMode) {
        print('NotificationTestRunner: Tests already running, skipping...');
      }
      return {};
    }

    _isRunning = true;
    final results = <String, bool>{};

    try {
      if (kDebugMode) {
        print(
            '🧪 NotificationTestRunner: Starting comprehensive test suite...');
      }

      // Phase 1 Tests
      results['phase1_basic_setup'] = await _testPhase1BasicSetup();
      results['phase1_configuration'] = await _testPhase1Configuration();
      results['phase1_notifications'] = await _testPhase1Notifications();

      // Phase 2 Tests
      results['phase2_enhanced_types'] = await _testPhase2EnhancedTypes();
      results['phase2_configuration'] = await _testPhase2Configuration();
      results['phase2_notifications'] = await _testPhase2Notifications();

      // Integration Tests
      results['integration_compatibility'] =
          await _testIntegrationCompatibility();
      results['integration_all_types'] = await _testAllNotificationTypes();

      // Summary
      final passedTests = results.values.where((result) => result).length;
      final totalTests = results.length;

      if (kDebugMode) {
        print('🎯 NotificationTestRunner: Test Results Summary:');
        print('   ✅ Passed: $passedTests/$totalTests tests');

        if (passedTests == totalTests) {
          print(
              '   🎉 ALL TESTS PASSED! Both Phase 1 and Phase 2 are working correctly.');
        } else {
          print(
              '   ⚠️  Some tests failed. Check individual test results below:');
          results.forEach((test, result) {
            final status = result ? '✅' : '❌';
            print('   $status $test');
          });
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ NotificationTestRunner: Error during testing: $e');
      }
      results['error'] = false;
    } finally {
      _isRunning = false;
    }

    return results;
  }

  /// Test Phase 1 basic setup
  static Future<bool> _testPhase1BasicSetup() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 1: Basic Setup...');
      }

      // Test basic notification types exist
      final basicTypes = [
        OnboardingNotificationType.boarding,
        OnboardingNotificationType.offBoarding,
        OnboardingNotificationType.stationApproaching,
        OnboardingNotificationType.coachReminder,
        OnboardingNotificationType.berthReminder,
      ];

      for (final type in basicTypes) {
        if (type.name.isEmpty) return false;
      }

      // Test basic trigger types exist
      final basicTriggers = [
        NotificationTriggerType.immediate,
        NotificationTriggerType.timeBasedDelay,
        NotificationTriggerType.locationBasedProximity,
        NotificationTriggerType.stationSequence,
      ];

      for (final trigger in basicTriggers) {
        if (trigger.name.isEmpty) return false;
      }

      if (kDebugMode) {
        print('   ✅ Phase 1 basic setup verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 1 basic setup failed: $e');
      }
      return false;
    }
  }

  /// Test Phase 1 configuration
  static Future<bool> _testPhase1Configuration() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 1: Configuration...');
      }

      // Test basic configuration
      NotificationIntegrationHelper.configureNotificationTiming(
        boardingNotificationDelay: const Duration(minutes: 10),
        offBoardingNotificationDelay: const Duration(minutes: 20),
        stationsBeforeAlert: 2,
      );

      // Test statistics retrieval
      final stats = NotificationIntegrationHelper.getNotificationStats();
      if (stats.isEmpty) return false;

      if (kDebugMode) {
        print('   ✅ Phase 1 configuration verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 1 configuration failed: $e');
      }
      return false;
    }
  }

  /// Test Phase 1 notifications
  static Future<bool> _testPhase1Notifications() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 1: Notifications...');
      }

      // Test basic notification
      await NotificationIntegrationHelper.sendTestNotification(
        title: 'Phase 1 Test',
        body: 'Testing Phase 1 basic notification',
      );

      // Small delay to allow notification processing
      await Future.delayed(const Duration(milliseconds: 500));

      if (kDebugMode) {
        print('   ✅ Phase 1 notifications verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 1 notifications failed: $e');
      }
      return false;
    }
  }

  /// Test Phase 2 enhanced types
  static Future<bool> _testPhase2EnhancedTypes() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 2: Enhanced Types...');
      }

      // Test enhanced notification types exist
      final enhancedTypes = [
        OnboardingNotificationType.stationApproachAlert,
        OnboardingNotificationType.boardingCountUpdate,
        OnboardingNotificationType.offBoardingPreparation,
        OnboardingNotificationType.trainStatusUpdate,
        OnboardingNotificationType.proximityAlert,
      ];

      for (final type in enhancedTypes) {
        if (type.name.isEmpty) return false;
      }

      // Test enhanced trigger types exist
      final enhancedTriggers = [
        NotificationTriggerType.passengerCountThreshold,
        NotificationTriggerType.trainStatusChange,
        NotificationTriggerType.scheduleChange,
      ];

      for (final trigger in enhancedTriggers) {
        if (trigger.name.isEmpty) return false;
      }

      if (kDebugMode) {
        print('   ✅ Phase 2 enhanced types verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 2 enhanced types failed: $e');
      }
      return false;
    }
  }

  /// Test Phase 2 configuration
  static Future<bool> _testPhase2Configuration() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 2: Configuration...');
      }

      // Test proximity configuration
      NotificationIntegrationHelper.configureProximityNotifications(
        proximityThresholdKm: 3.0,
        locationUpdateInterval: const Duration(minutes: 1),
        enableLocationMonitoring: true,
      );

      // Test passenger count configuration
      NotificationIntegrationHelper.configurePassengerCountNotifications(
        passengerCountThreshold: 8,
        enableBoardingCountUpdates: true,
        enableOffBoardingPreparation: true,
      );

      // Test train status configuration
      NotificationIntegrationHelper.configureTrainStatusMonitoring(
        monitoredStatusTypes: ['delay', 'cancellation'],
        statusCheckInterval: const Duration(minutes: 3),
        enableScheduleChangeAlerts: true,
      );

      if (kDebugMode) {
        print('   ✅ Phase 2 configuration verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 2 configuration failed: $e');
      }
      return false;
    }
  }

  /// Test Phase 2 notifications
  static Future<bool> _testPhase2Notifications() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Phase 2: Notifications...');
      }

      // Test proximity notification
      await NotificationIntegrationHelper.sendTestProximityNotification(
        stationName: 'Test Station',
        distanceKm: 2.5,
      );
      await Future.delayed(const Duration(milliseconds: 300));

      // Test station approach alert
      await NotificationIntegrationHelper.sendTestStationApproachAlert(
        stationName: 'Test Station',
        minutesBeforeArrival: 5,
      );
      await Future.delayed(const Duration(milliseconds: 300));

      // Test train status update
      await NotificationIntegrationHelper.sendTestTrainStatusUpdate(
        trainNumber: 'TEST123',
        statusType: 'delay',
        statusMessage: 'Test delay notification',
      );
      await Future.delayed(const Duration(milliseconds: 300));

      // Test boarding count update
      await NotificationIntegrationHelper.sendTestBoardingCountUpdate(
        stationName: 'Test Station',
        currentCount: 10,
        previousCount: 7,
        coaches: ['A1', 'B2'],
      );
      await Future.delayed(const Duration(milliseconds: 300));

      if (kDebugMode) {
        print('   ✅ Phase 2 notifications verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Phase 2 notifications failed: $e');
      }
      return false;
    }
  }

  /// Test integration compatibility
  static Future<bool> _testIntegrationCompatibility() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Integration: Compatibility...');
      }

      // Test that Phase 1 still works after Phase 2 installation
      await NotificationIntegrationHelper.sendTestNotification(
        title: 'Compatibility Test',
        body: 'Testing backward compatibility',
      );

      // Test that both configuration systems work together
      final stats = NotificationIntegrationHelper.getNotificationStats();
      if (stats.isEmpty) return false;

      if (kDebugMode) {
        print('   ✅ Integration compatibility verified');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ Integration compatibility failed: $e');
      }
      return false;
    }
  }

  /// Test all notification types are available
  static Future<bool> _testAllNotificationTypes() async {
    try {
      if (kDebugMode) {
        print('🔍 Testing Integration: All Types Available...');
      }

      const allTypes = OnboardingNotificationType.values;
      const allTriggers = NotificationTriggerType.values;

      // Should have at least 10 notification types (5 Phase 1 + 5 Phase 2)
      if (allTypes.length < 10) return false;

      // Should have at least 7 trigger types (4 Phase 1 + 3 Phase 2)
      if (allTriggers.length < 7) return false;

      if (kDebugMode) {
        print(
            '   ✅ All notification types available (${allTypes.length} types, ${allTriggers.length} triggers)');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('   ❌ All types test failed: $e');
      }
      return false;
    }
  }

  /// Quick test method for development
  static Future<void> quickTest() async {
    if (kDebugMode) {
      print('🚀 Running quick notification test...');
    }

    try {
      // Test one notification from each phase
      await NotificationIntegrationHelper.sendTestNotification(
        title: 'Phase 1 Quick Test',
        body: 'Basic notification working',
      );

      await Future.delayed(const Duration(milliseconds: 500));

      await NotificationIntegrationHelper.sendTestProximityNotification(
        stationName: 'Quick Test Station',
        distanceKm: 1.5,
      );

      if (kDebugMode) {
        print('✅ Quick test completed - check your notifications!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Quick test failed: $e');
      }
    }
  }
}

/// Simple test widget that can be added to your app for easy testing
/// Add this to any screen to get testing buttons
class NotificationTestWidget extends StatefulWidget {
  const NotificationTestWidget({super.key});

  @override
  State<NotificationTestWidget> createState() => _NotificationTestWidgetState();
}

class _NotificationTestWidgetState extends State<NotificationTestWidget> {
  bool _isRunning = false;
  Map<String, bool> _testResults = {};

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification System Testing',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Quick Test Button
            ElevatedButton(
              onPressed: _isRunning ? null : _runQuickTest,
              child: const Text('Quick Test (Phase 1 + Phase 2)'),
            ),
            const SizedBox(height: 8),

            // Full Test Suite Button
            ElevatedButton(
              onPressed: _isRunning ? null : _runFullTestSuite,
              child: const Text('Run Full Test Suite'),
            ),
            const SizedBox(height: 16),

            // Test Results
            if (_testResults.isNotEmpty) ...[
              const Text(
                'Test Results:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ..._testResults.entries.map((entry) {
                final icon = entry.value ? '✅' : '❌';
                return Text('$icon ${entry.key}');
              }),
            ],

            if (_isRunning) ...[
              const SizedBox(height: 16),
              const Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Text('Running tests...'),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _runQuickTest() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    try {
      await NotificationTestRunner.quickTest();
      setState(() {
        _testResults['quick_test'] = true;
      });
    } catch (e) {
      setState(() {
        _testResults['quick_test'] = false;
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _runFullTestSuite() async {
    setState(() {
      _isRunning = true;
      _testResults.clear();
    });

    try {
      final results = await NotificationTestRunner.runAllTests();
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults['error'] = false;
      });
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }
}
