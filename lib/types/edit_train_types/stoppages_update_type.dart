import 'dart:convert';

class StoppagesUpdateType {
  final String message;
  final String trainNo;
  final List<String> allStations;
  final List<String> attendanceStations;

  StoppagesUpdateType({
    required this.message,
    required this.trainNo,
    required this.allStations,
    required this.attendanceStations,
  });

  // Factory method to create an instance from JSON
  factory StoppagesUpdateType.fromJson(Map<String, dynamic> json) {
    return StoppagesUpdateType(
      message: json['message'] ?? '',
      trainNo: json['train_no'] ?? '',
      allStations: List<String>.from(json['all_stations'] ?? []),
      attendanceStations: List<String>.from(json['attendance_stations'] ?? []),
    );
  }

  // Convert the object to JSON
  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'train_no': trainNo,
      'all_stations': allStations,
      'attendance_stations': attendanceStations,
    };
  }
}
