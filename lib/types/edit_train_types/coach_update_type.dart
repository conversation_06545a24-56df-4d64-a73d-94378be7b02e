import 'dart:convert';

class CoachUpdateType {
  final String message;
  final String trainNo;
  final List<String> data;

  CoachUpdateType({
    required this.message,
    required this.trainNo,
    required this.data,
  });

  // Factory method to create an instance from JSON
  factory CoachUpdateType.fromJson(Map<String, dynamic> json) {
    return CoachUpdateType(
      message: json['message'] ?? '',
      trainNo: json['train_no'] ?? '',
      data: List<String>.from(json['data'] ?? []),
    );
  }

  // Convert the object to JSON
  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'train_no': trainNo,
      'data': data,
    };
  }
}