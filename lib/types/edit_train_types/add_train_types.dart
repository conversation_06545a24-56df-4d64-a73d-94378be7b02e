
class AddTrainResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;

  AddTrainResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory AddTrainResponse.fromJson(Map<String, dynamic> json) {
    return AddTrainResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? 'No message provided',
      data: json['data'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
    };
  }
}