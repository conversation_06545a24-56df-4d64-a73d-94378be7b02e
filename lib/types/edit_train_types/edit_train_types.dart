class EditTrainsData {
  final int? id;
  final Map<String, String>? extraInfo;
  final int? trainNo;
  final String? trainName;
  final int? relatedTrain;
  final String? updown;  // This can be null as per your JSON
  final String? division;
  final String? depot;
  final List<int>? frequency;
  final String? fromStation;
  final String? toStation;
  final String? chartingDay;
  final String? startTime;
  final String? endTime;
  final String? chartingTime;
  final String? trainType;
  final List<String>? stoppagesInSequence;
  final List<Map<String, String>>? arrivalTime;
  final int? journeyDurationDays;  // This can be null as per your JSON
  final List<String>? attendanceStations;
  final List<String>? coachesInSequence;
  final String? coachSequenceUpdatedBy;
  final String? coachSequenceUpdatedAt;
  final String? createdAt;
  final String? createdBy;
  final String? updatedAt;
  final String? updatedBy;

  EditTrainsData({
    this.id,
    this.extraInfo,
    this.trainNo,  // Changed from required to optional
    this.trainName,  // Changed from required to optional
    this.relatedTrain,  // Changed from required to optional
    this.updown,  // Changed from required to optional
    this.division,  // Changed from required to optional
    this.depot,  // Changed from required to optional
    this.frequency,  // Changed from required to optional
    this.fromStation,
    this.toStation,
    this.chartingDay,
    this.startTime,  // Changed from required to optional
    this.endTime,  // Changed from required to optional  
    this.chartingTime,
    this.trainType,  // Changed from required to optional
    this.stoppagesInSequence,  // Changed from required to optional
    this.arrivalTime,
    this.journeyDurationDays,
    this.attendanceStations,
    this.coachesInSequence,
    this.coachSequenceUpdatedBy,
    this.coachSequenceUpdatedAt,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  factory EditTrainsData.fromJson(Map<String, dynamic> json) {
    return EditTrainsData(
      id: json['id'] as int?,
      extraInfo: json['extra_info'] != null
          ? (json['extra_info'] as Map<String, dynamic>).map(
              (key, value) => MapEntry(key.toString(), value.toString()),
            )
          : null,
      trainNo: json['train_no'] as int?,
      trainName: json['train_name'] as String?,
      relatedTrain: json['related_train'] is int
          ? json['related_train'] as int?
          : int.tryParse(json['related_train']?.toString() ?? ''),
      updown: json['up_down'] as String?,  // Will handle the null case
      division: json['division'] as String?,
      depot: json['Depot'] as String?,
      frequency: (json['frequency'] as List?)
          ?.map((item) => int.tryParse(item.toString()) ?? 0)
          .toList(),
      fromStation: json['from_station'] as String?,
      toStation: json['to_station'] as String?,
      chartingDay: json['charting_day'] as String?,
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
      chartingTime: json['charting_time'] as String?,
      trainType: json['train_type'] as String?,
      stoppagesInSequence: (json['stopages_in_sequence'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      arrivalTime: (json['arrival_time'] as List?)
          ?.map((item) => (item as Map<String, dynamic>)
              .map((key, value) => MapEntry(key.toString(), value.toString())))
          .toList(),
      journeyDurationDays: json['journey_duration_days'] as int?,  // Will handle the null case
      attendanceStations: (json['attendance_stations'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      coachesInSequence: (json['coaches_in_sequence'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      coachSequenceUpdatedBy: json['coach_sequence_updated_by'] as String?,
      coachSequenceUpdatedAt: json['coach_sequence_updated_at'] as String?,
      createdAt: json['created_at'] as String?,
      createdBy: json['created_by'] as String?,
      updatedAt: json['updated_at'] as String?,
      updatedBy: json['updated_by'] as String?,
    );
  }

  // Added toJson method for consistency
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'extra_info': extraInfo,
      'train_no': trainNo,
      'train_name': trainName,
      'related_train': relatedTrain,
      'up_down': updown,
      'division': division,
      'Depot': depot,
      'frequency': frequency,
      'from_station': fromStation,
      'to_station': toStation,
      'charting_day': chartingDay,
      'start_time': startTime,
      'end_time': endTime,
      'charting_time': chartingTime,
      'train_type': trainType,
      'stopages_in_sequence': stoppagesInSequence,
      'arrival_time': arrivalTime,
      'journey_duration_days': journeyDurationDays,
      'attendance_stations': attendanceStations,
      'coaches_in_sequence': coachesInSequence,
      'coach_sequence_updated_by': coachSequenceUpdatedBy,
      'coach_sequence_updated_at': coachSequenceUpdatedAt,
      'created_at': createdAt,
      'created_by': createdBy,
      'updated_at': updatedAt,
      'updated_by': updatedBy,
    };
  }
}

class EditTrainsRequest {
  final String? token;
  final int? trainNo;
  final String? trainName;
  final int? relatedTrain;
  final String? updown;
  final String? division;
  final String? depot;
  final Map<String, String>? extraInfo;
  final List<int>? frequency;
  final String? fromStation;
  final String? toStation;
  final String? chartingDay;
  final String? startTime;
  final String? endTime;
  final String? chartingTime;
  final String? trainType;
  final List<String>? stoppagesInSequence;
  final int? journeyDurationDays;
  final List<String>? attendanceStations;
  final List<String>? coachesInSequence;

  EditTrainsRequest({
    this.token,
    this.trainNo,
    this.trainName,
    this.relatedTrain,
    this.updown,
    this.division,
    this.depot,
    this.extraInfo,
    this.frequency,
    this.fromStation,
    this.toStation,
    this.chartingDay,
    this.startTime,
    this.endTime,
    this.chartingTime,
    this.trainType,
    this.stoppagesInSequence,
    this.journeyDurationDays,
    this.attendanceStations,
    this.coachesInSequence,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'train_no': trainNo,
      'train_name': trainName,
      'related_train': relatedTrain,
      'up_down': updown,
      'division': division,
      'Depot': depot,
      'extra_info': extraInfo,
      'frequency': frequency,
      'from_station': fromStation,
      'to_station': toStation,
      'charting_day': chartingDay,
      'start_time': startTime,
      'end_time': endTime,
      'charting_time': chartingTime,
      'train_type': trainType,
      'stopages_in_sequence': stoppagesInSequence,
      'journey_duration_days': journeyDurationDays,
      'attendance_stations': attendanceStations,
      'coaches_in_sequence': coachesInSequence,
    };
  }
}