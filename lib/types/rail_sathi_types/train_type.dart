class Train {
  final int id;
  final String trainNo;
  final String trainName;
  
  Train({required this.id, required this.trainNo, required this.trainName});
  
  factory Train.fromJson(Map<String, dynamic> json) {
    return Train(
      id: json['pk'] ?? 0,
      trainNo: json['train_no'] ?? '',
      trainName: json['train_name'] ?? '',
    );
  }
  
  @override
  String toString() {
    return trainNo;
  }
}