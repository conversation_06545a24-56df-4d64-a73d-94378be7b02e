class Complain {
  final int complainId;
  final String complainDate;
  final String pnrNumber;
  final String isPnrValidated;
  final String name;
  final String mobileNumber;
  final String complainType;
  final String complainDescription;
  final String complainStatus;
  final String coach;
  final int berthNo;
  final String? trainNumber;
  final List<String> mediaUrls;
  final List<Map<String, dynamic>> media;
  final int?train;

  Complain({
    required this.complainId,
    required this.complainDate,
    required this.pnrNumber,
    required this.isPnrValidated,
    required this.name,
    required this.mobileNumber,
    required this.complainType,
    required this.complainDescription,
    required this.complainStatus,
    required this.coach,
    required this.berthNo,
    required this.trainNumber,
    required this.mediaUrls,
    required this.media,
    this.train,
  });

  factory Complain.fromJson(Map<String, dynamic> json) {
    final mediaList = List<Map<String, dynamic>>.from(json['rail_sathi_complain_media_files']);
    final mediaUrls = mediaList.map((e) => e['media_url'] as String).toList();

    return Complain(
      complainId: json['complain_id'],
      complainDate: json['complain_date'],
      pnrNumber: json['pnr_number'],
      isPnrValidated: json['is_pnr_validated'],
      name: json['name'],
      mobileNumber: json['mobile_number'],
      complainType: json['complain_type'],
      complainDescription: json['complain_description'],
      complainStatus: json['complain_status'],
      coach: json['coach'],
      berthNo: json['berth_no'],
      trainNumber: json['train_number'],
      mediaUrls: mediaUrls,
      media: mediaList,
      train:json["train"]
    );
  }
}
