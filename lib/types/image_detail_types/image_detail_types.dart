import 'package:intl/intl.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:railops/types/attendance_types/attendance_data.dart';
import 'package:railops/types/attendance_types/attendance_entery.dart';
import 'package:railops/types/trip_report_type/trip_report_type.dart';

class ImageResponse {
  final int id;
  final String imageUrl;
  final int coachNumber;
  final String date;
  final String userName;
  final String createdAt;
  final String createdBy;
  final String updatedAt;
  final String updatedBy;
  final String latitude;
  final String longitude;
  final int train;
  final int user;
  final String? coach;
  final String? issue;
  final List? imageUrls;
  final List? videoUrls;

  ImageResponse({
    required this.id,
    required this.imageUrl,
    required this.coachNumber,
    required this.date,
    required this.userName,
    required this.createdAt,
    required this.createdBy,
    required this.updatedAt,
    required this.updatedBy,
    required this.latitude,
    required this.longitude,
    required this.train,
    required this.user,
    this.coach,
    this.issue,
    this.imageUrls,
    this.videoUrls,
  });

  factory ImageResponse.fromJobchartType(JobchartType jobchart) {
    return ImageResponse(
      id: jobchart.id,
      imageUrl: jobchart.imageUrl,
      coachNumber: 0, // No coach number in JobchartType
      date: jobchart.originDate,
      userName: '', // No username in JobchartType
      createdAt: jobchart.createdAt,
      createdBy: jobchart.createdBy,
      updatedAt: jobchart.updatedAt,
      updatedBy: jobchart.updatedBy,
      latitude: jobchart.latitude ?? '',
      longitude: jobchart.longitude ?? '',
      train: jobchart.train,
      user: 0, // No user in JobchartType
      coach: jobchart.coach ,
      issue: jobchart.issue ,
    );
  }

  
  factory ImageResponse.fromTripReportType(TripReportType tripreport) {
    return ImageResponse(
      id: tripreport.id,
      imageUrl: "",
      imageUrls: tripreport.imageUrls,
      videoUrls: tripreport.videoUrls,
      coachNumber: 0, // No coach number in JobchartType
      date: tripreport.originDate,
      userName: '', // No username in JobchartType
      createdAt: tripreport.createdAt,
      createdBy: tripreport.createdBy,
      updatedAt: tripreport.updatedAt,
      updatedBy: tripreport.updatedBy,
      latitude: tripreport.latitude ?? '',
      longitude: tripreport.longitude ?? '',
      train: tripreport.train,
      user: 0, // No user in JobchartType
      coach: tripreport.coach ,
      issue: tripreport.issue ,
    );
  }

  factory ImageResponse.fromAttendanceData(AttendanceData attendance) {
    return ImageResponse(
      id: attendance.id,
      imageUrl: attendance.imageUrl,
      coachNumber: int.tryParse(attendance.coachNo ?? '0') ?? 0,
      date: attendance.originDate,
      userName: attendance.username,
      createdAt: attendance.createdAt,
      createdBy: attendance.createdBy,
      updatedAt: attendance.updatedAt,
      updatedBy: attendance.updatedBy,
      latitude: attendance.latitude ?? '',
      longitude: attendance.longitude ?? '',
      train: attendance.train,
      user: attendance.user,
    );
  }

  factory ImageResponse.fromAttendenceEntery(AttendanceEntry entry) {
    return ImageResponse(
      id: entry.id,
      imageUrl: entry.imageUrl,
      coachNumber: int.tryParse(entry.coachNo ?? '0') ?? 0,
      date: entry.originDate,
      userName: entry.username,
      createdAt: entry.updatedAt,
      createdBy: entry.username,
      updatedAt: entry.updatedAt,
      updatedBy: entry.updatedBy,
      latitude: entry.latitude,
      longitude: entry.longitude,
      train: int.tryParse(entry.trainNumber) ?? 0,
      user: 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image_url': imageUrl,
      'coach_number': coachNumber,
      'date': date,
      'user_name': userName,
      'created_at': createdAt,
      'created_by': createdBy,
      'updated_at': updatedAt,
      'updated_by': updatedBy,
      'latitude': latitude,
      'longitude': longitude,
      'train': train,
      'user': user,
      'coach': coach,
      'issue': issue,
    };
  }
}
