class AttendanceEntry {
  final int id;
  final String username;
  final String trainNumber;
  final String originDate;
  final String imageUrl;
  final String stationCode;
  final String? coachNo;
  final String latitude;
  final String longitude;
  final String updatedAt;
  final String updatedBy;
  final String? nearestStationCode;
  final String? distance;

  AttendanceEntry({
    required this.id,
    required this.username,
    required this.trainNumber,
    required this.originDate,
    required this.imageUrl,
    required this.stationCode,
    this.coachNo,
    required this.latitude,
    required this.longitude,
    required this.updatedAt,
    required this.updatedBy, 
    this.nearestStationCode, 
    this.distance,
  });

  factory AttendanceEntry.fromJson(Map<String, dynamic> json) {
    return AttendanceEntry(
        id: json['id'],
        username: json['username'],
        trainNumber: json['train_number'],
        originDate: json['origin_date'],
        imageUrl: json['image_url'],
        stationCode: json['station_code'],
        coachNo: json['coach_no'],
        latitude: json['latitude'],
        longitude: json['longitude'],
        updatedAt: json['updated_at'],
        updatedBy: json['updated_by']
      );
  }
}
