class AttendanceData {
  final int id;
  final String originDate;
  final String imageUrl;
  final String? stationCode;
  final String? coachNo;
  final String? latitude;
  final String? longitude;
  final String createdAt;
  final String createdBy;
  final String updatedAt;
  final String updatedBy;
  final int train;
  final int user;
  final String username;

  AttendanceData({
    required this.id,
    required this.originDate,
    required this.imageUrl,
    this.stationCode,
    this.coachNo,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.createdBy,
    required this.updatedAt,
    required this.updatedBy,
    required this.train,
    required this.user,
    required this.username
  });

  factory AttendanceData.fromJson(Map<String, dynamic> json) {
    return AttendanceData(
      id: json['id'],
      originDate: json['origin_date'] ?? '',
      imageUrl: json['image_url'] ?? '',
      stationCode: json['station_code'],
      coachNo: json['coach_no'],
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'] ?? '',
      createdBy: json['created_by'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      updatedBy: json['updated_by'] ?? '',
      train: json['train'],
      user: json['user'],
      username: json['username']
    );
  }
}
