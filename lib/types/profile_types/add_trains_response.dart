class AddTrainsResponse {
  final Map<String, AddTrainDetail> trainDetails;
  final Map<String, List<AddTrainDetail>> nonOngoingTrains;

  AddTrainsResponse({required this.trainDetails, required this.nonOngoingTrains});

  factory AddTrainsResponse.fromJson(Map<String, dynamic> json) {
    return AddTrainsResponse(
      trainDetails: (json['train_details'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, AddTrainDetail.fromJson(value, key)),
      ),
      nonOngoingTrains: (json['non_ongoing_trains'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(
          key,
          (value as List<dynamic>)
              .map((detail) => AddTrainDetail.fromJson(detail, key))
              .toList(),
        ),
      ),
    );
  }
}

class AddTrainDetail {
  final String trainNumber; // Added trainNumber
  final List<String> coachNumbers;
  final String originDate;

  AddTrainDetail({
    required this.trainNumber,
    required this.coachNumbers,
    required this.originDate,
  });

  factory AddTrainDetail.fromJson(Map<String, dynamic> json, String trainNumber) {
    return AddTrainDetail(
      trainNumber: trainNumber,
      coachNumbers: List<String>.from(json['coach_numbers']),
      originDate: json['origin_date'] as String,
    );
  }
}
