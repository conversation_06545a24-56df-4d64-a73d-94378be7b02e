class profileResponse {
  User? user;
  List<String>? posts;
  String? role;

  profileResponse({this.user, this.posts, this.role});

  profileResponse.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    posts = json['posts'] != null ? List<String>.from(json['posts']) : null;
    role = json['role'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['posts'] = posts;
    data['role'] = role;
    return data;
  }
}

class User {
  final String? id;
  final String? firstName;
  final String? middleName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? whatsappNumber;
  final String? empNumber;
  final String? depo;
  final String? secondaryPhone;

  User({
    this.id,
    this.firstName,
    this.middleName,
    this.lastName,
    this.email,
    this.phone,
    this.whatsappNumber,
    this.empNumber,
    this.depo,
    this.secondaryPhone,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      firstName: json['first_name'],
      middleName: json['middle_name'],
      lastName: json['last_name'],
      email: json['email'],
      phone: json['phone_number'],
      whatsappNumber: json['whatsapp_number'],
      empNumber: json['emp_number'],
      depo: json['depo'],
      secondaryPhone: json['secondary_phone'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'middle_name': middleName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'whatsapp_number': whatsappNumber,
      'emp_number': empNumber,
      'depo': depo,
      'secondary_phone': secondaryPhone,
    };
  }
}
