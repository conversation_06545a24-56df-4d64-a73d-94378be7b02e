class RequestProfile {
  final String firstName;
  final String middleName;
  final String lastName;
  final String empNumber;
  final String secondaryPhone;

  RequestProfile({
    required this.firstName,
    required this.middleName,
    required this.lastName,
    required this.empNumber,
    this.secondaryPhone = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'fname': firstName,
      'mname': middleName,
      'lname': lastName,
      'emp_number': empNumber,
      'secondary_phone': secondaryPhone,
    };
  }
}
