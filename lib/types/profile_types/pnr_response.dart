import 'dart:convert';

class PnrResponse {
  final String trainNumber;
  final String trainName;
  final String journeyFrom;
  final String journeyTo;
  final String dateOfJourney;
  final List<String> passengerNames;
  final List<String> passengerCoaches;
  final List<String> uniqueCoaches;
  final List<int> passengerBerths;
  final String overallStatus;
  final String bookingDate;
  final String journeyClass;
  final String departureTime;
  final String arrivalTime;
  final List<String> caUsernames;
  final List<String> obhsUsernames;
  final List<String> ehkUsernames;

  PnrResponse({
    required this.trainNumber,
    required this.trainName,
    required this.journeyFrom,
    required this.journeyTo,
    required this.dateOfJourney,
    required this.passengerNames,
    required this.passengerCoaches,
    required this.uniqueCoaches,
    required this.passengerBerths,
    required this.overallStatus,
    required this.bookingDate,
    required this.journeyClass,
    required this.departureTime,
    required this.arrivalTime,
    required this.caUsernames,
    required this.obhsUsernames,
    required this.ehkUsernames,
  });

  factory PnrResponse.fromJson(Map<String, dynamic> json) {
    // Function to handle null or empty date strings
    String parseDateTime(String? dateTime) {
      if (dateTime == null || dateTime.isEmpty)
        return "NONE"; // Return "NONE" for empty values
      return dateTime; // Keep as-is if valid
    }

    return PnrResponse(
      trainNumber: json['train_number'] ?? "",
      trainName: json['train_name'] ?? "",
      journeyFrom: json['journey_from'] ?? "",
      journeyTo: json['journey_to'] ?? "",
      dateOfJourney: json['date_of_journey'] ?? "",
      passengerNames: List<String>.from(json['passenger_names'] ?? []),
      passengerCoaches: List<String>.from(json['passenger_coaches'] ?? []),
      uniqueCoaches: List<String>.from(json['unique_coaches'] ?? []),
      passengerBerths: List<int>.from(json['passenger_berths'] ?? []),
      overallStatus: json['overall_status'] ?? "N/A",
      bookingDate: json['booking_date'] ?? "",
      journeyClass: json['journey_class'] ?? "",
      departureTime: parseDateTime(json['departure_time'] ?? ""),
      arrivalTime: parseDateTime(json['arrival_time'] ?? ""),
      caUsernames: List<String>.from(json['ca_usernames'] ?? []),
      obhsUsernames: List<String>.from(json['obhs_usernames'] ?? []),
      ehkUsernames: List<String>.from(json['ehk_usernames'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'train_number': trainNumber,
      'train_name': trainName,
      'journey_from': journeyFrom,
      'journey_to': journeyTo,
      'date_of_journey': dateOfJourney,
      'passenger_names': passengerNames,
      'passenger_coaches': passengerCoaches,
      'unique_coaches': uniqueCoaches,
      'passenger_berths': passengerBerths,
      'overall_status': overallStatus,
      'booking_date': bookingDate,
      'journey_class': journeyClass,
      'departure_time': departureTime,
      'arrival_time': arrivalTime,
      'ca_usernames': caUsernames,
      'obhs_usernames': obhsUsernames,
      'ehk_usernames': ehkUsernames,
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
