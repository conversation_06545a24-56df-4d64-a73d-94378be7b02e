class TrainChartingResponse {
  final String startTime;
  final String loadedAtEarliest;
  final String chartingTime;
  final String loadedAtRecent;
  final String date;
  final String trainNumber;
  final String depot;
  final String ehkDict;
  final List<String> refreshedTimes;
  final bool isRunningDay;

  TrainChartingResponse({
    required this.startTime,
    required this.loadedAtEarliest,
    required this.chartingTime,
    required this.loadedAtRecent,
    required this.date,
    required this.trainNumber,
    required this.depot,
    required this.ehkDict,
    required this.refreshedTimes,
    required this.isRunningDay,
  });

  factory TrainChartingResponse.fromJson(Map<String, dynamic> json) {
    String ehkName = "NA";
    if (json['ehk_dict'] != null && json['ehk_dict'] is Map<String, dynamic>) {
      Map<String, dynamic> ehkDict = json['ehk_dict'];
      ehkName = ehkDict.keys.isNotEmpty ? ehkDict.keys.first : "NA";
    }
    return TrainChartingResponse(
      startTime: json['start_time'] ?? "NA",
      loadedAtEarliest: json['loaded_at_earliest'] ?? "NA",
      chartingTime: json['charting_time'] ?? "NA",
      loadedAtRecent: json['loaded_at_recent'] ?? "NA",
      date: json['date'] ?? "NA",
      trainNumber: json['train_number'] ?? "NA",
      depot: json['depot'] ?? "NA",
      ehkDict: ehkName, 
       refreshedTimes: (json['all_loaded_at_times'] as List<dynamic>?)
            ?.map((e) => e.toString()) 
            .toList() ??
        [],
      isRunningDay: json['is_running_day'] ?? false,
    );
  }

}
