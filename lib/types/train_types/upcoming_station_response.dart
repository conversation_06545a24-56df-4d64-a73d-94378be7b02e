class UpcomingStationResponse {
  final String message;
  final List<String> stations;
  final String date;
  final String trainNumber;
  final List<String> coachNumbers;
  final Map<String, Map<String, List<int>>> details;
  final Map<String, Map<String, List<int>>> detailsOffBoarding;

  UpcomingStationResponse({
    required this.message,
    required this.stations,
    required this.date,
    required this.trainNumber,
    required this.coachNumbers,
    required this.details,
    required this.detailsOffBoarding,
  });

  factory UpcomingStationResponse.fromJson(Map<String, dynamic> json) {
    return UpcomingStationResponse(
      message: json['message'] ?? '',
      stations: List<String>.from(json['stations'] ?? []),
      date: json['date'] ?? '',
      trainNumber: json['train_number'] ?? '',
      coachNumbers: List<String>.from(json['coach_numbers'] ?? []),
      details: _parseDetails(json['details']),
      detailsOffBoarding: _parseDetails(json['details_off_boarding']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'stations': stations,
      'date': date,
      'train_number': trainNumber,
      'coach_numbers': coachNumbers,
      'details': details,
      'details_off_boarding': detailsOffBoarding,
    };
  }

  static Map<String, Map<String, List<int>>> _parseDetails(Map<String, dynamic>? json) {
    final Map<String, Map<String, List<int>>> details = {};
    if (json != null) {
      json.forEach((station, coaches) {
        details[station] = {};
        (coaches as Map<String, dynamic>).forEach((coach, berthNumbers) {
          details[station]![coach] = List<int>.from(berthNumbers);
        });
      });
    }
    return details;
  }
}
