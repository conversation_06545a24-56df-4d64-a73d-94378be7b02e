class TrainDataResponse {
  final List<TrainDetail> trainsData;
  final String startTime;
  final String endTime;
  final String chartingTime;
  final String chartingDay;

  TrainDataResponse({
    required this.trainsData,
    required this.startTime,
    required this.endTime,
    required this.chartingTime,
    required this.chartingDay,
  });

  factory TrainDataResponse.fromJson(Map<String, dynamic> json) {
    return TrainDataResponse(
      trainsData: (json['trains_data'] as List)
          .map((item) => TrainDetail.fromJson(item))
          .toList(),
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      chartingTime: json['charting_time'] ?? '',
      chartingDay: json['charting_day'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'trains_data': trainsData.map((item) => item.toJson()).toList(),
      'start_time': startTime,
      'end_time': endTime,
      'charting_time': chartingTime,
      'charting_day': chartingDay,
    };
  }
}


class TrainDetail {
  final String loadedAtEarliest;
  final String loadedAtRecent;
  final String trainNumber;
  final String date;
  final String stationCode;
  final List<BerthDetail> details;

  TrainDetail({
    required this.loadedAtEarliest,
    required this.loadedAtRecent,
    required this.trainNumber,
    required this.date,
    required this.stationCode,
    required this.details,
  });

  factory TrainDetail.fromJson(Map<String, dynamic> json) {
    return TrainDetail(
      loadedAtEarliest: json['loaded_at_earliest'] ?? '',
      loadedAtRecent: json['loaded_at_recent'] ?? '',
      trainNumber: json['train_number'].toString() ?? '',
      date: json['date'] ?? '',
      stationCode: json['station_code'] ?? '',
      details: (json['details'] as List)
          .map((item) => BerthDetail.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'loaded_at_earliest': loadedAtEarliest,
      'loaded_at_recent': loadedAtRecent,
      'train_number': trainNumber,
      'date': date,
      'station_code': stationCode,
      'details': details.map((item) => item.toJson()).toList(),
    };
  }
}

class BerthDetail {
  final String coachNumber;
  final int countOfBirth;
  final List<String> birthNumbers;
  final int offBoardingCountOfBirth;
  final List<String> offBoardingBirthNumbers;
  final int countOfVacantBerths;
  final List<String> vacantBerths;
  final List<String> initialBerths;
  final List<String> inrouteBerths;
  final List<String> offBoardingInitialBerths;
  final List<String> offBoardingInrouteBerths;


  BerthDetail({
    required this.coachNumber,
    required this.countOfBirth,
    required this.birthNumbers,
    required this.offBoardingCountOfBirth,
    required this.offBoardingBirthNumbers,
    required this.countOfVacantBerths,
    required this.vacantBerths,
    required this.initialBerths,
    required this.inrouteBerths,
    required this.offBoardingInitialBerths,
    required this.offBoardingInrouteBerths
  });

  factory BerthDetail.fromJson(Map<String, dynamic> json) {
    return BerthDetail(
      coachNumber: json['coach_number'] ?? '',
      countOfBirth: json['count_of_birth'] ?? 0,
      birthNumbers: List<String>.from((json['birth_numbers'] ?? []).map((e) => e.toString())),
      offBoardingCountOfBirth: json['off_boarding_count_of_birth'] ?? 0,
      offBoardingBirthNumbers: List<String>.from((json['off_boarding_birth_numbers'] ?? []).map((e) => e.toString())), 
      countOfVacantBerths: json['count_of_vacant_berths'] ?? 0,
      vacantBerths: List<String>.from((json['vacant_berths'] ?? []).map((e) => e.toString())),
      initialBerths: List<String>.from((json['initial_berths'] ?? []).map((e) => e.toString())),
      inrouteBerths: List<String>.from((json['inroute_berths'] ?? []).map((e) => e.toString())),
      offBoardingInitialBerths: List<String>.from((json['off_boarding_initial_berths'] ?? []).map((e) => e.toString())),
      offBoardingInrouteBerths: List<String>.from((json['off_boarding_inroute_berths'] ?? []).map((e) => e.toString())),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'coach_number': coachNumber,
      'count_of_birth': countOfBirth,
      'birth_numbers': birthNumbers,
      'off_boarding_count_of_birth': offBoardingCountOfBirth,
      'off_boarding_birth_numbers': offBoardingBirthNumbers,
      'count_of_vacant_berths': countOfVacantBerths,
      'vacant_berths': vacantBerths,
      'initial_berths': initialBerths,
      'inroute_berths': inrouteBerths,
      'off_boarding_initial_berths': offBoardingInitialBerths,
      'off_boarding_inroute_berths': offBoardingInrouteBerths
    };
  }
}