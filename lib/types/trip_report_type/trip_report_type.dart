class TripReportType {
  final int id;
  final String originDate;
  final List imageUrls;
  final String? latitude;
  final String? longitude;
  final String createdAt;
  final String createdBy;
  final String updatedAt;
  final String updatedBy;
  final int train;
  final String? coach;
  final String? issue;
  final List? videoUrls;
  final String? reportedBy;
  final String? reportedAt;
  final String? fixedBy;
  final String? fixedAt;
  final String? resolvedBy;
  final String? resolvedAt;
  final List<int>? selectedIssueIds;
  final List<int>? selectedSubIssueIds;
  String? comment;
  String? issueNames;
  String? subIssueNames;

  TripReportType(
      {required this.id,
      required this.originDate,
      required this.imageUrls,
      this.latitude,
      this.longitude,
      required this.createdAt,
      required this.createdBy,
      required this.updatedAt,
      required this.updatedBy,
      required this.train,
      this.coach,
      this.issue,
      this.videoUrls,
      this.fixedAt,
      this.fixedBy,
      this.reportedAt,
      this.reportedBy,
      this.resolvedAt,
      this.resolvedBy,
      this.selectedIssueIds,
      this.selectedSubIssueIds,
      this.comment,
      this.issueNames,
      this.subIssueNames
      });

  factory TripReportType.fromJson(Map<String, dynamic> json) {
    return TripReportType(
      id: json['id'],
      originDate: json['origin_date'] ?? '',
      imageUrls: json['image_urls'] ?? '',
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'] ?? '',
      createdBy: json['created_by'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      updatedBy: json['updated_by'] ?? '',
      train: json['train'],
      coach: json['coach'],
      issue: json['issue'],
      videoUrls: json['video_urls'],
      reportedBy: json['reported_by'],
      reportedAt: json['reported_at'],
      fixedBy: json['fixed_by'],
      fixedAt: json['fixed_at'],
      resolvedBy: json['resolved_by'],
      resolvedAt: json['resolved_at'],
      selectedIssueIds: json['selected_issue'] != null
          ? List<int>.from(json['selected_issue'])
          : null,
      selectedSubIssueIds: json['selected_sub_issue'] != null
          ? List<int>.from(json['selected_sub_issue'])
          : null,
      comment: json['comment'],
      issueNames: json['issue_names'],
      subIssueNames: json['sub_issue_names'],
    );
  }

  toJson() {}
}
