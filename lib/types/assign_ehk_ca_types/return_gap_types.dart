class ReturnGapData {
  final int? days;
  final String trainNo;
  final String createdBy;
  final String updatedBy;
  final String createdAt;
  final String updatedAt;
  final String inOut;

  ReturnGapData({
    required this.days,
    required this.trainNo,
    required this.createdBy,
    required this.updatedBy,
    required this.createdAt,
    required this.updatedAt,
    required this.inOut
  });

  factory ReturnGapData.fromJson(Map<String, dynamic> json) {
    return ReturnGapData(
      days: json['days'],
      inOut: json['in_out'],
      trainNo: json['train_no'].toString(),
      createdBy: json['created_by'],
      updatedBy: json['updated_by'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

class ReturnGapRequest {
  final int days;
  final String trainNo;
  final String token;
  final String inOut;

  ReturnGapRequest({
    required this.days,
    required this.trainNo,
    required this.token,
    required this.inOut
  });

  Map<String, dynamic> toJson() {
    return {
      "days": days,
      "train_no": trainNo,
      "token": token,
      "in_out": inOut,
    };
  }
}