class JobchartType {
  final int id;
  final String originDate;
  final String imageUrl;
  final String? latitude;
  final String? longitude;
  final String createdAt;
  final String createdBy;
  final String updatedAt;
  final String updatedBy;
  final int train;
  final String? coach;
  final String? issue;
  final String? videoUrl;
  final String? reportedBy;
  final String? reportedAt;
  final String? fixedBy;
  final String? fixedAt;
  final String? resolvedBy;
  final String? resolvedAt;

  JobchartType({
    required this.id,
    required this.originDate,
    required this.imageUrl,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.createdBy,
    required this.updatedAt,
    required this.updatedBy,
    required this.train,
    this.coach,
    this.issue,
    this.videoUrl,
    this.fixedAt,
    this.fixedBy,
    this.reportedAt,
    this.reportedBy,
    this.resolvedAt,
    this.resolvedBy,
  });

  factory JobchartType.fromJson(Map<String, dynamic> json) {
    return JobchartType(
      id: json['id'],
      originDate: json['origin_date'] ?? '',
      imageUrl: json['image_url'] ?? '',
      latitude: json['latitude'],
      longitude: json['longitude'],
      createdAt: json['created_at'] ?? '',
      createdBy: json['created_by'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      updatedBy: json['updated_by'] ?? '',
      train: json['train'],
      coach: json['coach'] ,
      issue: json['issue'],
      videoUrl: json['video_url'],
      reportedBy: json['reported_by'],
      reportedAt: json['reported_at'],
      fixedBy: json['fixed_by'],
      fixedAt: json['fixed_at'],
      resolvedBy: json['resolved_by'],
      resolvedAt: json['resolved_at'],
    );
  }
}
