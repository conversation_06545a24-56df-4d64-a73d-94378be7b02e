import 'dart:convert';

class JobChartStatus {
  final String trainNumber;
  final String date;
  final String statusFor;
  final String status;

  JobChartStatus({
    required this.trainNumber,
    required this.date,
    required this.statusFor,
    required this.status,
  });

  factory JobChartStatus.fromJson(Map<String, dynamic> json) {
    return JobChartStatus(
      trainNumber: json['train_number'],
      date: json['date'],
      statusFor: json['status_for'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'train_number': trainNumber,
      'date': date,
      'status_for': statusFor,
      'status': status,
    };
  }
}

class JobChartStatusRequest {
  final String trainNumber;
  final String date;
  final String statusFor;
  final String status;
  final String token;

  JobChartStatusRequest({
    required this.trainNumber,
    required this.date,
    required this.statusFor,
    required this.status,
    required this.token
  });

  Map<String, dynamic> toJson() {
    return {
      'train_number': trainNumber,
      'origin_date': date,
      'status_for': statusFor,
      'status': status,
      'token':token
    };
  }
}