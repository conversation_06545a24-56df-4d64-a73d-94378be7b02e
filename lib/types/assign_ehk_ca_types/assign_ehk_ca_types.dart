
class TrainCoachData {
  final List<String> coaches;
  final String trainNo;
  final String date;
  final Map<String, List<String>> coachWiseDict;
  final Map<String, List<String>> ehkDict;

  TrainCoachData({
    required this.coaches,
    required this.trainNo,
    required this.date,
    required this.coachWiseDict,
    required this.ehkDict,
  });

  factory TrainCoachData.fromJson(Map<String, dynamic> json) {
    return TrainCoachData(
      coaches: List<String>.from(json['coaches']),
      trainNo: json['train_no'],
      date: json['date'],
      coachWiseDict: Map<String, List<String>>.from(json['coach_wise_dict']),
      ehkDict: Map<String, List<String>>.from(json['ehk_dict']),
    );
  }
}