import 'package:flutter/material.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firebase_messaging_service.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';

/// Comprehensive FCM Integration Testing Widget
/// Use this to test FCM functionality step by step
class FcmIntegrationTestScreen extends StatefulWidget {
  const FcmIntegrationTestScreen({Key? key}) : super(key: key);

  @override
  State<FcmIntegrationTestScreen> createState() => _FcmIntegrationTestScreenState();
}

class _FcmIntegrationTestScreenState extends State<FcmIntegrationTestScreen> {
  String _testResults = '';
  bool _isLoading = false;

  void _addResult(String result) {
    setState(() {
      _testResults += '${DateTime.now().toIso8601String()}: $result\n';
    });
    print('FCM_TEST: $result');
  }

  Future<void> _testFcmTokenGeneration() async {
    setState(() {
      _isLoading = true;
      _testResults = '';
    });

    _addResult('=== FCM Token Generation Test ===');

    try {
      // Test 1: Get fresh FCM token
      _addResult('1. Testing fresh FCM token generation...');
      final freshToken = await FcmTokenService.getFreshFcmToken();
      if (freshToken != null) {
        _addResult('✅ Fresh FCM token generated successfully');
        _addResult('Token length: ${freshToken.length}');
        _addResult('Token preview: ${freshToken.substring(0, 50)}...');
      } else {
        _addResult('❌ Failed to generate fresh FCM token');
      }

      // Test 2: Get cached FCM token
      _addResult('2. Testing cached FCM token retrieval...');
      final cachedToken = await FcmTokenService.getFcmToken();
      if (cachedToken != null) {
        _addResult('✅ Cached FCM token retrieved successfully');
        _addResult('Tokens match: ${freshToken == cachedToken}');
      } else {
        _addResult('❌ No cached FCM token found');
      }

      // Test 3: Token validation
      _addResult('3. Testing FCM token validation...');
      final isValid = FcmTokenService.isValidFcmToken(freshToken);
      _addResult('Token is valid: $isValid');

      // Test 4: Device info collection
      _addResult('4. Testing device info collection...');
      final deviceInfo = await FcmTokenService.getDeviceInfo();
      _addResult('✅ Device info collected:');
      deviceInfo.forEach((key, value) {
        _addResult('  $key: $value');
      });

    } catch (e) {
      _addResult('❌ Error during FCM token testing: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testApiIntegration() async {
    setState(() {
      _isLoading = true;
      _testResults = '';
    });

    _addResult('=== API Integration Test ===');

    try {
      // Test API call with FCM token
      _addResult('1. Testing API call with FCM token...');
      
      // Use test coordinates (New Delhi Railway Station)
      const testLat = '28.6431';
      const testLng = '77.2197';
      const testToken = 'test-user-token'; // Replace with actual user token

      _addResult('Using test coordinates: $testLat, $testLng');
      _addResult('Using test user token: $testToken');

      final response = await UpcomingStationService.fetchUpcomingStationDetails(
        lat: testLat,
        lng: testLng,
        token: testToken,
      );

      _addResult('✅ API call completed successfully');
      _addResult('Response message: ${response.message}');
      _addResult('Stations count: ${response.stations.length}');

    } catch (e) {
      _addResult('❌ API call failed: $e');
      
      // Check if it's a network error vs FCM error
      final errorStr = e.toString().toLowerCase();
      if (errorStr.contains('firebase') || errorStr.contains('fcm')) {
        _addResult('🔍 This appears to be an FCM-related error');
      } else if (errorStr.contains('network') || errorStr.contains('connection')) {
        _addResult('🔍 This appears to be a network error');
      } else if (errorStr.contains('unauthorized') || errorStr.contains('401')) {
        _addResult('🔍 This appears to be an authentication error');
      }
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testTokenSync() async {
    setState(() {
      _isLoading = true;
      _testResults = '';
    });

    _addResult('=== Token Sync Test ===');

    try {
      const testUserToken = 'test-user-token'; // Replace with actual user token
      
      _addResult('1. Testing token sync with server...');
      final syncResult = await FcmTokenService.syncTokenWithServer(testUserToken);
      
      if (syncResult) {
        _addResult('✅ Token sync successful');
      } else {
        _addResult('❌ Token sync failed');
      }

      _addResult('2. Checking sync status...');
      final needsSync = await FcmTokenService.needsSync();
      _addResult('Needs sync: $needsSync');

      final lastSync = await FcmTokenService.getLastSyncTime();
      _addResult('Last sync: ${lastSync?.toIso8601String() ?? 'Never'}');

    } catch (e) {
      _addResult('❌ Error during token sync testing: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _testFirebaseMessagingService() async {
    setState(() {
      _isLoading = true;
      _testResults = '';
    });

    _addResult('=== Firebase Messaging Service Test ===');

    try {
      final messagingService = FirebaseMessagingService();
      
      _addResult('1. Testing FCM token retrieval from service...');
      final token = await messagingService.getFcmToken();
      
      if (token != null) {
        _addResult('✅ FCM token retrieved from messaging service');
        _addResult('Token length: ${token.length}');
      } else {
        _addResult('❌ Failed to retrieve FCM token from messaging service');
      }

      _addResult('2. Testing notification history...');
      final history = await messagingService.getNotificationHistory();
      _addResult('Notification history count: ${history.length}');

    } catch (e) {
      _addResult('❌ Error during Firebase Messaging Service testing: $e');
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM Integration Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFcmTokenGeneration,
                    child: const Text('Test FCM Token'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testApiIntegration,
                    child: const Text('Test API Integration'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testTokenSync,
                    child: const Text('Test Token Sync'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFirebaseMessagingService,
                    child: const Text('Test Messaging Service'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const CircularProgressIndicator()
            else
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      _testResults.isEmpty ? 'Click a test button to start testing...' : _testResults,
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
