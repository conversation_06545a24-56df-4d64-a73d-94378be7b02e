package com.biputri.railops

import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.media.MediaScannerConnection
import android.util.Log

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.biputri.railops/media_scanner"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "scanMedia") {
                val path: String? = call.argument("path")
                if (path != null) {
                    // Only scan if the file is a PDF
                    if (path.endsWith(".pdf", ignoreCase = true)) {
                        scanFile(path)
                        result.success(null)
                    } else {
                        // Return an error if the file is not a PDF
                        result.error("INVALID_FILE_TYPE", "Only PDF files are allowed", null)
                    }
                } else {
                    result.error("UNAVAILABLE", "Path not provided", null)
                }
            } else {
                result.notImplemented()
            }
        }
    }

    private fun scanFile(filePath: String) {
        MediaScannerConnection.scanFile(this, arrayOf(filePath), null) { path, uri ->
            Log.d("MediaScanner", "Scanned $path:")
            Log.d("MediaScanner", "-> uri=$uri")
        }
    }
}
