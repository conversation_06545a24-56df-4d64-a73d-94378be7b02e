import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:package_info_plus/package_info_plus.dart';

void main() {
  group('UpcomingStationService FCM Integration Tests', () {
    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      // Initialize SharedPreferences with empty values
      SharedPreferences.setMockInitialValues({});

      // Clear any cached device info from previous test runs
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('device_info');

      // Mock PackageInfo to avoid plugin errors
      PackageInfo.setMockInitialValues(
        appName: 'RailOps Test',
        packageName: 'com.biputri.railops.test',
        version: '1.0.0',
        buildNumber: '1',
        buildSignature: 'test-signature',
        installerStore: null,
      );
    });

    tearDown(() async {
      // Clear SharedPreferences after each test
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    });

    group('FCM Token Integration Tests', () {
      test('Should include FCM token in API request when available', () async {
        // Arrange
        const testFcmToken =
            'test-fcm-token:APA91bHun4MwP6pKuaANmLwpMPlqDCWcLjyggSxIIzK4NHGMHUXFdruUkn0dWjA';
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Save FCM token to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', testFcmToken);

        // Act & Assert - Should not throw due to FCM token handling
        // The test verifies that FCM integration doesn't break the service
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue, reason: 'Service completed successfully');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to FCM token logic
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related in backward compatibility mode');
          expect(errorMessage.contains('no firebase app'), isFalse,
              reason: 'Error should not be Firebase initialization-related');

          // The error should be network-related, not FCM-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason: 'Error should be network-related, not FCM-related');
        }
      });

      test('Should handle missing FCM token gracefully', () async {
        // Arrange
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Ensure no FCM token is stored
        final prefs = await SharedPreferences.getInstance();
        await prefs.remove('fcm_token');

        // Act & Assert - Should handle missing FCM token gracefully
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason: 'Service completed successfully without FCM token');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to missing FCM token
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related when FCM token is missing');
          expect(errorMessage.contains('fcm'), isFalse,
              reason: 'Error should not be FCM-related when token is missing');

          // The error should be network-related, not FCM-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason: 'Error should be network-related, not FCM-related');
        }
      });

      test('Should handle invalid FCM token gracefully', () async {
        // Arrange
        const invalidFcmToken = 'invalid-token';
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Save invalid FCM token to SharedPreferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', invalidFcmToken);

        // Act & Assert - Should handle invalid FCM token gracefully
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason: 'Service completed successfully with invalid FCM token');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to invalid FCM token
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related with invalid FCM token');
          expect(errorMessage.contains('fcm'), isFalse,
              reason: 'Error should not be FCM-related with invalid token');

          // The error should be network-related, not FCM-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason: 'Error should be network-related, not FCM-related');
        }
      });
    });

    group('Device Info Integration Tests', () {
      test('Should include device info in API request', () async {
        // Arrange
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Act - Test device info retrieval (should handle plugin errors gracefully)
        final deviceInfo = await FcmTokenService.getDeviceInfo();

        // Assert - Device info should have required fields
        // Our improved error handling should return fallback data instead of throwing
        expect(deviceInfo, isA<Map<String, dynamic>>());
        expect(deviceInfo.containsKey('platform'), isTrue,
            reason:
                'Device info should contain platform key. Got: $deviceInfo');
        expect(deviceInfo.containsKey('device_id'), isTrue,
            reason:
                'Device info should contain device_id key. Got: $deviceInfo');
        expect(deviceInfo.containsKey('app_version'), isTrue,
            reason:
                'Device info should contain app_version key. Got: $deviceInfo');

        // In test environment, we should get fallback values
        // Platform could be android, ios, or the actual test platform (e.g., macos)
        expect(deviceInfo['platform'], isNotNull);
        expect(deviceInfo['platform'], isNotEmpty);
        expect(deviceInfo['device_id'], isNotNull);
        expect(deviceInfo['app_version'], isNotNull);

        // Act & Assert - Should not throw when including device info
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason: 'Service completed successfully with device info');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to device info handling
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related when including device info');

          // The error should be network-related, not device-info-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason:
                  'Error should be network-related, not device-info-related');
        }
      });
    });

    group('API Request Structure Tests', () {
      test('Should validate request parameters', () async {
        // Test that required parameters are validated
        // These should not throw client-side validation errors

        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: '',
            lng: '77.2090',
            token: 'user-token',
          );
          expect(true, isTrue, reason: 'Service completed with empty lat');
        } catch (e) {
          // Should be network error, not parameter validation error
          expect(e.toString().toLowerCase().contains('failed to parse'), isTrue,
              reason: 'Should be network error for empty lat');
        }

        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: '28.6139',
            lng: '',
            token: 'user-token',
          );
          expect(true, isTrue, reason: 'Service completed with empty lng');
        } catch (e) {
          // Should be network error, not parameter validation error
          expect(e.toString().toLowerCase().contains('failed to parse'), isTrue,
              reason: 'Should be network error for empty lng');
        }

        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: '28.6139',
            lng: '77.2090',
            token: '',
          );
          expect(true, isTrue, reason: 'Service completed with empty token');
        } catch (e) {
          // Should be network error, not parameter validation error
          expect(e.toString().toLowerCase().contains('failed to parse'), isTrue,
              reason: 'Should be network error for empty token');
        }
      });
    });

    group('Error Handling Tests', () {
      test('Should handle FCM token service errors gracefully', () async {
        // This test ensures that errors in FCM token service don't break the main API call

        // Arrange
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Act & Assert - Should handle any FCM-related errors gracefully
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason: 'Service completed successfully despite FCM errors');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to FCM token handling
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related in error handling test');

          // The error should be network-related, not FCM-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason: 'Error should be network-related, not FCM-related');
        }
      });

      test('Should handle device info errors gracefully', () async {
        // This test ensures that errors in device info don't break the main API call

        // Arrange
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Act & Assert - Should handle device info errors gracefully
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason:
                  'Service completed successfully despite device info errors');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to device info handling
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related in device info error handling test');

          // The error should be network-related, not device-info-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason:
                  'Error should be network-related, not device-info-related');
        }
      });
    });

    group('Backward Compatibility Tests', () {
      test('Should maintain backward compatibility when FCM is not available',
          () async {
        // This test ensures that the service still works even if FCM is completely unavailable

        // Arrange
        const testLat = '28.6139';
        const testLng = '77.2090';
        const testUserToken = 'user-auth-token-123';

        // Clear any FCM-related data
        final prefs = await SharedPreferences.getInstance();
        await prefs.clear();

        // Act & Assert - Should work without FCM
        try {
          await UpcomingStationService.fetchUpcomingStationDetails(
            lat: testLat,
            lng: testLng,
            token: testUserToken,
          );
          // If we reach here, the service worked perfectly
          expect(true, isTrue,
              reason: 'Service completed successfully without FCM');
        } catch (e) {
          // Expected to fail due to network call in test environment
          // But should not fail due to FCM absence
          final errorMessage = e.toString().toLowerCase();
          expect(errorMessage.contains('firebase'), isFalse,
              reason:
                  'Error should not be Firebase-related when FCM is not available');
          expect(errorMessage.contains('fcm'), isFalse,
              reason:
                  'Error should not be FCM-related when FCM is not available');

          // The error should be network-related, not FCM-related
          expect(
              errorMessage.contains('failed to parse') ||
                  errorMessage.contains('network') ||
                  errorMessage.contains('connection') ||
                  errorMessage.contains('http'),
              isTrue,
              reason:
                  'Error should be network-related when FCM is not available');
        }
      });
    });
  });
}
