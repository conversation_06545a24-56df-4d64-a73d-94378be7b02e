import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

// This test file tests the notification history functionality
// that would be used by the FirebaseMessagingService
void main() {
  setUp(() async {
    // Set up shared preferences for testing
    SharedPreferences.setMockInitialValues({});
  });

  group('Notification History Tests', () {
    test('Should save and retrieve notification history', () async {
      // Arrange - Create test notification data
      final testNotificationData = {
        'id': 'test-id-123',
        'title': 'Test Title',
        'body': 'Test Body',
        'data': {'key': 'value'},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // Act - Save notification to history
      await saveNotificationToHistory(testNotificationData);

      // Act - Retrieve notification history
      final history = await getNotificationHistory();

      // Assert
      expect(history, isNotEmpty);
      expect(history.length, equals(1));
      expect(history[0]['id'], equals('test-id-123'));
      expect(history[0]['title'], equals('Test Title'));
      expect(history[0]['body'], equals('Test Body'));
    });

    test('Should clear notification history', () async {
      // Arrange - Create and save test notification data
      final testNotificationData = {
        'id': 'test-id-456',
        'title': 'Test Title',
        'body': 'Test Body',
        'data': {'key': 'value'},
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      await saveNotificationToHistory(testNotificationData);

      // Verify notification was saved
      final historyBefore = await getNotificationHistory();
      expect(historyBefore, isNotEmpty);

      // Act - Clear notification history
      await clearNotificationHistory();

      // Act - Retrieve notification history
      final historyAfter = await getNotificationHistory();

      // Assert
      expect(historyAfter, isEmpty);
    });

    test('Should handle multiple notifications correctly', () async {
      // Arrange - Create test notification data
      final testNotifications = [
        {
          'id': 'notification-1',
          'title': 'First Notification',
          'body': 'This is the first notification',
          'data': {'route': '/first'},
          'timestamp': DateTime.now().millisecondsSinceEpoch - 3000,
        },
        {
          'id': 'notification-2',
          'title': 'Second Notification',
          'body': 'This is the second notification',
          'data': {'route': '/second'},
          'timestamp': DateTime.now().millisecondsSinceEpoch - 2000,
        },
        {
          'id': 'notification-3',
          'title': 'Third Notification',
          'body': 'This is the third notification',
          'data': {'route': '/third'},
          'timestamp': DateTime.now().millisecondsSinceEpoch - 1000,
        },
      ];

      // Act - Save notifications
      for (final notification in testNotifications) {
        await saveNotificationToHistory(notification);
      }

      // Act - Retrieve notification history
      final history = await getNotificationHistory();

      // Assert
      expect(history.length, equals(3));
      expect(history[0]['id'], equals('notification-1'));
      expect(history[1]['id'], equals('notification-2'));
      expect(history[2]['id'], equals('notification-3'));
    });
  });
}

// Helper functions that mimic the FirebaseMessagingService functionality
Future<void> saveNotificationToHistory(
    Map<String, dynamic> notification) async {
  final prefs = await SharedPreferences.getInstance();
  final history = prefs.getStringList('notification_history') ?? [];

  history.add(json.encode(notification));
  await prefs.setStringList('notification_history', history);
}

Future<List<Map<String, dynamic>>> getNotificationHistory() async {
  final prefs = await SharedPreferences.getInstance();
  final history = prefs.getStringList('notification_history') ?? [];

  return history
      .map((item) => json.decode(item) as Map<String, dynamic>)
      .toList();
}

Future<void> clearNotificationHistory() async {
  final prefs = await SharedPreferences.getInstance();
  await prefs.remove('notification_history');
}
