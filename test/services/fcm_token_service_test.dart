import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/fcm_token_service.dart';

void main() {
  group('FcmTokenService Tests', () {
    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      // Initialize SharedPreferences with empty values
      SharedPreferences.setMockInitialValues({});
    });

    tearDown(() async {
      // Clear SharedPreferences after each test
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
    });

    group('Token Storage Tests', () {
      test('Should save and retrieve FCM token', () async {
        // Arrange
        const testToken =
            'test-fcm-token-123:APA91bHun4MwP6pKuaANmLwpMPlqDCWcLjyggSxIIzK4NHGMHUXFdruUkn0dWjA';

        // Act - Save token using private method (we'll test through public methods)
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', testToken);

        // Act - Retrieve token
        final retrievedToken = await FcmTokenService.getFcmToken();

        // Assert
        expect(retrievedToken, equals(testToken));
      });

      test('Should return null when no token is stored', () async {
        // Act
        final token = await FcmTokenService.getFcmToken();

        // Assert
        expect(token, isNull);
      });

      test('Should clear token correctly', () async {
        // Arrange - Save a token first
        const testToken =
            'test-fcm-token-to-clear:APA91bHun4MwP6pKuaANmLwpMPlqDCWcLjyggSxIIzK4NHGMHUXFdruUkn0dWjA';
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', testToken);

        // Verify token is saved
        final savedToken = await FcmTokenService.getFcmToken();
        expect(savedToken, equals(testToken));

        // Act - Clear token
        await FcmTokenService.clearToken();

        // Assert
        final retrievedToken = await FcmTokenService.getFcmToken();
        expect(retrievedToken, isNull);
      });
    });

    group('Token Validation Tests', () {
      test('Should validate correct FCM token format', () {
        // Arrange - Use a more realistic FCM token format (longer than 100 chars with colon)
        const validToken =
            'dGVzdC1mY206QVBBOTFiSHVuNE13UDZwS3VhQU5tTHdwTVBscURDV2NManlnZ1N4SUl6SzROSEdNSFVYRmRydVVrbjBkV2pBdGVzdC1mY206QVBBOTFiSHVuNE13UDZwS3VhQU5tTHdwTVBscURDV2NManlnZ1N4SUl6SzROSEdNSFVYRmRydVVrbjBkV2pB:APA91bHun4MwP6pKuaANmLwpMPlqDCWcLjyggSxIIzK4NHGMHUXFdruUkn0dWjA';

        // Act & Assert
        expect(FcmTokenService.isValidFcmToken(validToken), isTrue);
      });

      test('Should reject invalid FCM token formats', () {
        // Test cases for invalid tokens
        const invalidTokens = [
          null,
          '',
          'short',
          'no-colon-token-that-is-long-enough-but-missing-colon',
          'valid:but-too-short',
        ];

        for (final token in invalidTokens) {
          expect(FcmTokenService.isValidFcmToken(token), isFalse,
              reason: 'Token "$token" should be invalid');
        }
      });
    });

    group('Device Info Tests', () {
      test('Should return device info with required fields', () async {
        // Act
        final deviceInfo = await FcmTokenService.getDeviceInfo();

        // Assert
        expect(deviceInfo, isA<Map<String, dynamic>>());
        expect(deviceInfo.containsKey('platform'), isTrue);
        expect(deviceInfo.containsKey('device_id'), isTrue);
        expect(deviceInfo.containsKey('app_version'), isTrue);

        // Platform should be either 'android' or 'ios'
        expect(['android', 'ios'].contains(deviceInfo['platform']), isTrue);
      });

      test('Should cache device info', () async {
        // Act - Get device info twice
        final deviceInfo1 = await FcmTokenService.getDeviceInfo();
        final deviceInfo2 = await FcmTokenService.getDeviceInfo();

        // Assert - Should be the same (cached)
        expect(deviceInfo1, equals(deviceInfo2));
      });
    });

    group('Sync Status Tests', () {
      test('Should indicate sync is needed when no last sync time', () async {
        // Act
        final needsSync = await FcmTokenService.needsSync();

        // Assert
        expect(needsSync, isTrue);
      });

      test('Should indicate sync is not needed when recently synced', () async {
        // Arrange - Set recent sync time
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(
            'fcm_last_sync', DateTime.now().millisecondsSinceEpoch);

        // Act
        final needsSync = await FcmTokenService.needsSync();

        // Assert
        expect(needsSync, isFalse);
      });

      test('Should indicate sync is needed when sync is old', () async {
        // Arrange - Set old sync time (more than 24 hours ago)
        final prefs = await SharedPreferences.getInstance();
        final oldTime = DateTime.now().subtract(const Duration(hours: 25));
        await prefs.setInt('fcm_last_sync', oldTime.millisecondsSinceEpoch);

        // Act
        final needsSync = await FcmTokenService.needsSync();

        // Assert
        expect(needsSync, isTrue);
      });

      test('Should return last sync time correctly', () async {
        // Arrange
        final testTime = DateTime.now();
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt('fcm_last_sync', testTime.millisecondsSinceEpoch);

        // Act
        final lastSyncTime = await FcmTokenService.getLastSyncTime();

        // Assert
        expect(lastSyncTime, isNotNull);
        expect(lastSyncTime!.millisecondsSinceEpoch,
            equals(testTime.millisecondsSinceEpoch));
      });

      test('Should return null when no sync time is stored', () async {
        // Act
        final lastSyncTime = await FcmTokenService.getLastSyncTime();

        // Assert
        expect(lastSyncTime, isNull);
      });
    });

    group('Error Handling Tests', () {
      test('Should handle SharedPreferences errors gracefully', () async {
        // This test verifies that the service doesn't crash on SharedPreferences errors
        // In a real scenario, you might mock SharedPreferences to throw errors

        // Act & Assert - Should not throw
        expect(
            () async => await FcmTokenService.getFcmToken(), returnsNormally);
        expect(() async => await FcmTokenService.clearToken(), returnsNormally);
        expect(() async => await FcmTokenService.needsSync(), returnsNormally);
      });
    });
  });
}
