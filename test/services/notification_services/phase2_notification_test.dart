import 'package:flutter_test/flutter_test.dart';
import 'package:railops/models/onboarding_notification_model.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';

/// Test file for Phase 2 notification functionality
/// This demonstrates the new enhanced trigger logic and notification categories
void main() {
  group('Phase 2 Notification Tests', () {
    setUpAll(() {
      // Configure Phase 2 notification settings for testing
      NotificationIntegrationHelper.configureProximityNotifications(
        proximityThresholdKm: 3.0,
        locationUpdateInterval: Duration(minutes: 1),
        enableLocationMonitoring: true,
      );

      NotificationIntegrationHelper.configurePassengerCountNotifications(
        passengerCountThreshold: 8,
        enableBoardingCountUpdates: true,
        enableOffBoardingPreparation: true,
      );

      NotificationIntegrationHelper.configureTrainStatusMonitoring(
        monitoredStatusTypes: ['delay', 'cancellation', 'platform_change'],
        statusCheckInterval: Duration(minutes: 3),
        enableScheduleChangeAlerts: true,
      );
    });

    group('Enhanced Notification Types', () {
      test('should create station approach alert notification', () {
        // Test the new station approach alert notification type
        expect(OnboardingNotificationType.stationApproachAlert, isNotNull);
        expect(OnboardingNotificationType.stationApproachAlert.name, 'stationApproachAlert');
      });

      test('should create boarding count update notification', () {
        // Test the new boarding count update notification type
        expect(OnboardingNotificationType.boardingCountUpdate, isNotNull);
        expect(OnboardingNotificationType.boardingCountUpdate.name, 'boardingCountUpdate');
      });

      test('should create off-boarding preparation notification', () {
        // Test the new off-boarding preparation notification type
        expect(OnboardingNotificationType.offBoardingPreparation, isNotNull);
        expect(OnboardingNotificationType.offBoardingPreparation.name, 'offBoardingPreparation');
      });

      test('should create train status update notification', () {
        // Test the new train status update notification type
        expect(OnboardingNotificationType.trainStatusUpdate, isNotNull);
        expect(OnboardingNotificationType.trainStatusUpdate.name, 'trainStatusUpdate');
      });

      test('should create proximity alert notification', () {
        // Test the new proximity alert notification type
        expect(OnboardingNotificationType.proximityAlert, isNotNull);
        expect(OnboardingNotificationType.proximityAlert.name, 'proximityAlert');
      });
    });

    group('Enhanced Trigger Types', () {
      test('should create passenger count threshold trigger', () {
        final trigger = NotificationTrigger.passengerCountThreshold(10);
        expect(trigger.type, NotificationTriggerType.passengerCountThreshold);
        expect(trigger.conditions['passenger_threshold'], 10);
      });

      test('should create train status change trigger', () {
        final trigger = NotificationTrigger.trainStatusChange(
          statusTypes: ['delay', 'cancellation'],
          debounceDelay: Duration(minutes: 2),
        );
        expect(trigger.type, NotificationTriggerType.trainStatusChange);
        expect(trigger.conditions['status_types'], ['delay', 'cancellation']);
        expect(trigger.delay, Duration(minutes: 2));
      });

      test('should create schedule change trigger', () {
        final trigger = NotificationTrigger.scheduleChange(
          minimumDelayThreshold: Duration(minutes: 10),
          includeAdvanceNotifications: true,
        );
        expect(trigger.type, NotificationTriggerType.scheduleChange);
        expect(trigger.conditions['minimum_delay_minutes'], 10);
        expect(trigger.conditions['include_advance_notifications'], true);
      });
    });

    group('Test Notification Methods', () {
      test('should send test proximity notification', () async {
        // This test verifies that the test method works without throwing errors
        expect(() async {
          await NotificationIntegrationHelper.sendTestProximityNotification(
            stationName: 'New Delhi',
            distanceKm: 2.5,
          );
        }, returnsNormally);
      });

      test('should send test station approach alert', () async {
        // This test verifies that the test method works without throwing errors
        expect(() async {
          await NotificationIntegrationHelper.sendTestStationApproachAlert(
            stationName: 'Mumbai Central',
            minutesBeforeArrival: 5,
          );
        }, returnsNormally);
      });

      test('should send test train status update', () async {
        // This test verifies that the test method works without throwing errors
        expect(() async {
          await NotificationIntegrationHelper.sendTestTrainStatusUpdate(
            trainNumber: '12345',
            statusType: 'delay',
            statusMessage: 'Train delayed by 15 minutes due to signal issues',
          );
        }, returnsNormally);
      });

      test('should send test boarding count update', () async {
        // This test verifies that the test method works without throwing errors
        expect(() async {
          await NotificationIntegrationHelper.sendTestBoardingCountUpdate(
            stationName: 'Pune Junction',
            currentCount: 12,
            previousCount: 8,
            coaches: ['A1', 'B2', 'C3'],
          );
        }, returnsNormally);
      });
    });

    group('Configuration Tests', () {
      test('should configure proximity notifications', () {
        expect(() {
          NotificationIntegrationHelper.configureProximityNotifications(
            proximityThresholdKm: 5.0,
            locationUpdateInterval: Duration(minutes: 2),
            enableLocationMonitoring: true,
          );
        }, returnsNormally);
      });

      test('should configure passenger count notifications', () {
        expect(() {
          NotificationIntegrationHelper.configurePassengerCountNotifications(
            passengerCountThreshold: 15,
            enableBoardingCountUpdates: true,
            enableOffBoardingPreparation: true,
          );
        }, returnsNormally);
      });

      test('should configure train status monitoring', () {
        expect(() {
          NotificationIntegrationHelper.configureTrainStatusMonitoring(
            monitoredStatusTypes: ['delay', 'cancellation', 'emergency'],
            statusCheckInterval: Duration(minutes: 5),
            enableScheduleChangeAlerts: true,
          );
        }, returnsNormally);
      });
    });
  });
}
