import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../mocks/firebase_messaging_mock.dart';

void main() {
  setUp(() async {
    // Set up shared preferences for testing
    SharedPreferences.setMockInitialValues({});
  });

  group('FCM Token Storage Tests', () {
    test('Should save and retrieve FCM token', () async {
      // Arrange
      const testToken = 'test-fcm-token-123';

      // Act - Save token
      await FcmTokenStorage.saveToken(testToken);

      // Act - Retrieve token
      final retrievedToken = await FcmTokenStorage.getToken();

      // Assert
      expect(retrievedToken, equals(testToken));
    });

    test('Should return null when token is not set', () async {
      // Act - Clear any existing token
      await FcmTokenStorage.clearToken();

      // Act - Retrieve token
      final retrievedToken = await FcmTokenStorage.getToken();

      // Assert
      expect(retrievedToken, isNull);
    });

    test('Should clear token correctly', () async {
      // Arrange - Save a token
      const testToken = 'test-fcm-token-to-clear';
      await FcmTokenStorage.saveToken(testToken);

      // Verify token is saved
      final savedToken = await FcmTokenStorage.getToken();
      expect(savedToken, equals(testToken));

      // Act - Clear token
      await FcmTokenStorage.clearToken();

      // Assert
      final retrievedToken = await FcmTokenStorage.getToken();
      expect(retrievedToken, isNull);
    });
  });

  group('Notification History Storage Tests', () {
    test('Should save and retrieve notification', () async {
      // Arrange
      final testNotification = TestNotification.create(
        title: 'Test Notification',
        body: 'This is a test notification',
      );

      // Act - Save notification
      await NotificationHistoryStorage.saveNotification(testNotification);

      // Act - Retrieve notifications
      final notifications = await NotificationHistoryStorage.getNotifications();

      // Assert
      expect(notifications, isNotEmpty);
      expect(notifications.length, equals(1));
      expect(notifications[0]['title'], equals('Test Notification'));
      expect(notifications[0]['body'], equals('This is a test notification'));
    });

    test('Should save multiple notifications and retrieve them in order',
        () async {
      // Arrange
      final testNotifications = [
        TestNotification.create(title: 'Notification 1', body: 'Body 1'),
        TestNotification.create(title: 'Notification 2', body: 'Body 2'),
        TestNotification.create(title: 'Notification 3', body: 'Body 3'),
      ];

      // Act - Save notifications
      for (final notification in testNotifications) {
        await NotificationHistoryStorage.saveNotification(notification);
      }

      // Act - Retrieve notifications
      final notifications = await NotificationHistoryStorage.getNotifications();

      // Assert
      expect(notifications.length, equals(3));
      expect(notifications[0]['title'], equals('Notification 1'));
      expect(notifications[1]['title'], equals('Notification 2'));
      expect(notifications[2]['title'], equals('Notification 3'));
    });

    test('Should clear notification history correctly', () async {
      // Arrange - Save some notifications
      await NotificationHistoryStorage.saveNotification(
        TestNotification.create(title: 'Test Notification'),
      );

      // Verify notification is saved
      final savedNotifications =
          await NotificationHistoryStorage.getNotifications();
      expect(savedNotifications, isNotEmpty);

      // Act - Clear history
      await NotificationHistoryStorage.clearHistory();

      // Assert
      final notifications = await NotificationHistoryStorage.getNotifications();
      expect(notifications, isEmpty);
    });
  });

  group('Test Notification Creation', () {
    test('Should create notification with default values', () {
      // Act
      final notification = TestNotification.create();

      // Assert
      expect(notification['title'], equals('Test Title'));
      expect(notification['body'], equals('Test Body'));
      expect(notification['data'], equals({'key': 'value'}));
      expect(notification['isRead'], equals(false));
      expect(notification['id'], isNotNull);
      expect(notification['timestamp'], isNotNull);
    });

    test('Should create notification with custom values', () {
      // Arrange
      final customData = {'customKey': 'customValue'};
      final timestamp = DateTime.now().millisecondsSinceEpoch;

      // Act
      final notification = TestNotification.create(
        id: 'custom-id',
        title: 'Custom Title',
        body: 'Custom Body',
        data: customData,
        timestamp: timestamp,
        isRead: true,
      );

      // Assert
      expect(notification['id'], equals('custom-id'));
      expect(notification['title'], equals('Custom Title'));
      expect(notification['body'], equals('Custom Body'));
      expect(notification['data'], equals(customData));
      expect(notification['timestamp'], equals(timestamp));
      expect(notification['isRead'], equals(true));
    });
  });
}
