import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../mocks/firebase_messaging_mock.dart';

void main() {
  setUp(() async {
    // Set up shared preferences for testing
    SharedPreferences.setMockInitialValues({});

    // Clear any existing data
    await NotificationHistoryStorage.clearHistory();
  });

  group('Notification Processing Tests', () {
    test('Should process and store notification correctly', () async {
      // Arrange
      final testNotification = TestNotification.create(
        id: 'notification-1',
        title: 'New Message',
        body: 'You have a new message',
        data: {'route': '/chat', 'sender': '<PERSON>'},
      );

      // Act - Save notification to history
      await NotificationHistoryStorage.saveNotification(testNotification);

      // Act - Retrieve notifications
      final notifications = await NotificationHistoryStorage.getNotifications();

      // Assert
      expect(notifications, isNotEmpty);
      expect(notifications.length, equals(1));
      expect(notifications[0]['id'], equals('notification-1'));
      expect(notifications[0]['title'], equals('New Message'));
      expect(notifications[0]['body'], equals('You have a new message'));
      expect(notifications[0]['data']['route'], equals('/chat'));
      expect(notifications[0]['data']['sender'], equals('John Doe'));
    });

    test('Should handle multiple notifications correctly', () async {
      // Arrange
      final testNotifications = [
        TestNotification.create(
          id: 'notification-1',
          title: 'Train Delay',
          body: 'Your train is delayed by 10 minutes',
          data: {'route': '/train-status', 'trainNo': '12345'},
        ),
        TestNotification.create(
          id: 'notification-2',
          title: 'Platform Change',
          body: 'Your train will arrive at platform 3',
          data: {'route': '/platform-info', 'platform': '3'},
        ),
        TestNotification.create(
          id: 'notification-3',
          title: 'Maintenance Alert',
          body: 'Scheduled maintenance on track',
          data: {'route': '/maintenance', 'trackId': 'T-123'},
        ),
      ];

      // Act - Save notifications
      for (final notification in testNotifications) {
        await NotificationHistoryStorage.saveNotification(notification);
      }

      // Act - Retrieve notifications
      final notifications = await NotificationHistoryStorage.getNotifications();

      // Assert
      expect(notifications.length, equals(3));

      // Check first notification
      expect(notifications[0]['id'], equals('notification-1'));
      expect(notifications[0]['title'], equals('Train Delay'));
      expect(notifications[0]['data']['trainNo'], equals('12345'));

      // Check second notification
      expect(notifications[1]['id'], equals('notification-2'));
      expect(notifications[1]['title'], equals('Platform Change'));
      expect(notifications[1]['data']['platform'], equals('3'));

      // Check third notification
      expect(notifications[2]['id'], equals('notification-3'));
      expect(notifications[2]['title'], equals('Maintenance Alert'));
      expect(notifications[2]['data']['trackId'], equals('T-123'));
    });
  });

  group('Notification Data Handling Tests', () {
    test('Should handle notification with complex data', () async {
      // Arrange
      final complexData = {
        'route': '/train-details',
        'trainInfo': {
          'trainNo': '12345',
          'name': 'Express',
          'stations': ['Station A', 'Station B', 'Station C'],
          'schedule': {
            'departure': '10:00',
            'arrival': '14:30',
          },
        },
        'isUrgent': true,
        'priority': 1,
      };

      final testNotification = TestNotification.create(
        id: 'complex-notification',
        title: 'Train Information',
        body: 'Details about your train',
        data: complexData,
      );

      // Act - Save notification
      await NotificationHistoryStorage.saveNotification(testNotification);

      // Act - Retrieve notifications
      final notifications = await NotificationHistoryStorage.getNotifications();

      // Assert
      expect(notifications, isNotEmpty);
      expect(notifications[0]['data']['route'], equals('/train-details'));
      expect(notifications[0]['data']['isUrgent'], equals(true));
      expect(notifications[0]['data']['priority'], equals(1));

      // Note: Complex nested objects might not be preserved exactly as is
      // due to JSON serialization/deserialization in SharedPreferences
    });
  });
}
