import 'dart:async';
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A simple class to store FCM token for testing
class FcmTokenStorage {
  static const String _tokenKey = 'fcm_token';

  /// Save FCM token to shared preferences
  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  /// Get FCM token from shared preferences
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// Clear FCM token from shared preferences
  static Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }
}

/// A simple class to store notification history for testing
class NotificationHistoryStorage {
  static const String _historyKey = 'notification_history';

  /// Save a notification to history
  static Future<void> saveNotification(
      Map<String, dynamic> notification) async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList(_historyKey) ?? [];

    history.add(json.encode(notification));
    await prefs.setStringList(_historyKey, history);
  }

  /// Get all notifications from history
  static Future<List<Map<String, dynamic>>> getNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    final history = prefs.getStringList(_historyKey) ?? [];

    return history
        .map((item) => json.decode(item) as Map<String, dynamic>)
        .toList();
  }

  /// Clear notification history
  static Future<void> clearHistory() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_historyKey);
  }
}

/// A simple class to create test notification data
class TestNotification {
  /// Create a simple notification data map
  static Map<String, dynamic> create({
    String? id,
    String? title,
    String? body,
    Map<String, dynamic>? data,
    int? timestamp,
    bool? isRead,
  }) {
    return {
      'id': id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      'title': title ?? 'Test Title',
      'body': body ?? 'Test Body',
      'data': data ?? {'key': 'value'},
      'timestamp': timestamp ?? DateTime.now().millisecondsSinceEpoch,
      'isRead': isRead ?? false,
    };
  }
}
