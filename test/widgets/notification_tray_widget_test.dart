import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/widgets/notification_tray_widget.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/models/notification_tray_model.dart';

void main() {
  group('NotificationTrayWidget', () {
    late NotificationTrayProvider provider;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      provider = NotificationTrayProvider();
      await provider.initialize();
    });

    Widget createTestWidget({bool showHeader = true, bool showActions = true}) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<NotificationTrayProvider>.value(
            value: provider,
            child: NotificationTrayWidget(
              showHeader: showHeader,
              showActions: showActions,
            ),
          ),
        ),
      );
    }

    testWidgets('should display empty state when no notifications',
        (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('No Notifications'), findsOneWidget);
      expect(find.text('Train location notifications will appear here'),
          findsOneWidget);
      expect(find.byIcon(Icons.notifications_none), findsOneWidget);
    });

    testWidgets('should display header when showHeader is true',
        (tester) async {
      // Add some test data first so header is shown
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget(showHeader: true));
      await tester.pump(); // Allow provider to update

      expect(find.text('Notification Tray'), findsOneWidget);
    });

    testWidgets('should hide header when showHeader is false', (tester) async {
      await tester.pumpWidget(createTestWidget(showHeader: false));

      expect(find.text('Notification Tray'), findsNothing);
    });

    testWidgets('should display actions when showActions is true',
        (tester) async {
      // Add some test data first
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget(showActions: true));
      await tester.pump(); // Allow provider to update

      expect(find.text('Mark All Read'), findsOneWidget);
      expect(find.text('Refresh'), findsOneWidget);
      expect(find.text('Clear'), findsOneWidget);
    });

    testWidgets('should hide actions when showActions is false',
        (tester) async {
      // Add some test data first
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget(showActions: false));
      await tester.pump(); // Allow provider to update

      expect(find.text('Mark All Read'), findsNothing);
      expect(find.text('Refresh'), findsNothing);
      expect(find.text('Clear'), findsNothing);
    });

    testWidgets('should display notification table with correct data',
        (tester) async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      // Check table headers
      expect(find.text('Station'), findsOneWidget);
      expect(find.text('Coach'), findsOneWidget);
      expect(find.text('On'), findsOneWidget);
      expect(find.text('Off'), findsOneWidget);
      expect(find.text('Vacant'), findsOneWidget);

      // Check data rows
      expect(find.text('DDU'), findsAtLeastNWidgets(2)); // Two DDU entries
      expect(find.text('A1'), findsOneWidget);
      expect(find.text('B3'), findsOneWidget);
      expect(find.text('5'), findsOneWidget); // Onboarding count for A1
      expect(
          find.text('6'),
          findsAtLeastNWidgets(
              2)); // Onboarding count for B3 and vacant count for B3
    });

    testWidgets('should display unread count badge', (tester) async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      expect(
          find.text('2'),
          findsAtLeastNWidgets(
              1)); // Unread count badge (may appear in multiple places)
    });

    testWidgets('should mark item as read when tapped', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      expect(provider.unreadCount, 1);

      // Tap on the notification row
      await tester.tap(find.text('DDU').first);
      await tester.pump();

      expect(provider.unreadCount, 0);
    });

    testWidgets('should show color-coded count chips', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      // Check for colored indicators in header
      expect(find.byType(Container), findsAtLeastNWidgets(1));

      // The color chips should be present (green, orange, grey circles)
      final containers = tester.widgetList<Container>(find.byType(Container));
      final coloredContainers = containers.where((container) {
        final decoration = container.decoration as BoxDecoration?;
        return decoration?.shape == BoxShape.circle;
      });

      expect(coloredContainers.length,
          greaterThanOrEqualTo(3)); // At least 3 colored circles
    });

    testWidgets('should handle mark all as read action', (tester) async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      expect(provider.unreadCount, 2);

      // Tap mark all as read button
      await tester.tap(find.text('Mark All Read'));
      await tester.pump();

      expect(provider.unreadCount, 0);
    });

    testWidgets('should handle refresh action', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      // Tap refresh button
      await tester.tap(find.text('Refresh'));
      await tester.pump();

      // Should not throw any errors
      expect(provider.items.length, 1);
    });

    testWidgets('should show clear confirmation dialog', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      // Tap clear button
      await tester.tap(find.text('Clear'));
      await tester.pumpAndSettle();

      // Should show confirmation dialog
      expect(find.text('Clear All Notifications'), findsOneWidget);
      expect(find.textContaining('Are you sure you want to clear'),
          findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Clear All'), findsOneWidget);
    });

    testWidgets('should clear all items when confirmed', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      expect(provider.items.length, 1);

      // Tap clear button
      await tester.tap(find.text('Clear'));
      await tester.pumpAndSettle();

      // Confirm clear action
      await tester.tap(find.text('Clear All'));
      await tester.pumpAndSettle();

      expect(provider.items.length, 0);
    });

    testWidgets('should cancel clear action', (tester) async {
      final items = [_createTestItem('test_1', 'DDU', 'A1', 5, 3, 2)];
      await provider.addItems(items);

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow provider to update

      expect(provider.items.length, 1);

      // Tap clear button
      await tester.tap(find.text('Clear'));
      await tester.pumpAndSettle();

      // Cancel clear action
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      expect(provider.items.length, 1); // Should remain unchanged
    });
  });

  group('NotificationTraySummaryWidget', () {
    late NotificationTrayProvider provider;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      provider = NotificationTrayProvider();
      await provider.initialize();
    });

    Widget createSummaryWidget({VoidCallback? onTap}) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<NotificationTrayProvider>.value(
            value: provider,
            child: NotificationTraySummaryWidget(onTap: onTap),
          ),
        ),
      );
    }

    testWidgets('should display summary widget with no notifications',
        (tester) async {
      await tester.pumpWidget(createSummaryWidget());

      expect(find.text('Train Notifications'), findsOneWidget);
      expect(find.text('No active notifications'), findsOneWidget);
      expect(find.byIcon(Icons.train), findsOneWidget);
    });

    testWidgets('should display summary with active notifications',
        (tester) async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];
      await provider.addItems(items);

      await tester.pumpWidget(createSummaryWidget());
      await tester.pump(); // Allow provider to update

      expect(find.text('Train Notifications'), findsOneWidget);
      expect(find.text('No active notifications'), findsNothing);

      // Should show summary chips with counts
      expect(find.text('11'), findsOneWidget); // Total onboarding (5+6)
      expect(find.text('6'), findsOneWidget); // Total offboarding (3+3)
      expect(find.text('8'), findsOneWidget); // Total vacant (2+6)
    });

    testWidgets('should display unread count badge', (tester) async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];
      await provider.addItems(items);

      await tester.pumpWidget(createSummaryWidget());
      await tester.pump(); // Allow provider to update

      expect(find.text('2'), findsOneWidget); // Unread count badge
    });

    testWidgets('should handle tap callback', (tester) async {
      bool tapped = false;

      await tester.pumpWidget(createSummaryWidget(onTap: () {
        tapped = true;
      }));

      await tester.tap(find.byType(NotificationTraySummaryWidget));
      await tester.pump();

      expect(tapped, true);
    });
  });
}

/// Helper function to create test notification tray items
NotificationTrayItem _createTestItem(
  String id,
  String stationCode,
  String coachNumber,
  int onboardingCount,
  int offboardingCount,
  int vacantCount,
) {
  return NotificationTrayItem(
    id: id,
    stationCode: stationCode,
    coachNumber: coachNumber,
    onboardingCount: onboardingCount,
    offboardingCount: offboardingCount,
    vacantCount: vacantCount,
    timestamp: DateTime.now(),
    trainNumber: '12391',
    date: '2025-06-02',
    isRead: false,
  );
}
