import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/models/notification_tray_model.dart';

void main() {
  group('NotificationTrayProvider', () {
    late NotificationTrayProvider provider;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      provider = NotificationTrayProvider();
      await provider.initialize();
    });

    test('should initialize with empty state', () {
      expect(provider.items, isEmpty);
      expect(provider.unreadCount, 0);
      expect(provider.isLoading, false);
      expect(provider.lastUpdateTime, isNull);
    });

    test('should add items correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];

      await provider.addItems(items);

      expect(provider.items.length, 2);
      expect(provider.unreadCount, 2);
      expect(provider.lastUpdateTime, isNotNull);
    });

    test('should prevent duplicate items', () async {
      final timestamp = DateTime.now();
      final item1 = _createTestItemWithTimestamp(
          'test_1', 'DDU', 'A1', 5, 3, 2, timestamp);
      final item2 = _createTestItemWithTimestamp(
          'test_1', 'DDU', 'A1', 5, 3, 2, timestamp);

      // Verify items are identical
      expect(item1.id, item2.id);
      expect(item1.stationCode, item2.stationCode);
      expect(item1.coachNumber, item2.coachNumber);
      expect(item1.timestamp.millisecondsSinceEpoch,
          item2.timestamp.millisecondsSinceEpoch);

      final items = [item1, item2];

      await provider.addItems(items);

      expect(provider.items.length, 1); // Only one should be added
    });

    test('should mark item as read correctly', () async {
      final item = _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2);
      await provider.addItems([item]);

      expect(provider.unreadCount, 1);

      await provider.markAsRead('test_1');

      expect(provider.unreadCount, 0);
      expect(provider.items.first.isRead, true);
    });

    test('should mark all items as read correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await provider.addItems(items);
      expect(provider.unreadCount, 3);

      await provider.markAllAsRead();

      expect(provider.unreadCount, 0);
      expect(provider.items.every((item) => item.isRead), true);
    });

    test('should mark station items as read correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await provider.addItems(items);
      expect(provider.unreadCount, 3);

      await provider.markStationAsRead('DDU');

      expect(provider.unreadCount, 1); // Only BSB item should remain unread

      final dduItems =
          provider.items.where((item) => item.stationCode == 'DDU');
      expect(dduItems.every((item) => item.isRead), true);

      final bsbItems =
          provider.items.where((item) => item.stationCode == 'BSB');
      expect(bsbItems.every((item) => !item.isRead), true);
    });

    test('should clear all items correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];

      await provider.addItems(items);
      expect(provider.items.length, 2);

      await provider.clearAll();

      expect(provider.items, isEmpty);
      expect(provider.unreadCount, 0);
      expect(provider.lastUpdateTime, isNull);
    });

    test('should group items by station correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await provider.addItems(items);

      final groupedItems = provider.itemsByStation;

      expect(groupedItems.keys.length, 2); // DDU and BSB
      expect(groupedItems['DDU']!.items.length, 2);
      expect(groupedItems['BSB']!.items.length, 1);

      expect(groupedItems['DDU']!.totalOnboardingCount, 11); // 5 + 6
      expect(groupedItems['DDU']!.totalOffboardingCount, 6); // 3 + 3
      expect(groupedItems['DDU']!.totalVacantCount, 8); // 2 + 6
    });

    test('should calculate summary correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await provider.addItems(items);

      final summary = provider.summary;

      expect(summary.totalUnreadCount, 3);
      expect(summary.totalOnboardingCount, 19); // 5 + 6 + 8
      expect(summary.totalOffboardingCount, 8); // 3 + 3 + 2
      expect(summary.totalVacantCount, 9); // 2 + 6 + 1
      expect(summary.activeStations, containsAll(['DDU', 'BSB']));
      expect(summary.activeCoaches, containsAll(['A1', 'B3']));
      expect(summary.hasActiveNotifications, true);
    });

    test('should filter items correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await provider.addItems(items);

      // Test filtering by train
      final trainItems = provider.getItemsForTrain('12391', '2025-06-02');
      expect(trainItems.length, 3);

      // Test filtering by station
      final stationItems = provider.getItemsForStation('DDU');
      expect(stationItems.length, 2);

      // Test filtering by coach
      final coachItems = provider.getItemsForCoach('A1');
      expect(coachItems.length, 2);
    });

    test('should remove old items correctly', () async {
      final oldTimestamp = DateTime.now().subtract(const Duration(days: 10));
      final recentTimestamp = DateTime.now();

      final items = [
        _createTestItemWithTimestamp(
            'test_1', 'DDU', 'A1', 5, 3, 2, oldTimestamp),
        _createTestItemWithTimestamp(
            'test_2', 'DDU', 'B3', 6, 3, 6, recentTimestamp),
      ];

      await provider.addItems(items);
      expect(provider.items.length, 2);

      await provider.removeOldItems(maxAge: const Duration(days: 7));

      expect(provider.items.length, 1);
      expect(provider.items.first.id, 'test_2');
    });

    test('should handle empty state correctly', () {
      final summary = provider.summary;

      expect(summary.totalUnreadCount, 0);
      expect(summary.hasActiveNotifications, false);
      expect(provider.itemsByStation, isEmpty);
      expect(provider.unreadItems, isEmpty);
    });

    test('should persist and load data correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];

      await provider.addItems(items);
      expect(provider.items.length, 2);

      // Create new provider instance to test loading
      final newProvider = NotificationTrayProvider();
      await newProvider.initialize();

      expect(newProvider.items.length, 2);
      expect(newProvider.items.first.stationCode, 'DDU');
    });
  });
}

/// Helper function to create test notification tray items
NotificationTrayItem _createTestItem(
  String id,
  String stationCode,
  String coachNumber,
  int onboardingCount,
  int offboardingCount,
  int vacantCount,
) {
  return NotificationTrayItem(
    id: id,
    stationCode: stationCode,
    coachNumber: coachNumber,
    onboardingCount: onboardingCount,
    offboardingCount: offboardingCount,
    vacantCount: vacantCount,
    timestamp: DateTime.now(),
    trainNumber: '12391',
    date: '2025-06-02',
    isRead: false,
  );
}

/// Helper function to create test notification tray items with specific timestamp
NotificationTrayItem _createTestItemWithTimestamp(
  String id,
  String stationCode,
  String coachNumber,
  int onboardingCount,
  int offboardingCount,
  int vacantCount,
  DateTime timestamp,
) {
  return NotificationTrayItem(
    id: id,
    stationCode: stationCode,
    coachNumber: coachNumber,
    onboardingCount: onboardingCount,
    offboardingCount: offboardingCount,
    vacantCount: vacantCount,
    timestamp: timestamp,
    trainNumber: '12391',
    date: '2025-06-02',
    isRead: false,
  );
}
