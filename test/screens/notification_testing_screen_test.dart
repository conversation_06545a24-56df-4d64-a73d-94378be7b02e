import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:railops/screens/notification_testing/notification_testing_screen.dart';

void main() {
  group('NotificationTestingScreen', () {
    testWidgets('should render all test categories', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Verify the screen renders
      expect(find.text('Notification Testing'), findsOneWidget);
      
      // Verify test configuration section
      expect(find.text('Test Configuration'), findsOneWidget);
      expect(find.text('User Token'), findsOneWidget);
      expect(find.text('Latitude'), findsOneWidget);
      expect(find.text('Longitude'), findsOneWidget);
      
      // Verify API integration section
      expect(find.text('API Integration Tests'), findsOneWidget);
      expect(find.text('Test Real API Call'), findsOneWidget);
      
      // Verify Phase 1 tests section
      expect(find.text('Phase 1 Notification Types'), findsOneWidget);
      expect(find.text('Boarding Alert'), findsOneWidget);
      expect(find.text('Off-boarding Alert'), findsOneWidget);
      expect(find.text('Station Approaching'), findsOneWidget);
      expect(find.text('Coach Reminder'), findsOneWidget);
      expect(find.text('Berth Reminder'), findsOneWidget);
      
      // Verify Phase 2 tests section
      expect(find.text('Phase 2 Enhanced Types'), findsOneWidget);
      expect(find.text('Proximity Alert'), findsOneWidget);
      expect(find.text('Station Approach Alert'), findsOneWidget);
      expect(find.text('Train Status Update'), findsOneWidget);
      expect(find.text('Boarding Count Update'), findsOneWidget);
      
      // Verify Quick tests section
      expect(find.text('Quick Tests'), findsOneWidget);
      expect(find.text('Quick Test (Phase 1 + 2)'), findsOneWidget);
      expect(find.text('Run Full Test Suite'), findsOneWidget);
      
      // Verify Test results section
      expect(find.text('Test Results'), findsOneWidget);
      expect(find.text('No tests run yet. Click any test button above to see results.'), findsOneWidget);
    });

    testWidgets('should have default coordinates set', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Check that default coordinates are set (New Delhi)
      final latField = find.widgetWithText(TextField, '28.6139');
      final lngField = find.widgetWithText(TextField, '77.2090');
      
      expect(latField, findsOneWidget);
      expect(lngField, findsOneWidget);
    });

    testWidgets('should show loading state when test is running', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Initially, buttons should be enabled
      final testButton = find.text('Test Real API Call');
      expect(testButton, findsOneWidget);
      
      // The button should be enabled initially
      final elevatedButton = tester.widget<ElevatedButton>(
        find.ancestor(
          of: testButton,
          matching: find.byType(ElevatedButton),
        ),
      );
      expect(elevatedButton.onPressed, isNotNull);
    });

    testWidgets('should allow text input in configuration fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Find the token input field
      final tokenField = find.widgetWithText(TextField, '').first;
      
      // Enter text in the token field
      await tester.enterText(tokenField, 'test_token_123');
      await tester.pump();

      // Verify the text was entered
      expect(find.text('test_token_123'), findsOneWidget);
    });

    testWidgets('should display helpful information text', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Check for helpful information text
      expect(find.text('Default coordinates: New Delhi (28.6139, 77.2090)'), findsOneWidget);
      expect(find.textContaining('Calls https://railops-uat-api.biputri.com'), findsOneWidget);
    });

    testWidgets('should have proper button styling and icons', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Check for various icons used in buttons
      expect(find.byIcon(Icons.api), findsOneWidget);
      expect(find.byIcon(Icons.train), findsOneWidget);
      expect(find.byIcon(Icons.exit_to_app), findsOneWidget);
      expect(find.byIcon(Icons.location_on), findsOneWidget);
      expect(find.byIcon(Icons.directions_railway), findsOneWidget);
      expect(find.byIcon(Icons.bed), findsOneWidget);
      expect(find.byIcon(Icons.near_me), findsOneWidget);
      expect(find.byIcon(Icons.schedule), findsOneWidget);
      expect(find.byIcon(Icons.info), findsOneWidget);
      expect(find.byIcon(Icons.people), findsOneWidget);
      expect(find.byIcon(Icons.flash_on), findsOneWidget);
      expect(find.byIcon(Icons.playlist_play), findsOneWidget);
    });
  });

  group('NotificationTestingScreen Integration', () {
    testWidgets('should integrate with existing notification system', (WidgetTester tester) async {
      // This test verifies that the screen can be instantiated without errors
      // and that it properly integrates with the existing notification infrastructure
      
      await tester.pumpWidget(
        const MaterialApp(
          home: NotificationTestingScreen(),
        ),
      );

      // Verify that the screen loads without throwing exceptions
      expect(find.byType(NotificationTestingScreen), findsOneWidget);
      
      // Verify that all major UI components are present
      expect(find.byType(Card), findsAtLeastNWidgets(5)); // Configuration, API, Phase1, Phase2, Quick tests, Results
      expect(find.byType(ElevatedButton), findsAtLeastNWidgets(10)); // All test buttons
      expect(find.byType(TextField), findsNWidgets(3)); // Token, lat, lng fields
    });
  });
}
