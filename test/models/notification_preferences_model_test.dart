import 'package:flutter_test/flutter_test.dart';
import 'package:railops/models/notification_preferences_model.dart';

void main() {
  group('NotificationPreferencesModel', () {
    test('should create default preferences', () {
      final preferences = NotificationPreferencesModel.defaultPreferences();
      
      expect(preferences.enableOnboardingNotifications, true);
      expect(preferences.enableStationApproachNotifications, false);
      expect(preferences.enableBoardingAlerts, true);
      expect(preferences.enableOffBoardingAlerts, true);
      expect(preferences.enableProximityAlerts, true);
      expect(preferences.advanceNoticeMinutes, 5);
      expect(preferences.proximityThresholdKm, 2);
      expect(preferences.enabledCoachTypes, ["AC1", "AC2", "AC3", "SL", "CC", "2S"]);
      expect(preferences.enableCoachSpecificFiltering, false);
      expect(preferences.enableSound, true);
      expect(preferences.enableVibration, true);
      expect(preferences.enableBackgroundNotifications, true);
      expect(preferences.enableLocationBasedNotifications, true);
      expect(preferences.maxNotificationsPerHour, 10);
    });

    test('should serialize to and from JSON correctly', () {
      final originalPreferences = NotificationPreferencesModel(
        enableOnboardingNotifications: false,
        enableStationApproachNotifications: true,
        enableBoardingAlerts: false,
        enableOffBoardingAlerts: true,
        enableProximityAlerts: false,
        advanceNoticeMinutes: 15,
        proximityThresholdKm: 5,
        enabledCoachTypes: ["AC1", "AC2"],
        enableCoachSpecificFiltering: true,
        enableSound: false,
        enableVibration: true,
        notificationTone: 'custom',
        enableBackgroundNotifications: false,
        enableLocationBasedNotifications: true,
        maxNotificationsPerHour: 20,
      );

      final json = originalPreferences.toJson();
      final deserializedPreferences = NotificationPreferencesModel.fromJson(json);

      expect(deserializedPreferences.enableOnboardingNotifications, false);
      expect(deserializedPreferences.enableStationApproachNotifications, true);
      expect(deserializedPreferences.enableBoardingAlerts, false);
      expect(deserializedPreferences.enableOffBoardingAlerts, true);
      expect(deserializedPreferences.enableProximityAlerts, false);
      expect(deserializedPreferences.advanceNoticeMinutes, 15);
      expect(deserializedPreferences.proximityThresholdKm, 5);
      expect(deserializedPreferences.enabledCoachTypes, ["AC1", "AC2"]);
      expect(deserializedPreferences.enableCoachSpecificFiltering, true);
      expect(deserializedPreferences.enableSound, false);
      expect(deserializedPreferences.enableVibration, true);
      expect(deserializedPreferences.notificationTone, 'custom');
      expect(deserializedPreferences.enableBackgroundNotifications, false);
      expect(deserializedPreferences.enableLocationBasedNotifications, true);
      expect(deserializedPreferences.maxNotificationsPerHour, 20);
    });

    test('should handle missing JSON fields with defaults', () {
      final json = <String, dynamic>{
        'enableOnboardingNotifications': false,
        'advanceNoticeMinutes': 10,
        // Missing other fields should use defaults
      };

      final preferences = NotificationPreferencesModel.fromJson(json);

      expect(preferences.enableOnboardingNotifications, false);
      expect(preferences.advanceNoticeMinutes, 10);
      expect(preferences.enableStationApproachNotifications, false); // default
      expect(preferences.enableBoardingAlerts, true); // default
      expect(preferences.enableSound, true); // default
    });

    test('should create copy with modified values', () {
      final original = NotificationPreferencesModel.defaultPreferences();
      final modified = original.copyWith(
        enableOnboardingNotifications: false,
        advanceNoticeMinutes: 20,
        enabledCoachTypes: ["AC1"],
      );

      expect(modified.enableOnboardingNotifications, false);
      expect(modified.advanceNoticeMinutes, 20);
      expect(modified.enabledCoachTypes, ["AC1"]);
      
      // Other values should remain unchanged
      expect(modified.enableBoardingAlerts, original.enableBoardingAlerts);
      expect(modified.enableSound, original.enableSound);
      expect(modified.proximityThresholdKm, original.proximityThresholdKm);
    });

    test('should validate preferences correctly', () {
      // Valid preferences
      final validPreferences = NotificationPreferencesModel(
        advanceNoticeMinutes: 30,
        proximityThresholdKm: 5,
        maxNotificationsPerHour: 15,
      );
      expect(validPreferences.isValid, true);

      // Invalid advance notice (too low)
      final invalidAdvanceNotice = NotificationPreferencesModel(
        advanceNoticeMinutes: 0,
        proximityThresholdKm: 5,
        maxNotificationsPerHour: 15,
      );
      expect(invalidAdvanceNotice.isValid, false);

      // Invalid proximity threshold (too high)
      final invalidProximity = NotificationPreferencesModel(
        advanceNoticeMinutes: 30,
        proximityThresholdKm: 100,
        maxNotificationsPerHour: 15,
      );
      expect(invalidProximity.isValid, false);

      // Invalid max notifications (too high)
      final invalidMaxNotifications = NotificationPreferencesModel(
        advanceNoticeMinutes: 30,
        proximityThresholdKm: 5,
        maxNotificationsPerHour: 200,
      );
      expect(invalidMaxNotifications.isValid, false);
    });

    test('should check notification type permissions correctly', () {
      final preferences = NotificationPreferencesModel(
        enableOnboardingNotifications: true,
        enableStationApproachNotifications: false,
        enableBoardingAlerts: true,
        enableOffBoardingAlerts: false,
        enableProximityAlerts: true,
        enableCoachSpecificFiltering: true,
      );

      expect(preferences.shouldShowNotificationType(NotificationType.general), true);
      expect(preferences.shouldShowNotificationType(NotificationType.onboarding), true);
      expect(preferences.shouldShowNotificationType(NotificationType.stationApproach), false);
      expect(preferences.shouldShowNotificationType(NotificationType.boardingAlert), true);
      expect(preferences.shouldShowNotificationType(NotificationType.offBoardingAlert), false);
      expect(preferences.shouldShowNotificationType(NotificationType.proximityAlert), true);
      expect(preferences.shouldShowNotificationType(NotificationType.coachSpecific), true);

      // When onboarding notifications are disabled, all onboarding types should be false
      final disabledPreferences = preferences.copyWith(enableOnboardingNotifications: false);
      expect(disabledPreferences.shouldShowNotificationType(NotificationType.onboarding), false);
      expect(disabledPreferences.shouldShowNotificationType(NotificationType.boardingAlert), false);
      expect(disabledPreferences.shouldShowNotificationType(NotificationType.proximityAlert), false);
    });

    test('should check coach type filtering correctly', () {
      final preferences = NotificationPreferencesModel(
        enableCoachSpecificFiltering: true,
        enabledCoachTypes: ["AC1", "AC2", "SL"],
      );

      expect(preferences.shouldShowForCoachType("AC1"), true);
      expect(preferences.shouldShowForCoachType("ac1"), true); // case insensitive
      expect(preferences.shouldShowForCoachType("AC2"), true);
      expect(preferences.shouldShowForCoachType("SL"), true);
      expect(preferences.shouldShowForCoachType("CC"), false);
      expect(preferences.shouldShowForCoachType("2S"), false);

      // When filtering is disabled, all coach types should be allowed
      final noFilteringPreferences = preferences.copyWith(enableCoachSpecificFiltering: false);
      expect(noFilteringPreferences.shouldShowForCoachType("CC"), true);
      expect(noFilteringPreferences.shouldShowForCoachType("2S"), true);
      expect(noFilteringPreferences.shouldShowForCoachType("UNKNOWN"), true);
    });

    test('should implement equality correctly', () {
      final preferences1 = NotificationPreferencesModel(
        enableOnboardingNotifications: true,
        advanceNoticeMinutes: 10,
        enabledCoachTypes: ["AC1", "AC2"],
      );

      final preferences2 = NotificationPreferencesModel(
        enableOnboardingNotifications: true,
        advanceNoticeMinutes: 10,
        enabledCoachTypes: ["AC1", "AC2"],
      );

      final preferences3 = NotificationPreferencesModel(
        enableOnboardingNotifications: false,
        advanceNoticeMinutes: 10,
        enabledCoachTypes: ["AC1", "AC2"],
      );

      expect(preferences1 == preferences2, true);
      expect(preferences1 == preferences3, false);
      expect(preferences1.hashCode == preferences2.hashCode, true);
      expect(preferences1.hashCode == preferences3.hashCode, false);
    });

    test('should have meaningful toString representation', () {
      final preferences = NotificationPreferencesModel(
        enableOnboardingNotifications: true,
        advanceNoticeMinutes: 15,
        enabledCoachTypes: ["AC1", "SL"],
      );

      final stringRepresentation = preferences.toString();
      expect(stringRepresentation, contains('enableOnboarding: true'));
      expect(stringRepresentation, contains('advanceNotice: 15min'));
      expect(stringRepresentation, contains('[AC1, SL]'));
    });
  });

  group('NotificationType Extension', () {
    test('should convert to string correctly', () {
      expect(NotificationType.general.value, 'general');
      expect(NotificationType.onboarding.value, 'onboarding');
      expect(NotificationType.stationApproach.value, 'station_approach');
      expect(NotificationType.boardingAlert.value, 'boarding_alert');
      expect(NotificationType.offBoardingAlert.value, 'off_boarding_alert');
      expect(NotificationType.proximityAlert.value, 'proximity_alert');
      expect(NotificationType.trainStatus.value, 'train_status');
      expect(NotificationType.coachSpecific.value, 'coach_specific');
    });

    test('should convert from string correctly', () {
      expect(NotificationTypeExtension.fromString('general'), NotificationType.general);
      expect(NotificationTypeExtension.fromString('onboarding'), NotificationType.onboarding);
      expect(NotificationTypeExtension.fromString('station_approach'), NotificationType.stationApproach);
      expect(NotificationTypeExtension.fromString('boarding_alert'), NotificationType.boardingAlert);
      expect(NotificationTypeExtension.fromString('off_boarding_alert'), NotificationType.offBoardingAlert);
      expect(NotificationTypeExtension.fromString('proximity_alert'), NotificationType.proximityAlert);
      expect(NotificationTypeExtension.fromString('train_status'), NotificationType.trainStatus);
      expect(NotificationTypeExtension.fromString('coach_specific'), NotificationType.coachSpecific);
      
      // Unknown values should default to general
      expect(NotificationTypeExtension.fromString('unknown'), NotificationType.general);
      expect(NotificationTypeExtension.fromString(''), NotificationType.general);
    });
  });
}
