import 'package:flutter_test/flutter_test.dart';
import 'package:railops/models/notification_tray_model.dart';

void main() {
  group('NotificationTrayItem', () {
    test('should create notification tray item with correct properties', () {
      final timestamp = DateTime.now();
      final item = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 5,
        offboardingCount: 3,
        vacantCount: 2,
        timestamp: timestamp,
        trainNumber: '12391',
        date: '2025-06-02',
        isRead: false,
      );

      expect(item.id, 'test_id');
      expect(item.stationCode, 'DDU');
      expect(item.coachNumber, 'A1');
      expect(item.onboardingCount, 5);
      expect(item.offboardingCount, 3);
      expect(item.vacantCount, 2);
      expect(item.timestamp, timestamp);
      expect(item.trainNumber, '12391');
      expect(item.date, '2025-06-02');
      expect(item.isRead, false);
    });

    test('should calculate total passenger count correctly', () {
      final item = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 5,
        offboardingCount: 3,
        vacantCount: 2,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      expect(item.totalPassengerCount, 8); // 5 + 3
    });

    test('should detect activity correctly', () {
      final activeItem = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 5,
        offboardingCount: 0,
        vacantCount: 0,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final inactiveItem = NotificationTrayItem(
        id: 'test_id_2',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 0,
        offboardingCount: 0,
        vacantCount: 0,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      expect(activeItem.hasActivity, true);
      expect(inactiveItem.hasActivity, false);
    });

    test('should determine primary type correctly', () {
      final onboardingItem = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 10,
        offboardingCount: 3,
        vacantCount: 2,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final offboardingItem = NotificationTrayItem(
        id: 'test_id_2',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 2,
        offboardingCount: 10,
        vacantCount: 3,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      final vacantItem = NotificationTrayItem(
        id: 'test_id_3',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 1,
        offboardingCount: 2,
        vacantCount: 10,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
      );

      expect(onboardingItem.primaryType, NotificationTrayItemType.onboarding);
      expect(offboardingItem.primaryType, NotificationTrayItemType.offboarding);
      expect(vacantItem.primaryType, NotificationTrayItemType.vacant);
    });

    test('should create copy with updated properties', () {
      final original = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 5,
        offboardingCount: 3,
        vacantCount: 2,
        timestamp: DateTime.now(),
        trainNumber: '12391',
        date: '2025-06-02',
        isRead: false,
      );

      final updated = original.copyWith(isRead: true, onboardingCount: 10);

      expect(updated.isRead, true);
      expect(updated.onboardingCount, 10);
      expect(updated.stationCode, 'DDU'); // Unchanged
      expect(updated.coachNumber, 'A1'); // Unchanged
    });

    test('should serialize to and from JSON correctly', () {
      final timestamp = DateTime.now();
      final original = NotificationTrayItem(
        id: 'test_id',
        stationCode: 'DDU',
        coachNumber: 'A1',
        onboardingCount: 5,
        offboardingCount: 3,
        vacantCount: 2,
        timestamp: timestamp,
        trainNumber: '12391',
        date: '2025-06-02',
        isRead: true,
      );

      final json = original.toJson();
      final restored = NotificationTrayItem.fromJson(json);

      expect(restored.id, original.id);
      expect(restored.stationCode, original.stationCode);
      expect(restored.coachNumber, original.coachNumber);
      expect(restored.onboardingCount, original.onboardingCount);
      expect(restored.offboardingCount, original.offboardingCount);
      expect(restored.vacantCount, original.vacantCount);
      expect(restored.timestamp.millisecondsSinceEpoch, 
             original.timestamp.millisecondsSinceEpoch);
      expect(restored.trainNumber, original.trainNumber);
      expect(restored.date, original.date);
      expect(restored.isRead, original.isRead);
    });
  });

  group('StationNotificationGroup', () {
    test('should calculate totals correctly', () {
      final items = [
        NotificationTrayItem(
          id: 'test_1',
          stationCode: 'DDU',
          coachNumber: 'A1',
          onboardingCount: 5,
          offboardingCount: 3,
          vacantCount: 2,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
        ),
        NotificationTrayItem(
          id: 'test_2',
          stationCode: 'DDU',
          coachNumber: 'B3',
          onboardingCount: 6,
          offboardingCount: 3,
          vacantCount: 6,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
        ),
      ];

      final group = StationNotificationGroup(
        stationCode: 'DDU',
        items: items,
        latestTimestamp: DateTime.now(),
      );

      expect(group.totalOnboardingCount, 11); // 5 + 6
      expect(group.totalOffboardingCount, 6); // 3 + 3
      expect(group.totalVacantCount, 8); // 2 + 6
    });

    test('should calculate unread count correctly', () {
      final items = [
        NotificationTrayItem(
          id: 'test_1',
          stationCode: 'DDU',
          coachNumber: 'A1',
          onboardingCount: 5,
          offboardingCount: 3,
          vacantCount: 2,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
          isRead: false,
        ),
        NotificationTrayItem(
          id: 'test_2',
          stationCode: 'DDU',
          coachNumber: 'B3',
          onboardingCount: 6,
          offboardingCount: 3,
          vacantCount: 6,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
          isRead: true,
        ),
      ];

      final group = StationNotificationGroup(
        stationCode: 'DDU',
        items: items,
        latestTimestamp: DateTime.now(),
      );

      expect(group.unreadCount, 1);
      expect(group.hasUnreadItems, true);
    });

    test('should get coach numbers correctly', () {
      final items = [
        NotificationTrayItem(
          id: 'test_1',
          stationCode: 'DDU',
          coachNumber: 'A1',
          onboardingCount: 5,
          offboardingCount: 3,
          vacantCount: 2,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
        ),
        NotificationTrayItem(
          id: 'test_2',
          stationCode: 'DDU',
          coachNumber: 'B3',
          onboardingCount: 6,
          offboardingCount: 3,
          vacantCount: 6,
          timestamp: DateTime.now(),
          trainNumber: '12391',
          date: '2025-06-02',
        ),
      ];

      final group = StationNotificationGroup(
        stationCode: 'DDU',
        items: items,
        latestTimestamp: DateTime.now(),
      );

      expect(group.coachNumbers, ['A1', 'B3']);
    });
  });

  group('NotificationTraySummary', () {
    test('should create empty summary correctly', () {
      final summary = NotificationTraySummary.empty();

      expect(summary.totalUnreadCount, 0);
      expect(summary.totalOnboardingCount, 0);
      expect(summary.totalOffboardingCount, 0);
      expect(summary.totalVacantCount, 0);
      expect(summary.activeStations, isEmpty);
      expect(summary.activeCoaches, isEmpty);
      expect(summary.hasActiveNotifications, false);
      expect(summary.totalPassengerActivity, 0);
    });

    test('should calculate passenger activity correctly', () {
      final summary = NotificationTraySummary(
        totalUnreadCount: 2,
        totalOnboardingCount: 10,
        totalOffboardingCount: 5,
        totalVacantCount: 3,
        activeStations: ['DDU', 'BSB'],
        activeCoaches: ['A1', 'B3'],
      );

      expect(summary.hasActiveNotifications, true);
      expect(summary.totalPassengerActivity, 15); // 10 + 5
    });
  });

  group('NotificationTrayItemFactory', () {
    test('should create items from mock onboarding response', () {
      // Mock response data structure
      final mockResponse = MockOnboardingResponse(
        stations: ['DDU', 'BSB'],
        details: {
          'DDU': {
            'A1': [1, 2, 3, 4, 5], // 5 passengers
            'B3': [1, 2, 3, 4, 5, 6], // 6 passengers
          },
          'BSB': {
            'A1': [1, 2, 3, 4, 5, 6, 7, 8], // 8 passengers
          },
        },
        detailsOffBoarding: {
          'DDU': {
            'A1': [1, 2, 3], // 3 passengers
            'B3': [1, 2, 3], // 3 passengers
          },
          'BSB': {
            'A1': [1, 2], // 2 passengers
          },
        },
        detailsVacant: {
          'DDU': {
            'A1': [1, 2], // 2 vacant
            'B3': [1, 2, 3, 4, 5, 6], // 6 vacant
          },
          'BSB': {
            'A1': [1], // 1 vacant
          },
        },
      );

      final items = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      expect(items.length, 3); // DDU-A1, DDU-B3, BSB-A1

      // Check DDU-A1
      final dduA1 = items.firstWhere((item) => 
          item.stationCode == 'DDU' && item.coachNumber == 'A1');
      expect(dduA1.onboardingCount, 5);
      expect(dduA1.offboardingCount, 3);
      expect(dduA1.vacantCount, 2);

      // Check DDU-B3
      final dduB3 = items.firstWhere((item) => 
          item.stationCode == 'DDU' && item.coachNumber == 'B3');
      expect(dduB3.onboardingCount, 6);
      expect(dduB3.offboardingCount, 3);
      expect(dduB3.vacantCount, 6);

      // Check BSB-A1
      final bsbA1 = items.firstWhere((item) => 
          item.stationCode == 'BSB' && item.coachNumber == 'A1');
      expect(bsbA1.onboardingCount, 8);
      expect(bsbA1.offboardingCount, 2);
      expect(bsbA1.vacantCount, 1);
    });

    test('should handle empty response correctly', () {
      final mockResponse = MockOnboardingResponse(
        stations: [],
        details: {},
        detailsOffBoarding: {},
        detailsVacant: {},
      );

      final items = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      expect(items, isEmpty);
    });
  });
}

/// Mock class for testing NotificationTrayItemFactory
class MockOnboardingResponse {
  final List<String>? stations;
  final Map<String, Map<String, List<int>>>? details;
  final Map<String, Map<String, List<int>>>? detailsOffBoarding;
  final Map<String, Map<String, List<int>>>? detailsVacant;

  MockOnboardingResponse({
    this.stations,
    this.details,
    this.detailsOffBoarding,
    this.detailsVacant,
  });
}
