import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_location_integration_service.dart';
import 'package:railops/providers/notification_tray_provider.dart';
import 'package:railops/models/notification_tray_model.dart';
import 'package:railops/services/attendance_services/location_service.dart';

void main() {
  group('Notification Tray Integration Tests', () {
    late NotificationTrayProvider trayProvider;

    setUp(() async {
      // Mock SharedPreferences
      SharedPreferences.setMockInitialValues({});
      trayProvider = NotificationTrayProvider();
      await trayProvider.initialize();
    });

    test('should complete full notification flow from API to tray', () async {
      // Test the complete flow:
      // 1. Mock API response
      // 2. Create notification tray items
      // 3. Add to provider
      // 4. Verify data integrity

      // Step 1: Create mock API response
      final mockResponse = MockOnboardingResponse(
        trainNumber: '12391',
        date: '2025-06-02',
        message: 'Success',
        stations: ['DDU', 'BSB'],
        details: {
          'DDU': {
            'A1': [1, 2, 3, 4, 5], // 5 passengers
            'B3': [1, 2, 3, 4, 5, 6], // 6 passengers
          },
          'BSB': {
            'A1': [1, 2, 3, 4, 5, 6, 7, 8], // 8 passengers
          },
        },
        detailsOffBoarding: {
          'DDU': {
            'A1': [1, 2, 3], // 3 passengers
            'B3': [1, 2, 3], // 3 passengers
          },
          'BSB': {
            'A1': [1, 2], // 2 passengers
          },
        },
        detailsVacant: {
          'DDU': {
            'A1': [1, 2], // 2 vacant
            'B3': [1, 2, 3, 4, 5, 6], // 6 vacant
          },
          'BSB': {
            'A1': [1], // 1 vacant
          },
        },
      );

      // Step 2: Create notification tray items from response
      final trayItems = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );

      // Verify items were created correctly
      expect(trayItems.length, 3); // DDU-A1, DDU-B3, BSB-A1

      // Step 3: Add items to provider
      await trayProvider.addItems(trayItems);

      // Step 4: Verify data integrity
      expect(trayProvider.items.length, 3);
      expect(trayProvider.unreadCount, 3);

      // Verify summary calculations
      final summary = trayProvider.summary;
      expect(summary.totalOnboardingCount, 19); // 5 + 6 + 8
      expect(summary.totalOffboardingCount, 8); // 3 + 3 + 2
      expect(summary.totalVacantCount, 9); // 2 + 6 + 1
      expect(summary.activeStations, containsAll(['DDU', 'BSB']));
      expect(summary.activeCoaches, containsAll(['A1', 'B3']));

      // Verify specific item data
      final dduA1 = trayItems.firstWhere((item) => 
          item.stationCode == 'DDU' && item.coachNumber == 'A1');
      expect(dduA1.onboardingCount, 5);
      expect(dduA1.offboardingCount, 3);
      expect(dduA1.vacantCount, 2);
      expect(dduA1.trainNumber, '12391');
      expect(dduA1.date, '2025-06-02');
    });

    test('should handle notification state management correctly', () async {
      // Create test items
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
      ];

      await trayProvider.addItems(items);

      // Test initial state
      expect(trayProvider.unreadCount, 3);
      expect(trayProvider.items.every((item) => !item.isRead), true);

      // Test mark single item as read
      await trayProvider.markAsRead('test_1');
      expect(trayProvider.unreadCount, 2);
      expect(trayProvider.items.firstWhere((item) => item.id == 'test_1').isRead, true);

      // Test mark station as read
      await trayProvider.markStationAsRead('DDU');
      expect(trayProvider.unreadCount, 1); // Only BSB item should remain unread

      // Test mark all as read
      await trayProvider.markAllAsRead();
      expect(trayProvider.unreadCount, 0);
      expect(trayProvider.items.every((item) => item.isRead), true);
    });

    test('should handle data persistence correctly', () async {
      // Add items to first provider instance
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
      ];

      await trayProvider.addItems(items);
      expect(trayProvider.items.length, 2);

      // Create new provider instance to test persistence
      final newProvider = NotificationTrayProvider();
      await newProvider.initialize();

      // Data should be loaded from storage
      expect(newProvider.items.length, 2);
      expect(newProvider.items.first.stationCode, 'DDU');
      expect(newProvider.summary.totalOnboardingCount, 11); // 5 + 6
    });

    test('should handle filtering and grouping correctly', () async {
      final items = [
        _createTestItem('test_1', 'DDU', 'A1', 5, 3, 2),
        _createTestItem('test_2', 'DDU', 'B3', 6, 3, 6),
        _createTestItem('test_3', 'BSB', 'A1', 8, 2, 1),
        _createTestItem('test_4', 'BSB', 'B3', 4, 1, 3),
      ];

      await trayProvider.addItems(items);

      // Test filtering by station
      final dduItems = trayProvider.getItemsForStation('DDU');
      expect(dduItems.length, 2);
      expect(dduItems.every((item) => item.stationCode == 'DDU'), true);

      // Test filtering by coach
      final a1Items = trayProvider.getItemsForCoach('A1');
      expect(a1Items.length, 2);
      expect(a1Items.every((item) => item.coachNumber == 'A1'), true);

      // Test filtering by train
      final trainItems = trayProvider.getItemsForTrain('12391', '2025-06-02');
      expect(trainItems.length, 4);

      // Test grouping by station
      final groupedItems = trayProvider.itemsByStation;
      expect(groupedItems.keys.length, 2); // DDU and BSB
      expect(groupedItems['DDU']!.items.length, 2);
      expect(groupedItems['BSB']!.items.length, 2);

      // Test group totals
      expect(groupedItems['DDU']!.totalOnboardingCount, 11); // 5 + 6
      expect(groupedItems['BSB']!.totalOnboardingCount, 12); // 8 + 4
    });

    test('should handle edge cases correctly', () async {
      // Test empty response
      final emptyItems = NotificationTrayItemFactory.fromOnboardingResponse(
        MockOnboardingResponse(stations: []),
        '12391',
        '2025-06-02',
      );
      expect(emptyItems, isEmpty);

      // Test duplicate prevention
      final timestamp = DateTime.now();
      final duplicateItems = [
        _createTestItemWithTimestamp('test_1', 'DDU', 'A1', 5, 3, 2, timestamp),
        _createTestItemWithTimestamp('test_2', 'DDU', 'A1', 5, 3, 2, timestamp), // Same station, coach, timestamp
      ];

      await trayProvider.addItems(duplicateItems);
      expect(trayProvider.items.length, 1); // Only one should be added

      // Test old item removal
      final oldTimestamp = DateTime.now().subtract(const Duration(days: 10));
      final oldItem = _createTestItemWithTimestamp('old_1', 'DDU', 'A1', 5, 3, 2, oldTimestamp);
      await trayProvider.addItems([oldItem]);

      await trayProvider.removeOldItems(maxAge: const Duration(days: 7));
      expect(trayProvider.items.length, 1); // Old item should be removed, recent one remains
    });

    test('should handle train location service integration', () async {
      // Test service configuration
      expect(TrainLocationNotificationService.proximityThresholdKm, 50.0);
      expect(TrainLocationNotificationService.minNotificationInterval, 
             const Duration(hours: 1));

      // Test service statistics
      final service = TrainLocationNotificationService();
      final stats = service.getNotificationStats();
      expect(stats['proximity_threshold_km'], 50.0);
      expect(stats['min_notification_interval_hours'], 1);
      expect(stats['notified_stations_count'], 0);

      // Test notification history clearing
      service.clearNotificationHistory();
      final clearedStats = service.getNotificationStats();
      expect(clearedStats['notified_stations_count'], 0);
    });

    test('should handle integration service correctly', () async {
      // Test service configuration check
      expect(TrainLocationIntegrationService.isConfigured(), true);

      // Test configuration status
      final configStatus = TrainLocationIntegrationService.getConfigurationStatus();
      expect(configStatus['firebase_initialized'], true);
      expect(configStatus['location_permissions'], true);
      expect(configStatus['notification_permissions'], true);
      expect(configStatus['api_connectivity'], true);

      // Test service statistics
      final serviceStats = TrainLocationIntegrationService.getServiceStats();
      expect(serviceStats['service_name'], 'TrainLocationIntegrationService');
      expect(serviceStats['location_service_stats'], isA<Map<String, dynamic>>());
    });

    test('should handle complete user workflow', () async {
      // Simulate complete user workflow:
      // 1. User starts app
      // 2. Train location data is fetched
      // 3. Notifications are created and displayed
      // 4. User interacts with notifications
      // 5. Data is persisted

      // Step 1: Initialize (already done in setUp)
      expect(trayProvider.items, isEmpty);

      // Step 2: Simulate train location data fetch
      final mockResponse = MockOnboardingResponse(
        trainNumber: '12391',
        stations: ['DDU'],
        details: {'DDU': {'A1': [1, 2, 3, 4, 5]}},
        detailsOffBoarding: {'DDU': {'A1': [1, 2, 3]}},
        detailsVacant: {'DDU': {'A1': [1, 2]}},
      );

      // Step 3: Create and add notifications
      final items = NotificationTrayItemFactory.fromOnboardingResponse(
        mockResponse,
        '12391',
        '2025-06-02',
      );
      await trayProvider.addItems(items);

      expect(trayProvider.items.length, 1);
      expect(trayProvider.unreadCount, 1);

      // Step 4: User interacts with notifications
      final item = trayProvider.items.first;
      expect(item.stationCode, 'DDU');
      expect(item.coachNumber, 'A1');
      expect(item.onboardingCount, 5);
      expect(item.offboardingCount, 3);
      expect(item.vacantCount, 2);

      // User marks as read
      await trayProvider.markAsRead(item.id);
      expect(trayProvider.unreadCount, 0);

      // Step 5: Data persistence is handled automatically
      // (tested in separate test above)

      // User clears notifications
      await trayProvider.clearAll();
      expect(trayProvider.items, isEmpty);
    });
  });
}

/// Helper functions for testing
NotificationTrayItem _createTestItem(
  String id,
  String stationCode,
  String coachNumber,
  int onboardingCount,
  int offboardingCount,
  int vacantCount,
) {
  return NotificationTrayItem(
    id: id,
    stationCode: stationCode,
    coachNumber: coachNumber,
    onboardingCount: onboardingCount,
    offboardingCount: offboardingCount,
    vacantCount: vacantCount,
    timestamp: DateTime.now(),
    trainNumber: '12391',
    date: '2025-06-02',
    isRead: false,
  );
}

NotificationTrayItem _createTestItemWithTimestamp(
  String id,
  String stationCode,
  String coachNumber,
  int onboardingCount,
  int offboardingCount,
  int vacantCount,
  DateTime timestamp,
) {
  return NotificationTrayItem(
    id: id,
    stationCode: stationCode,
    coachNumber: coachNumber,
    onboardingCount: onboardingCount,
    offboardingCount: offboardingCount,
    vacantCount: vacantCount,
    timestamp: timestamp,
    trainNumber: '12391',
    date: '2025-06-02',
    isRead: false,
  );
}

/// Mock class for testing
class MockOnboardingResponse {
  final String? trainNumber;
  final String? date;
  final String? message;
  final List<String>? stations;
  final Map<String, Map<String, List<int>>>? details;
  final Map<String, Map<String, List<int>>>? detailsOffBoarding;
  final Map<String, Map<String, List<int>>>? detailsVacant;

  MockOnboardingResponse({
    this.trainNumber,
    this.date,
    this.message,
    this.stations,
    this.details,
    this.detailsOffBoarding,
    this.detailsVacant,
  });
}
